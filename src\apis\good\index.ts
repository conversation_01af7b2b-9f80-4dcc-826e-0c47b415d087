/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-21 10:28:56
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-27 13:24:32
 */
import { get, post, put, del, patch } from '../http'
export enum API {
    retrieveProduct = 'gruul-mall-search/search/product',
}

/**
 * 获取所有展示分类
 * @param data
 */
export const getAllCategory = (data: any) => {
    return get({
        url: '/goods-open/manager/show/category/get/all',
        params: data,
    })
}
/**
 * 获取展示分类列表
 * @param data
 */
export const getProList = (data: any) => {
    return get({
        url: '/goods-open/manager/product/list',
        params: data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取分类列表
 */
export const doGetCategory = (params: any) => {
    return get({
        url: 'gruul-mall-goods/goods/product/category',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description:分页获取商品和规格信息
 * @param {any} data
 * @returns {*}
 */
export const doGetProductSkus = (data?: any) => {
    return get({
        url: 'gruul-mall-goods/manager/product/getProductSkus',
        params: data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取select类目选择项
 * @param {string} level
 * @param {string} parentId
 */
export const doGetCategoryLevel = (level: string | number, parentId: string | number) => {
    return get({
        url: `gruul-mall-goods/goods/product/category/by/parent/${parentId}`,
        params: {
            level: `LEVEL_${Number(level) + 1}`,
            size: 1000,
        },
        showLoading: false,
    })
}
/**
 * @LastEditors: lexy
 * @description: 新增分类
 * @param {any} data
 * @returns {*}
 */
export const doNewCategory = (data: any) => {
    return post({
        url: 'gruul-mall-goods/goods/product/category',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 分类排序
 * @param {string} parentId
 */
export const doSortCategory = (ids: string[], parentId: string) => {
    return patch({
        url: 'gruul-mall-goods/goods/product/category',
        data: {
            sortedIds: ids,
            parentId,
        },
        headers: { 'Content-Type': 'application/json' },
        showLoading: false,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新类目
 * @param {any} data
 */
export const doUpdateCategory = (id: string, data: any) => {
    return put({
        url: `gruul-mall-goods/goods/product/category/${id}`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除类目
 * @param {string} id
 */
export const doDelCategory = (id: string) => {
    return del({
        url: `gruul-mall-goods/goods/product/category/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取属性列表
 * @param {any} params
 */
export const getAttsList = (params: any) => {
    return get({
        url: 'gruul-mall-goods/manager/attribute/template/list',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 属性模板删除
 * @param {string} id
 */
export const delAttr = (ids: string) => {
    return del({
        url: `gruul-mall-goods/manager/attribute/template/delete/${ids}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 属性模板子属性删除
 * @param {string} id
 */
export const delAttrSon = (id: string) => {
    return del({
        url: `gruul-mall-goods/manager/attribute/template/delete/child/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 新增属性模板
 * @param {*}
 */
export const addAttrList = (data: any) => {
    return post({
        url: `gruul-mall-goods/manager/attribute/template/save`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新属性模板
 * @param {any} data
 */
export const updateAttrList = (data: any) => {
    return put({
        url: 'gruul-mall-goods/manager/attribute/template/update',
        data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取商品列表
 * @param {any} params
 */
export const doGetCommodity = (params: any) => {
    return get({
        url: 'addon-supplier/supplier/manager/product/list',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 库存中心 获取商品列表
 * @param {any} params
 */
export const doGetInventoryCommodity = (params: any) => {
    return get({
        url: 'addon-supplier/supplier/manager/product/productStock/list',
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取平台类目列表
 */
export const doGetPlatformCategory = () => {
    return get({
        url: 'gruul-mall-addon-platform/platform/shop/signing/category/choosable/list',
    })
}
/**
 * @LastEditors: lexy
 * @description: 发布商品
 * @param {any} data
 */
export const doReleaseGood = (data: any) => {
    return post({
        url: 'addon-supplier/supplier/manager/product/issue',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 编辑商品
 * @param {any} data
 */
export const doUpdateCommodity = (data: any) => {
    return put({
        url: 'addon-supplier/supplier/manager/product/update',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新商品状态
 * @param {string} ids
 * @param {string} status
 * @returns {*}
 */
export const doUpdateSellStatus = (productIds: string[], status: string) => {
    return put({
        url: `addon-supplier/supplier/manager/product/updateStatus/${status}`,
        data: {
            productIds,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除商品
 * @param {string} ids
 */
export const doDeleteCommodity = (ids: any) => {
    return del({
        url: `addon-supplier/supplier/manager/product/delete/${ids}`,
    })
}
/**
 * @LastEditors: lexy 获取单个商品信息
 * @description: 获取单个商品信息
 * @param {string} id
 */
export const doGetSingleCommodity = (id: any, shopId: string) => {
    return get({
        url: `addon-supplier/supplier/manager/product/get/${shopId}/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 设置限购
 * @param {any} data
 * @returns {*}
 */
export const doSetPurchase = (productId: string, data: any) => {
    return post({
        url: `/gruul-mall-storage/storage/product/${productId}/limit`,
        data,
    })
}
export const doSetInventory = (productId: string, data: any) => {
    return post({
        url: `gruul-mall-storage/storage/product/${productId}/stock`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 查询商品规格组与sku列表
 */
export const doGetCommoditySku = (shopId: string, productId: any) => {
    return get({
        url: `gruul-mall-storage/storage/shop/${shopId}/product/${productId}?isSupplier=true`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 查询商品库存与限购
 */
export const doGetProductPurchasing = (productId: string) => {
    return get({
        url: `gruul-mall-storage/storage/product/${productId}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 查询一级分类和分类下的商品数量
 */
export const doGetHighestCategoryLevel = (params) => {
    return get({
        url: `gruul-mall-goods/goods/product/category/categoryLevel1WithProductNum`,
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 通用检索商品接口
 */
export const doGetComRetrieve = (data) => {
    return post({
        url: 'gruul-mall-search/search/product',
        data,
    })
}
/**
 *获取品牌列表
 */
export const getBrandList = (data: any) => {
    return get({
        url: 'gruul-mall-search/search/brand/brandInfo',
        params: data,
    })
}
/*
 * @LastEditors: lexy 商品一键复制
 * @description: 商品一键复制
 * @param {string} id
 */
export const doGetCopyGoods = (params: any) => {
    return get({
        url: `gruul-mall-goods/api/copy/goods/detail`,
        params,
    })
}
/*
 * @LastEditors: lexy 产品 特性列表 I
 * @description: 产品 特性列表 I
 * @param {string} id
 */
export const doGetfeatureList = (params: any) => {
    return get({
        url: `gruul-mall-goods/manager/feature/list`,
        params,
    })
}

/*
 * @LastEditors: lexy 产品 特性新增
 * @description: 产品 特性新增
 * @param {string} data
 */
export const dofeatureSave = (data: any) => {
    return post({
        url: `gruul-mall-goods/manager/feature/save`,
        data,
    })
}

/*
 * @LastEditors: lexy 产品 特性编辑
 * @description: 产品 特性编辑
 * @param {string} data
 */
export const dofeatureUpdate = (data: any) => {
    return post({
        url: `gruul-mall-goods/manager/feature/update`,
        data,
    })
}

/*
 * @LastEditors: lexy 产品 特性删除
 * @description: 产品 特性编辑
 * @param {string} id
 */

export const dofeatureDel = (featuresType: string, data: any) => {
    return put({
        url: `gruul-mall-goods/manager/feature/del?featuresType=${featuresType}`,
        data,
    })
}

/*
 * @LastEditors: lexy 商品名称修改
 * @description: 商品名称修改
 * @param {string} id
 */
export const doNameUpdate = ({ id, name }: { id: string; name: string }) => {
    return put({
        url: `addon-supplier/supplier/manager/product/update/${id}/${name}`,
    })
}

/*
 * @LastEditors: lexy 更新商品价格
 * @description: 更新商品价格
 * @param {string} id
 */
export const doPriceUpdate = (data: any, productId: string) => {
    return post({
        url: `gruul-mall-storage/storage/product/${productId}/price`,
        data,
    })
}

/**
 * @description 获取供应商商品详情接口
 * @param id 商品id号
 * @param params 查询参数信息
 * @returns
 */
export const doGetSupplierCommodityDetails = (id: any, params: any = {}) => {
    return get({ url: `addon-supplier/supplier/manager/product/show/${id}`, params })
}

/**
 * @description 获取商品审核列表信息
 * @param params 参数信息
 * @returns
 */
export const doGetExamineGoodsList = (params: any = {}) => {
    return get({ url: 'addon-supplier/supplier/manager/product/audit', params })
}
/**
 * @description 重新审核商品信息
 * @param id 商品ID号
 * @returns
 */
export const doPutReviewGoodsInfo = (id: string) => {
    return put({ url: `addon-supplier/supplier/manager/product/audit/product/submit/${id}` })
}
