import { get, post, put, del } from '../../http'
import { OnlyPromotionType, OnlyPromotionSearchParams } from '../model'

/**
 * 获取会员专享活动列表信息
 */
export const doGetPromotionList = (params: OnlyPromotionSearchParams) => {
    return get({
        url: `/addon-member-only/onlyPromotion/onlyList`,
        params: {
            ...params,
            platformVisit: false,
        },
    })
}
/**
 * 新增/编辑会员专享活动信息
 * @param update boolean  // true：编辑 false：新增
 */
export const doPostPromotion = (data: OnlyPromotionType, update: boolean) => {
    return post({
        url: `/addon-member-only/onlyPromotion/edit`,
        params: { update },
        data,
    })
}

/**
 * 获取会员专享活动详情
 * @param shopId string  供应商ID
 * @param onlyId string  活动ID
 */
export const doGetPromotionDetail = (shopId: string, onlyId: string) => {
    return get({
        url: `/addon-member-only/onlyPromotion/${shopId}/${onlyId}`,
    })
}

/**
 * 获取会员专享活动下的商品
 * @param shopId string  供应商ID
 * @param onlyId string  活动ID
 * @param params any     列表筛选条件
 */
export const doGetPromotionProductsList = (shopId: string, onlyId: string, params: OnlyPromotionSearchParams) => {
    return get({
        url: `/addon-member-only/onlyPromotion/onlyProduct/${shopId}/${onlyId}`,
        params,
    })
}

/**
 * 供应商删除会员专享活动信息
 */
export const doDelPromotion = (idArr: number[]) => {
    return post({
        url: `/addon-member-only/onlyPromotion/del`,
        data: idArr,
    })
}
/**
 * 供应商下架会员专享活动信息
 */
export const doOffPromotion = (onlyId: string) => {
    return put({
        url: `addon-member-only/onlyPromotion/shop/${onlyId}/sellOff`,
    })
}

/**
 * 获取参与会员类型
 */
export const doGetMemberType = () => {
    return get({
        url: `addon-member/paid/member/all/type`,
    })
}
