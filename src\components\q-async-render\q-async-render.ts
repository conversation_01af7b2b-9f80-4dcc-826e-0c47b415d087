/*
 * @description: 异步渲染函数
 * @Author: lexy
 * @Date: 2022-08-29 22:25:25
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-06 15:07:24
 */
// TODO:接口先后传值都为空 loading状态未移除
import { h, defineComponent, PropType } from 'vue'
type PropsType = string | number | boolean | object
const buildProps = () => ({
    isLoad: {
        type: Boolean as PropType<PropsType>,
        default: false,
    },
})
export default defineComponent({
    name: 'AsyncRender',
    props: buildProps(),
    setup(props, { slots }) {
        return () => render(props, slots)
    },
})
function render(props, slots) {
    if (!slots.default) throw new Error('请设置默认插槽')
    const defaultSlots = slots.default ? slots.default() : []
    const loadingSlots = slots.loading ? slots.loading() : 'loading...'
    return h('div', null, [judgmentIsTrue(props.isLoad) ? defaultSlots : loadingSlots])
}
function judgmentIsTrue(val: PropType<PropsType>) {
    let returnType
    const varType = Object.prototype.toString.call(val)
    switch (varType) {
        case '[object Array]':
            returnType = val.length
            break
        case '[object Object]':
            returnType = Object.keys(val).length
            break
        default:
            returnType = Boolean(val)
            break
    }
    return Boolean(returnType)
}
