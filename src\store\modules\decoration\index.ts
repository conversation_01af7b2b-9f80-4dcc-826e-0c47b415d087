/*
 * @description:装修存储
 * @Author: lexy
 * @Date: 2022-08-02 10:33:19
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-31 14:03:16
 */
import { defineStore } from 'pinia'
import { decorationState } from './state'
import storage from '@/utils/Storage'
import type { SubmitForm, PageType, DecorationType } from '@/views/decoration/types'
const $storage = new storage()
export const useDecorationStore = defineStore('settingStore', {
    state: () => decorationState,
    actions: {
        SET_ISUSERCENTER(data: number) {
            this.isUsercenterCompontents = false
            this.userCenterType = ''
            this.activeTab = data
        },
        SET_LINK_FORM(data: string) {
            this.getFrom = data
        },
        SET_ACTIVE_COMINDEX(data: number) {
            this.activeComIndex = data
        },
        SET_ACTIVE_PAGE_TYPE(type: PageType) {
            this.activePageType = type
        },
        SET_ACTIVE_PAGE(page: SubmitForm) {
            this.activePage = page
        },
        SET_DEC_TYPE(type: DecorationType) {
            this.decorationType = type
            $storage.setItem('decorationType', type, 60 * 60 * 24)
        },
    },
    getters: {
        getterDecType(): DecorationType {
            if (this.decorationType === '') {
                if (!$storage.getItem('decorationType')) {
                    return ''
                } else {
                    return $storage.getItem('decorationType')
                }
            } else {
                return this.decorationType
            }
        },
    },
})
