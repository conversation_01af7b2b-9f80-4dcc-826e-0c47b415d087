/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-05 21:51:29
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-08 14:40:11
 */
import { defineComponent, PropType, computed, ref, watch, VNode, defineExpose } from 'vue'
import QTableRow from './QTableRow'
import { ColumnsType } from './table'
const buildProps = () => ({
    data: {
        type: Array as PropType<any[]>,
        default() {
            return []
        },
    },
    selection: {
        type: Boolean,
        default: false,
    },
    custom: {
        type: Boolean,
        default: false,
    },
    needBorder: {
        type: Boolean,
        default: false,
    },
    columns: {
        type: Array as PropType<ColumnsType[]>,
        default() {
            return []
        },
    },
    selectable: {
        type: Function,
        default: () => {},
    },
    tableHeaderClass: {
        type: String,
        default: '',
    },
    rowHeaderClass: {
        type: String,
        default: '',
    },
    needHoverBorder: {
        type: Boolean,
        default: false,
    },
    /*多行字段key*/
    multipleKey: {
        type: String,
        default: '',
    },
})
export default defineComponent({
    props: buildProps(),
    emits: ['check'],
    setup(prop, { slots, emit }) {
        const tableData = ref<any[]>([])
        const checkAll = computed({
            get: () => {
                return Boolean(tableData.value.length && tableData.value.every((item) => item.checked))
            },
            set: (v) => {
                onCheckAllChange(v)
                emit('check', getCheckedItem())
            },
        })
        const isIndeterminate = computed(() => {
            const len = tableData.value.filter((item) => item.checked).length
            return len !== 0 && len !== tableData.value.length
        })

        watch(
            prop,
            (value) => {
                tableData.value = value.data
            },
            { immediate: true },
        )
        /**
         * @LastEditors: lexy
         * @description: 设置组件propdata
         * @param {VNode} component
         * @param {any} data
         */
        const setPropData = (component: VNode, data: any) => {
            Object.assign(component.props ? component.props : {}, data)
        }
        /**
         * @LastEditors: lexy
         * @description: 单选
         * @param {boolean} checked
         * @param {number} index
         */
        const onItemCheckChange = (checked = false, index: number) => {
            Object.assign(tableData.value[index], { checked: !checked })
            emit('check', getCheckedItem())
        }
        /**
         * @LastEditors: lexy
         * @description: 全选
         * @param {boolean} v
         */
        function onCheckAllChange(v: boolean | number) {
            tableData.value = tableData.value.map((item) => {
                item.checked = v
                return item
            })
        }
        /**
         * @LastEditors: lexy
         * @description: 获取选中的条目
         */
        function getCheckedItem() {
            return tableData.value.filter((item) => item.checked)
        }
        /**
         * @LastEditors: lexy
         * @description: 清除所有选项
         */
        function clearCheckedItem() {
            tableData.value = tableData.value.map((item) => {
                item.checked = false
                return item
            })
        }
        // defineExpose({
        //     clearCheckedItem,
        // })
        return () => (
            <table class={['m__table']} cellpadding="0" cellspacing="0">
                <colgroup>
                    {prop.columns.map((item) => {
                        return <col width={item.width}></col>
                    })}
                </colgroup>
                <thead class={['m__table--head', prop.tableHeaderClass, slots.header && 'padding']}>
                    <tr class={'m__tr'}>
                        {prop.columns.map((item, i) => {
                            return (
                                <th style={item.customStyle} class={i === 0 && prop.selection && ['m__table--center']}>
                                    {i === 0 && prop.selection && (
                                        <el-checkbox indeterminate={isIndeterminate.value} vModel={checkAll.value} onChange={onCheckAllChange.bind(this, checkAll.value)}></el-checkbox>
                                    )}
                                    <div class={['m__table--shrink']}>{item.label}</div>
                                </th>
                            )
                        })}
                    </tr>
                </thead>

                {!tableData.value.length ? (
                    <tbody class="m__table--empty">
                        <tr>
                            <td class="empty__td" colspan={prop.columns.length} align="center">
                                暂无数据~
                            </td>
                        </tr>
                    </tbody>
                ) : (
                    tableData.value.map((row, index) => {
                        return (
                            <tbody
                                class={[
                                    'm__table--body',
                                    prop.custom ? 'custom' : 'default',
                                    prop.needBorder && 'need--border',
                                    prop.needHoverBorder && row.close ? 'hover--class' : 'ordinary--class',
                                ]}
                            >
                                {slots.header && (
                                    <tr>
                                        <td colspan={prop.columns.length}>
                                            <div class={['body--header', prop.rowHeaderClass, { close: row.close }]}>{slots.header({ row, index })}</div>
                                        </td>
                                    </tr>
                                )}
                                {slots.custom ? (
                                    slots.custom({ row, index })
                                ) : (
                                    <QTableRow>
                                        {slots.default &&
                                            slots
                                                .default()
                                                .filter((item) => {
                                                    //zrb:修改兼容vue3时v-if失效问题
                                                    return item && item.type && item.type !== Symbol.for('v-cmt')
                                                })
                                                .map((child, i) => {
                                                    // 设置tableColumn的props
                                                    setPropData(child, {
                                                        row,
                                                        index,
                                                        hasSlots: Boolean(child.children),
                                                    })
                                                    return (
                                                        <td class={['m__table--item', !child && 'hide']}>
                                                            <div class={['selection__checkbox', prop.selection && i === 0 && 'selection']}>
                                                                {i === 0 && prop.selection && (
                                                                    <el-checkbox
                                                                        key={row.checked}
                                                                        checked={row.checked}
                                                                        disabled={!prop.selectable(row)}
                                                                        onChange={onItemCheckChange.bind(this, row.checked, index)}
                                                                    ></el-checkbox>
                                                                )}
                                                                <div class={['m__table--shrink']}>{child}</div>
                                                            </div>
                                                        </td>
                                                    )
                                                })}
                                    </QTableRow>
                                )}
                            </tbody>
                        )
                    })
                )}
            </table>
        )
    },
})
