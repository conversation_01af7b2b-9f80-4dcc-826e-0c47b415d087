<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-25 20:26:15
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="名称">
                            <el-input v-model="searchType.name" placeholder="请输入名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item style="margin-bottom: 0">
                            <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                            <el-button class="from_btn" round @click="handleReset">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import { doGetCategory } from '@/apis/good'
import MCard from '@/components/MCard.vue'
/**
 * reactive variable
 */

export type SearchType = Record<'createBeginTime' | 'createEndTime' | 'name' | 'categoryId' | 'status', string>

const isShow = ref(false)

const searchType = reactive({
    name: '',
})

const categoryList = ref([])
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
onMounted(() => {
    init()
})
/**
 * function
 */
async function init() {
    const { data, code } = await doGetCategory({ size: 1000 })
    categoryList.value = data?.records
}

function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    searchType.name = ''
    search()
}
</script>
