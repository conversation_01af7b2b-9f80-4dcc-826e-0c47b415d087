<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import AccordionFrom from '@/views/order/components/accordionFrom.vue'
import { ElMessage } from 'element-plus'
import PageManage from '@/components/pageManage/PageManage.vue'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import { TabItem, afsPagesStatus, getAfsListStatusCn, getAfsNumStatusCn } from '@/composables/useAfsStatus'
import { doGetAfsList } from '@/apis/afs'
import type { ApiOrderAfsItem } from '@/views/afs/types'
import type { OrderListSearchData } from '@/views/order/types/order'
import type { TabPaneName } from 'element-plus'
import { doGetExportData, doPostAfsExport } from '@/apis/exportData'
/*
 *variable
 */
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
const { divTenThousand } = useConvert()
const activeTab = ref(' ')
// 批量备注弹窗
const noteDialog = ref(false)
const isPackUp = ref(false)
// 备注消息文本
const TabData = ref<ApiOrderAfsItem[]>([])
const multiSelect = ref<ApiOrderAfsItem[]>([])
const ids = ref<string[]>([])
const currentRemark = ref('')
const $router = useRouter()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const afsListparams = reactive({
    status: '',
    params: {
        afsNo: '',
        buyerNickname: '',
        productName: '',
        receiverName: '',
        startTime: '',
        endTime: '',
    },
})
/*
 *lifeCircle
 */
/*
 *function
 */
async function initAfsList() {
    const params = { ...afsListparams.params, status: afsListparams.status, ...pageConfig }
    const { code, data } = await doGetAfsList(params)
    if (code !== 200) return ElMessage.error('订单列表获取失败')
    TabData.value = data.records
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}
/**
 * @LastEditors: lexy
 * @description: 处理审核
 * @returns {*}
 */
const handleAudit = (row: ApiOrderAfsItem, afsNo: string) => {
    const { no, shopOrderItemId, packageId, orderNo } = row
    $router.push({
        name: 'orderSaleDetailIndex',
        query: { orderNo, no, shopOrderItemId, packageId: packageId || '', afsNo, audit: 'afterSalesInfo' },
    })
}
/**
 * @LastEditors: lexy
 * @description: 下拉选择（查看详情）
 * @params salesShow 显示售后页面
 * @returns {*}
 */
const handleSelected = (row: ApiOrderAfsItem, e: string, afsNo: string) => {
    const { no, shopOrderItemId, packageId, orderNo } = row
    if (e !== '查看详情') return
    $router.push({ name: 'orderSaleDetailIndex', query: { orderNo, no, shopOrderItemId, packageId: packageId || '', afsNo } })
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    pageConfig.size = value
    initAfsList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initAfsList()
}

const totalPrice = (price: string, num: number) => {
    return divTenThousand(price).mul(num)
}
/**
 * @LastEditors: lexy
 * @description: 手风琴表单收起
 * @param {*} value
 * @returns {*}
 */
const handleSearchChange = (value: boolean) => {
    isPackUp.value = value
}
const handleTabChange = (name: TabPaneName) => {
    afsListparams.status = name as string
    initAfsList()
}
const handleSearchData = (params: OrderListSearchData) => {
    afsListparams.params = {
        afsNo: params.no,
        buyerNickname: params.buyerNickname,
        productName: params.productName,
        receiverName: params.receiverName,
        startTime: params.startTime,
        endTime: params.endTime,
    }
    initAfsList()
}
/**
 * @LastEditors: lexy
 * @description: 批量备注
 * @returns {*}
 */
const handleNote = () => {
    if (multiSelect.value.length) {
        ids.value = multiSelect.value.map((item) => item.no)
        noteDialog.value = true
        return
    }
    ElMessage.error('请先选择订单')
}
const handleNoteItem = (row: ApiOrderAfsItem) => {
    if (row.no) {
        ids.value = [row.no]
        if (row.remark) {
            currentRemark.value = row.remark
        }
        noteDialog.value = true
    }
}
const updateList = () => {
    multiSelect.value = []
    ids.value = []
    initAfsList()
}
// 导出数据
const exportData = async (SearchFromData: any) => {
    if (multiSelect.value.length) {
        let exportAfsOrderNos = ['']
        exportAfsOrderNos = multiSelect.value.map((item) => item.no)
        const { code, data, msg } = await doPostAfsExport({ exportAfsOrderNos })
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    } else {
        let param = {
            afsNo: SearchFromData.no,
            buyerNickname: SearchFromData.buyerNickname,
            productName: SearchFromData.productName,
            receiverName: SearchFromData.receiverName,
            startTime: SearchFromData.clinchTime?.[0],
            endTime: SearchFromData.clinchTime?.[1],
            status: activeTab.value,
            exportAfsOrderNos: '',
        }
        param.status = param.status.trim()
        const { code, data, msg } = await doPostAfsExport(param)
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    }
}
</script>

<template>
    <div class="sale">
        <!-- 搜索部分s -->
        <accordion-from @search-data="handleSearchData" @search-change="handleSearchChange" @export-data="exportData" />
        <!-- 搜索部分e -->
        <!-- tab部分s -->
        <el-tabs v-model="activeTab" class="demo-tabs" style="margin-top: 13px" @tab-change="handleTabChange">
            <el-tab-pane v-for="item in TabItem" :key="item.id" :label="item.title" :name="item.name">
                <!-- <dropDown title="批量备注" /> -->
            </el-tab-pane>
        </el-tabs>
        <!-- <el-button class="caozuo_btn" style="background: #ecf5fd" type="primary" plain text bg round @click="handleNote">批量备注</el-button> -->
        <!-- tab部分e -->
        <!-- tab表格s -->
        <q-table v-model:checkedItem="multiSelect" :data="TabData" :selection="true" style="margin-top: 13px" class="table" :class="{ packUp: !isPackUp }">
            <template #header="{ row }">
                <el-tag style="margin-right: 10px">{{ getAfsNumStatusCn(row.type) }}单</el-tag>
                <div style="margin-right: 36px">订单号:{{ row.no }}</div>
                <div>创建时间:{{ row.createTime }}</div>
                <el-row style="flex: 1" justify="end">
                    <remark-flag :content="row.remark" @see-remark="handleNoteItem(row)" />
                </el-row>
            </template>
            <q-table-column label="退款商品" align="left">
                <template #default="{ row }">
                    <el-avatar style="width: 68px; height: 68px; flex-shrink: 0" shape="square" size="large" :src="row.afsOrderItem.image" />
                    <div style="flex: 1; padding-left: 10px">
                        <div class="avatar_text_box">
                            <div class="money_text">
                                <div class="avatar_text_box__show" title="row.afsOrderItem.productName">
                                    {{ row.afsOrderItem.productName }}
                                </div>
                            </div>
                            <div class="money_text shop_specs" style="width: 200px" :title="row.afsOrderItem.specs?.join(',')">
                                {{ row.afsOrderItem.specs?.join(',') }}
                            </div>
                            <p class="order-info__selltype">{{ SellTypeEnum[row?.afsOrderItem?.sellType] }}</p>
                        </div>
                    </div>
                    <div style="width: 100px; height: 68px; text-align: center" class="money_text">
                        <div>￥{{ divTenThousand(row.afsOrderItem.dealPrice) }}</div>
                        <div style="color: #838383; font-size: 10px">x {{ row.afsOrderItem.num }}</div>
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="退款金额" class="rate_size" width="100">
                <template #default="{ row }">
                    <div class="avatar_text money_text" style="width: 68px; text-align: center; color: #f00; font-weight: 600">
                        ￥{{ totalPrice(row.afsOrderItem.dealPrice, row.afsOrderItem.num) }}
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="退款用户" width="130">
                <template #default="{ row }">
                    <div class="tab__client_box">
                        <div class="avatar_text avatar_text__bottom money_text">
                            <span style="color: #2e99f3; margin-right: 10px">{{ row.buyerNickname }}</span>
                        </div>
                        <!-- <div style="padding: 0 10px 0" class="money_text">
                            (收货人：{{ row.afsOrderReceiver?.name + ',' + row.afsOrderReceiver?.mobile }})
                        </div> -->
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="状态" width="95">
                <template #default="{ row }: { row: ApiOrderAfsItem }">
                    <div class="sale__status">
                        <div class="order-status_text">{{ afsPagesStatus[row.packageStatus].list }}</div>
                        <div class="money_text">{{ getAfsListStatusCn(row) }}</div>
                    </div>
                </template>
            </q-table-column>
            <q-table-column prop="sex" label="操作" width="170">
                <template #default="{ row }">
                    <!-- <q-dropdown-btn
                        v-if="activeTab === 'PENDING'"
                        :option="[{ label: 'details', name: '查看详情' }]"
                        @left-click="handleAudit(row, row.no)"
                        @right-click="handleSelected(row, $event, row.no)"
                    /> -->
                    <el-button v-if="activeTab === 'PENDING'" round type="primary" @click="handleAudit(row, row.no)">审核</el-button>
                    <el-button v-else class="caozuo_btn" style="background: #ecf5fd" type="primary" plain text bg round @click="handleSelected(row, '查看详情', row.no)"> 查看详情 </el-button>
                </template>
            </q-table-column>
        </q-table>
        <!-- tab表格e -->
        <el-row justify="space-between" align="middle">
            <el-button class="caozuo_btn" style="background: #ecf5fd" type="primary" plain text bg round @click="handleNote">批量备注 </el-button>
            <!-- 好用的分页器 -->
            <page-manage
                v-model="pageConfig"
                :load-init="true"
                :page-size="pageConfig.size"
                :total="pageConfig.total"
                @reload="initAfsList"
                @handle-size-change="handleSizeChange"
                @handle-current-change="handleCurrentChange"
            />
        </el-row>
        <!-- 售后备注弹窗s -->
        <remark-popup v-model:isShow="noteDialog" v-model:ids="ids" v-model:remark="currentRemark" remark-type="AFS" @success="updateList" />
        <!-- 售后备注弹窗s -->
    </div>
</template>

<style lang="scss" scoped>
@include b(table) {
    overflow-x: auto;
    height: calc(100vh - 500px);
    transition: height 0.5s;
}
@include b(packUp) {
    height: calc(100vh - 340px);
}
.caozuo_btn:hover {
    color: #fff;
    background: #309af3 !important;
}
.tab {
    margin-top: 13px;
    background: #f9f9f9;
}
@include b(sale) {
    @include e(status) {
        @include flex;
        flex-direction: column;
    }
}
@include b(money_text) {
    font-size: 12px;
    color: #000000;
    overflow: hidden;
    width: 100%;
}
@include b(shop_specs) {
    // width: 100px;
    @include utils-ellipsis(1);
}
@include b(order-status_text) {
    margin-bottom: 5px;
    font-size: 24rpx;
    color: #ff7417;
}
@include b(avatar_text) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 可以显示的行数，超出部分用...表示*/
    -webkit-box-orient: vertical;
    @include e(bottom) {
        margin-bottom: 5px;
    }
}
@include b(avatar_text_box) {
    height: 68px;
    @include flex(space-between, flex-start);
    flex-direction: column;
    overflow: hidden;
    @include e(show) {
        width: 260px;
        @include utils-ellipsis(1);
        font-weight: 600;
    }
}

@include b(NoteDialog) {
    @include e(title) {
        font-size: 14px;
        color: #333333;
        padding: 0 0 14px 0;
    }
}
</style>
