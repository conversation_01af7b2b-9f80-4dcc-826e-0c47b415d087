/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-17 10:33:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-31 14:03:33
 */
import { defineStore } from 'pinia'
import { orderListState } from './state'
import storage from '@/utils/Storage'
import type { ApiOrder } from '@/views/order/types/order'
export const useDeliveryOrderList = defineStore('deliveryOrderList', {
    state: () => orderListState,
    actions: {
        SET_ORDER_LIST(data: ApiOrder[]) {
            new storage().setItem('deliveryOrderList', data, 60 * 60 * 24)
            this.orderList = data
        },
    },
})
