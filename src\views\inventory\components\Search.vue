<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-26 17:56:42
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.supplierGoodsName" placeholder="请输入商品名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品ID">
                            <el-input v-model="searchType.supplierProductId" placeholder="请输入商品名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品类型">
                            <el-select
                                v-model="searchType.productType"
                                v-loadMore="{
                                    fn: loadMoreHandle,
                                }"
                                placeholder="请选择"
                                style="width: 224px"
                            >
                                <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
// import { doGetPlatformCategory } from '@/apis/good'
import MCard from '@/components/MCard.vue'
// import DateUtil from '@/utils/date'
export type SearchType = Record<'supplierGoodsName' | 'supplierProductId' | 'productType', string>
// type ShopCategoryItem = Record<'id' | 'name' | 'parentId' | 'level', string>
// interface ShopCategoryList extends ShopCategoryItem {
//     disabled?: boolean
//     children: ShopCategoryList[]
// }
/**
 * reactive variable
 */
const isShow = ref(false)
const typeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'REAL_PRODUCT',
        label: '实物商品',
    },
    {
        value: 'VIRTUAL_PRODUCT',
        label: '虚拟商品',
    },
])
const searchType = reactive({
    supplierGoodsName: '',
    supplierProductId: '',
    productType: '',
})
// const categoryList = ref<ShopCategoryList[]>([])
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
// onMounted(() => {
//     init()
// })
/**
 * function
 */
// async function init() {
//     const { data, code } = await doGetPlatformCategory()
//     categoryList.value = checkCategoryEnable(1, data)
// }
// function checkCategoryEnable(currentLevel: number, records: any[]) {
//     const isLastLevel = currentLevel === 2 // 3
//     for (let index = 0; index < records.length; ) {
//         const record = records[index]
//         if (isLastLevel) {
//             record.disabled = false
//             index++
//             continue
//         }
//         const children = (record.children || record.secondCategoryVos || record.categoryThirdlyVos) as any[]
//         delete record.secondCategoryVos
//         delete record.categoryThirdlyVos
//         const disable = !children || children.length === 0
//         record.disabled = disable
//         if (disable) {
//             records.splice(index, 1)
//             continue
//         }
//         checkCategoryEnable(currentLevel + 1, children)
//         if (children.length === 0) {
//             records.splice(index, 1)
//             continue
//         }
//         record.children = children
//         index++
//     }

//     return records
// }
const loadMoreHandle = (e: any) => {
    $emit('onSearchParams')
}
// const handleChangeCascader = (e: any) => {
//     searchType.platformCategoryId = e[e.length - 1]
// }
function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    search()
}
</script>
