/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-20 17:12:36
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-20 17:22:14
 */
export interface ApiFinanceItem {
    amount: string
    changeType: string
    createTime: string
    orderNo: string
    shopId: string
    shopName: string
    tradeDetail: TradeDetail
    tradeNo: string
    tradeTime: string
    tradeType: string
    userId: string
    id?: string
}
interface TradeDetail {
    afsNo?: string
    shopOrderItemId?: string
    packageId: string
}
