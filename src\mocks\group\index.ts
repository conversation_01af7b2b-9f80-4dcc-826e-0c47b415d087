/*
 * @description:
 * @Author: lexy
 * @Date: 2023-03-14 15:48:51
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-04 09:41:19
 */
import { rest } from 'msw'
// import { API } from '@/q-plugin/group/apis'
import { groupModel, groupOrderModel } from './models'
enum API {
    orderList = 'addon-team/team/activity/order',
    groupList = 'addon-team/team/activity',
    groupForm = 'addon-team/team/activity',
    fillGroupForm = 'addon-team/team/activity',
    delGroup = 'addon-team/team/activity/delete/batch',
}
export const mockGroupList = () => {
    return rest.get(API.groupList, (req, res, ctx) => {
        const current = req.url.searchParams.get('current')
        const size = req.url.searchParams.get('size')
        return res(ctx.json(groupModel(Number(current), Number(size))))
    })
}
export const mockGroupOrderList = () => {
    return rest.get('addon-team/team/order', (req, res, ctx) => {
        const current = req.url.searchParams.get('current')
        const size = req.url.searchParams.get('size')
        return res(ctx.json(groupOrderModel(Number(current), Number(size))))
    })
}
export const groupServer = [mockGroupList(), mockGroupOrderList()]
