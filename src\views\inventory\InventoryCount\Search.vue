<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-31 18:58:30
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="订单号">
                            <el-input v-model="searchType.no" placeholder="请输入" maxlength="23"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="盘点时间">
                            <el-date-picker
                                v-model="searchType.date"
                                :clearable="false"
                                type="datetimerange"
                                range-separator="-"
                                start-placeholder="开始时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import MCard from '@/components/MCard.vue'
// import DateUtil from '@/utils/date'
export type SearchType = Record<'no' | 'stockChangeType' | 'date', string>

/**
 * reactive variable
 */
const isShow = ref(false)
const searchType = reactive({
    no: '',
    date: '',
})
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)

function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    search()
}
</script>
