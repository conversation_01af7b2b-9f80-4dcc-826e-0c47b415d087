<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-02 16:51:16
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.productName" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品ID">
                            <el-input v-model="searchType.productId" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="流水号">
                            <el-input v-model="searchType.Id" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="类型">
                            <el-select v-model="searchType.stockChangeType" placeholder="请选择" style="width: 224px">
                                <el-option v-for="item in stockChangeTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="销售方式">
                            <el-select v-model="searchType.sellType" placeholder="请选择" style="width: 224px">
                                <el-option v-for="item in sellTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="关联订单号">
                            <el-input v-model="searchType.orderNo" placeholder="请输入" maxlength="23"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="变更时间">
                            <el-date-picker
                                v-model="searchType.date"
                                :clearable="false"
                                type="datetimerange"
                                range-separator="-"
                                start-placeholder="开始时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import MCard from '@/components/MCard.vue'
export type SearchType = Record<'supplierGoodsName' | 'supplierProductId' | 'productType', string>
/**
 * reactive variable
 */
const isShow = ref(false)
const sellTypeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'PURCHASE',
        label: '采购商品',
    },
    {
        value: 'CONSIGNMENT',
        label: '代销商品',
    },
])
const stockChangeTypeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'PUBLISHED_INBOUND',
        label: '发布入库',
    },
    {
        value: 'EDITED_INBOUND',
        label: '编辑入库',
    },
    {
        value: 'OVERAGE_INBOUND',
        label: '盘盈入库',
    },
    {
        value: 'RETURNED_INBOUND',
        label: '退货入库',
    },
    {
        value: 'ORDER_CANCELLED_INBOUND',
        label: '订单取消入库',
    },
    {
        value: 'ALLOCATION_INBOUND',
        label: '调拨入库',
    },
    {
        value: 'OTHER_INBOUND',
        label: '其它入库',
    },
    {
        value: 'SOLD_OUTBOUND',
        label: '销售出库',
    },
    {
        value: 'EDITED_OUTBOUND',
        label: '编辑出库',
    },
    {
        value: 'SHORTAGE_OUTBOUND',
        label: '盘亏出库',
    },
    {
        value: 'ALLOCATION_OUTBOUND',
        label: '调拨出库',
    },
    {
        value: 'OTHER_OUTBOUND',
        label: '其它出库',
    },
])
const searchType = reactive({
    productName: '',
    productId: '',
    Id: '',
    stockChangeType: '',
    sellType: '',
    orderNo: '',
    date: '',
})
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    search()
}
</script>
