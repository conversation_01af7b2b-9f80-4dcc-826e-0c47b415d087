<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 11:43:17
 * @LastEditors: lexy
 * @LastEditTime: 2024-05-08 14:38:53
-->
<script lang="ts" setup>
import { PropType } from 'vue'
import { InfoFilled, Search } from '@element-plus/icons-vue'
import { useVModel } from '@vueuse/core'
import { OnlyPromotionSearchParams, OnlyStatusMap } from '@/apis/marketing/model'
import { ElMessageBox } from 'element-plus'
import { doGetArticle } from '@/apis/remark'

/*
 *variable
 */
const props = defineProps({
    modelValue: {
        type: Object as PropType<OnlyPromotionSearchParams>,
        default() {
            return {}
        },
    },
    batchDisabled: {
        type: Boolean,
        default: true,
    },
    leftBtnText: {
        type: String,
        default: '新增会员专享',
    },
})
const emit = defineEmits(['update:modelValue', 'add', 'search', 'batchDel'])
const _modelValue = useVModel(props, 'modelValue', emit)

const openTips = async () => {
    // https://nav.chongyoulingxi.com/gwadmin_web_main/prod-api/system/magic/shop/article/get?article_type=member_instructions
    // 先获取富文本内容
    doGetArticle('member_instructions').then((res) => {
        if (res?.data?.length) {
            ElMessageBox.alert(`<div style="width: 100%; overflow-y: scroll;height: 500px;padding: 0 20px">${res?.data?.[0]?.articleText}</div>`, res?.data?.[0]?.articleTitle || '会员专享说明', {
                dangerouslyUseHTMLString: true,
                center: true,
                customStyle: {
                    width: '700px',
                    height: '600px',
                },
            })
        } else {
            ElMessage.warning('暂无说明')
        }
    })
}
</script>

<template>
    <div style="margin-bottom: 15px; width: 100%; display: flex; justify-content: space-between; padding-right: 15px">
        <div>
            <el-button round type="primary" @click="emit('add')">
                {{ props.leftBtnText }}
            </el-button>
            <el-button round :disabled="$props.batchDisabled" @click="emit('batchDel')"> 批量删除 </el-button>
        </div>
        <div class="header-right">
            <el-select v-model="_modelValue.onlyStatus" :placeholder="''" style="width: 200px" @change="emit('search')">
                <el-option label="全部状态" value="" />
                <el-option v-for="(item, key) in OnlyStatusMap" :key="key" :label="item" :value="key" />
            </el-select>
            <el-input v-model="_modelValue.keyword" placeholder="活动名称" style="width: 200px" @keyup.enter="emit('search')">
                <template #append>
                    <el-button :icon="Search" @click="emit('search')" />
                </template>
            </el-input>
            <el-button :icon="InfoFilled" link @click="openTips" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.header-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
}
</style>
