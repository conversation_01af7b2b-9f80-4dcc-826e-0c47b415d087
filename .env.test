###
 # @description: 
 # @Author: lexy
 # @Date: 2023-07-14 15:03:14
 # @LastEditors: lexy
 # @LastEditTime: 2023-08-08 11:09:30
### 
# 开发环境配置
MODE=test
# request相关配置
VITE_REQUEST_TIME_OUT = 10000
VITE_IS_SINGLE = false
VITE_CLIENT_TYPE=SUPPLIER_CONSOLE
VITE_RESUME_MOCK=fasle
#平台名称
VITE_PLATFORM_NAME=宠有灵犀
#logo
VITE_RESUME_LOGO=https://devoss.chongyoulingxi.com/system-front/mobile/logo_with_white_bg.png
#测试环境url
VITE_BASE_URL=https://dev.chongyoulingxi.com/api/
VITE_ARTICLE_BASE_URL=https://nav.chongyoulingxi.com/gwadmin_web_main/prod-api/
#stomp 连接通信地址uri 与BASE_URL拼接成完整url
VITE_STOMP_CONNECT_URI=gruul-mall-carrier-pigeon/pigeon/stomp
#store配置
VITE_LOCAL_STORAGE_KEY_PREFIX = supplierlocal
#高德地图配置
VITE_MAP_KEY=5834a95f0e9d1a6794a4c21dd275f6cd
#静态资源地址
VITE_CDN_URL=https://dev.chongyoulingxi.com/gruul-mall-single/gruul/oss/
# 密钥
VITE_CRYPTO_KEY=xt8JWPub4ycvo9DK
# 初始化向量
VITE_CRYPTO_IV=HX3ByXkPdyvExgf6