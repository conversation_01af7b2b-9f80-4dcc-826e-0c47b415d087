<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 15:09:24
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品id">
                            <el-input v-model="searchType.supplierProductId" placeholder="请输入商品id" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.supplierGoodsName" placeholder="请输入商品名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="销售方式">
                            <el-select
                                v-model="searchType.sellType"
                                v-loadMore="{
                                    fn: loadMoreHandle,
                                }"
                                placeholder="请选择"
                                style="width: 224px"
                            >
                                <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台类目">
                            <el-cascader
                                ref="cascaderRef"
                                style="width: 224px"
                                :options="categoryList"
                                :props="platformCascaderProps"
                                placeholder="请选择展示分类"
                                :show-all-levels="false"
                                @change="handleChangeCascader"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import { doGetPlatformCategory } from '@/apis/good'
import { doGetCurrentShopRelationCategory } from '@/apis/store/index'

import MCard from '@/components/MCard.vue'
import { CascaderInstance } from 'element-plus'
import { useShopInfoStore } from '@/store/modules/shopInfo'

// import DateUtil from '@/utils/date'
export type SearchType = Record<'createBeginTime' | 'createEndTime' | 'name' | 'categoryId' | 'status', string>
type ShopCategoryItem = Record<'id' | 'name' | 'parentId' | 'level', string>
interface ShopCategoryList extends ShopCategoryItem {
    disabled?: boolean
    children: ShopCategoryList[]
}
/**
 * reactive variable
 */
const cascaderRef = ref<CascaderInstance | null>(null)
const isShow = ref(false)
const typeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'PURCHASE',
        label: '采购商品',
    },
    {
        value: 'CONSIGNMENT',
        label: '代销商品',
    },
])
const searchType = reactive({
    supplierProductId: '',
    supplierGoodsName: '',
    sellType: '',
    secondPlatformCategoryId: '',
})
const platformCascaderProps = {
    expandTrigger: 'hover' as 'click' | 'hover',
    label: 'currentName',
    value: 'currentId',
}
const shopCascaderProps = {
    expandTrigger: 'hover' as 'click' | 'hover',
    label: 'name',
    value: 'id',
}
const categoryList = ref<ShopCategoryList[]>([])
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
onMounted(() => {
    init()
})
/**
 * function
 */
async function init() {
    const shopStore = useShopInfoStore()
    const res = await doGetCurrentShopRelationCategory({ shopId: shopStore.shopInfo.id })
    for (let i = 0; i < res.data?.length; i++) {
        const element = res.data[i]
        element.currentName = element.parentName
        element.currentId = element.parentId
        element.children = []
        element.children.push({
            currentName: element.currentCategoryName,
            currentId: element.currentCategoryId,
        })
    }

    categoryList.value = res.data || []
}

const loadMoreHandle = (e: any) => {
    $emit('onSearchParams')
}
const handleChangeCascader = (e: any) => {
    searchType.secondPlatformCategoryId = e[e.length - 1]
}
function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    cascaderRef.value?.cascaderPanelRef?.clearCheckedNodes()
    search()
}
</script>
