export enum DATA_TYPE_ENUM {
    USER_ORDER = '用户订单',
    DELIVERY_ORDER = '快递面单',
    PURCHASE_ORDER = '采购订单',
    AFTER_SALES_WORK_ORDER = '售后工单',
    STATEMENT_OF_ACCOUNT = '对账单',
    STORED_VALUE_FLOW = '储值流水',
    MEMBER_RECORDER = '会员记录',
    STORE_SETTLEMENT = '店铺结算',
    SUPPLIER_SETTLEMENT = '供应商结算',
}

export enum EXPORT_STATUS_ENUM {
    PROCESSING = '生成中',
    SUCCESS = '已完成',
    FAILED = '失败',
}
