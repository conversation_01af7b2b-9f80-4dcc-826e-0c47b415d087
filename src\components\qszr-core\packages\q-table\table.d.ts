/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-05 23:30:42
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-15 15:10:07
 */
type ColumnsType = Record<'label' | 'prop' | 'customStyle' | 'width', string>
import { VNode, RendererNode, RendererElement } from 'vue'
interface MTableSlotsType {
    header?: (row: any) => VNode<
        RendererNode,
        RendererElement,
        {
            [key: string]: any
        }
    >[]
    custom?: (row: any) => VNode<
        RendererNode,
        RendererElement,
        {
            [key: string]: any
        }
    >[]
    default: (row: any) => VNode<
        RendererNode,
        RendererElement,
        {
            [key: string]: any
        }
    >[]
}
