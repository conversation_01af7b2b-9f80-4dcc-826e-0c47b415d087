<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:32:00
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-27 16:35:49
-->
<template>
    <aside-header @keyword-change="(val) => emits('keywordChange', val)" @search-focus="(val) => emits('searchFocus', val)" />
    <aside-main :message-users="messageUsers" @change="(val) => emits('change', val)" />
</template>
<script setup lang="ts">
import AsideHeader from './AsideHeader.vue'
import AsideMain from './AsideMain.vue'
import { MessageUser } from '@/views/mall/customerService/types'
import { PropType } from 'vue'

const props = defineProps({
    messageUsers: {
        type: Array as PropType<Array<MessageUser>>,
        default: () => [],
    },
})
const emits = defineEmits(['change', 'keywordChange', 'searchFocus'])
</script>
<style scoped></style>
