import { get } from '../apis/http'
import routers from '../router'
// @ts-ignore
import * as vue from 'vue'
import * as element from 'element-plus'
import * as request from '../apis/http'
import MContent from '../components/layout/MContent.vue'
import { RouteRecordRaw } from 'vue-router'

let nameDriver = 0
export const getComponent = async (menu: any) => {
    const addon = menu.addon
    const component = addon.component
    const result = await get({
        url: `${addon.name}/static/${component.jar}/${component.file}`,
        showLoading: false,
    })
    // @ts-ignore
    window.addon = {
        vue,
        element,
        request,
    }
    // @ts-ignore
    new Function(<string>result)()
    // @ts-ignore
    const value = window.value
    // @ts-ignore
    delete window.addon
    // @ts-ignore
    delete window.value
    return value
}
//渲染路由并获取路由名称
export const renderGetRouteRouteName = async (parentName: string, menu: any) => {
    const currentName = `Addon${String.fromCharCode(nameDriver++)}`
    // @ts-ignore
    const route: RouteRecordRaw = {
        path: '/',
        name: currentName,
        meta: {
            title: menu.name,
            auth: true,
        },
    }
    switch (menu.type) {
        case 'CATALOG':
            route.redirect = '/overview'
            // @ts-ignore
            route.component = MContent
            break
        case 'MENU':
            route.path = menu.path
            route.component = await getComponent(menu)
            break
        default:
            // @ts-ignore
            route.path = null
            break
    }
    if (route.path) {
        routers.addRoute(parentName, route)
    }
    return currentName
}
/**
 * 路由标题和名称映射
 */
export const routesTitleNameRecord = () => {
    const record: Record<string, string> = {}
    const routes = routers.getRoutes()
    routes.forEach((route) => {
        const meta = route.meta
        if (!meta || !meta.title || !route.name) {
            return
        }
        const title = <string>meta.title
        record[title] = <string>route.name
    })
    return record
}
//增加路由
export const addRoute = (record: Record<string, string>, parentName: string, menus: Array<any>) => {
    console.log('menus', menus)
    menus.forEach(async (menu) => {
        const currentName = menu.addon ? await renderGetRouteRouteName(parentName, menu) : record[menu.name]
        if (!menu.children) {
            return
        }
        addRoute(record, currentName, menu.children)
    })
}

export const getMenus = async () => {
    nameDriver = 'A'.charCodeAt(0)
    const record = routesTitleNameRecord()
    const result = await get({
        url: 'gruul-mall-uaa/uaa/menu/navigate',
        showLoading: false,
    })
    const data = result.data
    addRoute(record, 'Main', data.menus)
    console.log('menus render done')
    return data.menus
}

//追加默认菜单
export const addDefaultMenu = (menus: Array<any>) => {
    menus.unshift({
        path: '/overview',
        name: '经营概况',
        type: 'CATALOG',
    })
    return menus
}
