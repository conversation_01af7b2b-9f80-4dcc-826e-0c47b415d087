export type OnlyPromotionSearchParams = {
    shopId?: string
    keyword?: string
    memberType?: JoinMemberType
    onlyStatus?: OnlyStatusType
    productParams?: {
        productId?: string
        shopId?: string
        onlyId?: string
    }
    [key: string]: any
}

export type MemberType = {
    id: string
    name: string
    paid: string
    include?: boolean
}

// 参与会员
export type JoinMemberType = keyof typeof JOIN_MEMBER_TYPES
export enum JOIN_MEMBER_TYPES {
    ALL,
    SHOP,
}

// 活动状态 [0未开始 1进行中 2已结束 3暂停中 4商家关闭]
export type OnlyStatusType = keyof typeof ONLY_STATUS_TYPES
export enum ONLY_STATUS_TYPES {
    NOT_STARTED, //未开始
    PROCESSING, //进行中
    OVER, //已结束
    ILLEGAL_SELL_OFF, //违规下架
    SHOP_SELL_OFF, //下架
}

export const OnlyStatusColorMap = {
    NOT_STARTED: 'info',
    PROCESSING: 'primary',
    OVER: 'danger',
    ILLEGAL_SELL_OFF: 'warning',
    SHOP_SELL_OFF: 'warning',
}

export const OnlyStatusMap = {
    NOT_STARTED: '未开始',
    PROCESSING: '进行中',
    OVER: '已结束',
    ILLEGAL_SELL_OFF: '违规下架',
    SHOP_SELL_OFF: '下架',
}

// 限购限制
export type PayLimitType = keyof typeof PAY_LIMIT
export enum PAY_LIMIT {
    UNLIMITED,
    PRODUCT_LIMITED,
    SKU_LIMITED,
    ACTIVITY_LIMITED,
    ACTIVITY_ORIGIN_LIMITED,
}

/**
 * 限购类型
 */
export type StockType = keyof typeof STOCK_TYPES
export enum STOCK_TYPES {
    UNLIMITED,
    LIMITED,
}

// 适用优惠 类型
export type ApplyType = keyof typeof APPLY_TYPES
export enum APPLY_TYPES {
    APPLY_USER_PRICE, // 0-会员价
    APPLY_COUPON, // 1-优惠劵
    APPLY_FULL_REDUCED, // 2-满减
}

export type OnlyPromotionType = {
    onlyId?: string
    shopId?: string
    shopName?: string
    name: string
    joinMember: MemberType[]
    limitMember: MemberType[]
    onlyStatus?: OnlyStatusType
    payLimit?: PayLimitType
    startTime?: string
    endTime?: string
    orderClosingTime: number
    deductionType: boolean
    applyTypes?: ApplyType[]
    onlyProducts: OnlyProductType[]
}

export type OnlyProductType = {
    onlyId?: string
    onlyProductSkus: OnlyProductSkuType[]
    id?: number
    shopId?: number
    productId: string
    productPic: string
    productName: string
    desc?: string
    // 不做实际存储，为了同步给sku的限购数量
    onlyLimit?: number
}

export type OnlyPromotionItemType = {
    id: number
    shopId?: string
    shopName?: string
    onlyName: string
    onlyStatus?: OnlyStatusType
    joinMember: string[]
    payLimit?: PayLimitType
    startTime?: string
    endTime?: string
    // 活动商品数
    productNum: number
    // 参与人数
    peopleNum: number
    // 应收金额
    amountReceivable: number
    // 违规下架原因
    violationReason?: string
    // 订单关闭时间
    orderClosingTime: number
    deductionType: boolean
    applyTypes?: ApplyType[]
    onlyProducts: OnlyProductType[]
}

// 专享活动sku类 （price均✖️10000）
export type OnlyProductSkuType = {
    productId: string
    skuId: string
    skuName: string
    onlyPrice: number
    onlyStock: number
    onlyLimit: number
    actualPaidPrice: number
    skuStock: number
    stockType: StockType
    commission: number
}
