<template>
    <search @on-search-params="getSearch" @change-show="handleSearchShow" />
    <el-tabs v-model="currentTab" style="margin-top: 15px" @tab-change="handleTabClick">
        <el-tab-pane v-for="(item, key) in goodsStatus" :key="item" :label="key" :name="item"></el-tab-pane>
    </el-tabs>
    <q-table :data="tableList.goods" :selection="false" :style="{ height: tableHeight }" class="table">
        <q-table-column label="商品" align="left" width="300">
            <template #default="{ row }">
                <div class="commodity-info">
                    <el-image :src="row?.pic" :alt="row?.name" style="width: 68px; height: 68px; flex-shrink: 0" />
                    <div class="commodity-info__main">
                        <span class="commodity-info__main--name">{{ row?.name }}</span>
                        <span class="commodity-info__main--price">￥{{ salePriceRange(row?.salePrices) }}</span>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="状态" align="center" width="100">
            <template #default="{ row }">
                {{ ExamineGoodsEnum[row?.auditStatus as keyof typeof ExamineGoodsEnum] }}
            </template>
        </q-table-column>
        <q-table-column label="提交时间" align="center" prop="submitTime" width="150" />
        <q-table-column label="审核时间" align="center" prop="auditTime" width="150" />
        <q-table-column label="操作" align="center" width="250">
            <template #default="{ row }">
                <el-link type="primary" @click="handlePreviewExamineDetails(row)">查看</el-link>
                <el-link v-if="row?.auditStatus === 'REFUSE'" type="primary" @click="handleEditExamineGoods(row?.id)">编辑</el-link>
                <el-link v-else type="primary" @click="handleCopyExamineGoods(row?.id)">复制</el-link>
                <el-link v-if="row?.auditStatus === 'UNDER_REVIEW'" type="primary" @click="delCommodity(row?.id)">删除</el-link>
                <el-dropdown v-if="row?.auditStatus === 'REFUSE'" @command="handleCommand($event, row)">
                    <el-link type="primary">
                        <span style="flex-shrink: 0">更多</span>
                    </el-link>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="commit">提交审核</el-dropdown-item>
                            <el-dropdown-item command="reason">拒绝原因</el-dropdown-item>
                            <el-dropdown-item command="copy">复制</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </template>
        </q-table-column>
    </q-table>
    <PageManage v-model="tableList.page" load-init :total="tableList.total" @reload="initList" />
    <el-dialog v-model="previewVisible" title="商品详情" center width="900px" destroy-on-close>
        <CommodityDetails :commodity-id="commodityInfo.id" :shop-id="commodityInfo.shopId" />
        <el-space direction="vertical" alignment="flex-start">
            <span> 审核状态：{{ ExamineGoodsEnum[commodityInfo.auditStatus as keyof typeof ExamineGoodsEnum] }} </span>
            <span v-if="commodityInfo.explain">审核备注：{{ commodityInfo.explain || '' }}</span>
        </el-space>
    </el-dialog>
    <el-dialog v-model="showReasonDialog" title="拒绝理由" center destroy-on-close>
        <span>原因：{{ rejectReson }}</span>
    </el-dialog>
</template>

<script lang="ts" setup>
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import search from './components/search.vue'
import useExamineListHooks from './hooks/useExamineListHooks'
import PageManage from '@/components/pageManage/PageManage.vue'
import CommodityDetails from '../details/index.vue'
import usePreviewExamineDetails from './hooks/usePreviewExamineDetails'
import { ExamineGoodsEnum } from '../types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { doDeleteCommodity } from '@/apis/good'

const {
    getSearch,
    handleSearchShow,
    currentTab,
    goodsStatus,
    handleTabClick,
    tableHeight,
    tableList,
    salePriceRange,
    initList,
    handleCopyExamineGoods,
    handleEditExamineGoods,
    handleCommand,
    showReasonDialog,
    rejectReson,
} = useExamineListHooks()

const { commodityInfo, previewVisible, handlePreviewExamineDetails } = usePreviewExamineDetails()

const delCommodity = (id: any) => {
    ElMessageBox.confirm('确定需要删除商品？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
    }).then(async () => {
        let ids = [id as string]
        const { code, success, msg } = await doDeleteCommodity(ids.join(','))
        if (code === 200 && success) {
            ElMessage.success('删除成功')
            initList()
        } else {
            ElMessage.error(msg)
        }
    })
}
</script>

<style lang="scss" scoped>
@include b(commodity-info) {
    display: flex;
    @include e(main) {
        display: flex;
        flex-direction: column;
        margin-left: 8px;
        @include m(name) {
            font-weight: 600;
            height: 32px;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        @include m(price) {
            color: #ff7417;
            margin-top: 20px;
        }
    }
}
@include b(el-link) {
    margin-right: 8px;
}
@include b(table) {
    overflow: auto;
    transition: height 0.5s;
}
</style>
