<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 11:39:49
 * @LastEditors: lexy 
 * @LastEditTime: 2023-12-21 14:22:31
-->
<template>
    <div class="commodity">
        <div class="commodity__left">
            <el-image style="width: 68px; height: 68px" :src="pic" fit="fill"></el-image>
        </div>
        <div class="commodity__right">
            <el-tooltip :content="$props.info.productName" :teleported="false" effect="light">
                <div style="position: relative">
                    <div v-if="flag" class="commodity__right--name-id">
                        <el-text class="commodity__right--name">{{ $props.info.productName }}</el-text>
                        <el-text class="mx-1" size="small" @click="handleCopy($props.info.id)">
                            ({{ $props.info.id }} <el-icon><CopyDocument /></el-icon>)
                        </el-text>
                    </div>
                    <el-input v-else ref="inputRef" v-model="name" :maxlength="35" type="textarea" @blur="handleBlur" />
                    <div v-if="flag && !['PLATFORM_SELL_OFF', 'UNDER_REVIEW'].includes($props.info.status)" class="edit" @click="handleName">
                        <q-icon name="icon-bianji_o edit" color="#333" />
                    </div>
                </div>
            </el-tooltip>
            <div class="commodity__right--price-group">
                <div class="commodity__right--price">售价：￥{{ price }}</div>
                <div class="commodity__right--price">供货：￥{{ salePrice }}</div>
                <div class="commodity__right--price">佣金：￥{{ commission }}</div>
                <div v-if="!['PLATFORM_SELL_OFF', 'UNDER_REVIEW'].includes($props.info.status)" class="edit" @click="openEditPrice">
                    <q-icon name="icon-bianji_o edit" color="#333" />
                </div>
            </div>
            <el-tooltip v-if="$props.info.providerName" :teleported="false" :content="$props.info.providerName" effect="light">
                <div class="commodity__right--sup">供应商:{{ $props.info.providerName }}</div>
            </el-tooltip>
        </div>

        <el-dialog v-model="editPriceVisible" title="价格设置" center width="900px" destroy-on-close @close="handleClose">
            <template #default>
                <el-row :gutter="20" align="middle">
                    <el-col :span="6" style="text-align: center;bagc"> 批量设置 </el-col>
                    <el-col :span="8"> 划线价：<decimal-input v-model="allPrice" :max="999999" :min="0" :decimal-places="2" @blur="priceSkuItemRef?.setALLskyPrice('price', allPrice)" /> </el-col>
                    <el-col :span="7">
                        供货价：<decimal-input v-model="allSalePrice" :max="999999" :min="0" :decimal-places="2" @blur="priceSkuItemRef?.setALLskyPrice('salePrice', allSalePrice)" />
                    </el-col>
                </el-row>
                <price-sku-item ref="priceSkuItemRef" :skus="skus" />
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editPriceVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleEditPrice"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts" setup>
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { ElMessage } from 'element-plus'
import { doGetSingleCommodity, doGetCommoditySku, doUpdateCommodity, doNameUpdate, doPriceUpdate } from '@/apis/good'
import qIcon from '@/components/q-icon/q-icon.vue'
import priceSkuItem from './priceSkuItem.vue'
import { computed, ref, nextTick } from 'vue'
import type { PropType } from 'vue'
import { ApiCommodityType } from '../types'
import { CopyDocument } from '@element-plus/icons-vue'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
const $props = defineProps({
    info: {
        type: Object as PropType<ApiCommodityType>,
        default: null,
    },
})
const { divTenThousand, mulTenThousand } = useConvert()
const emit = defineEmits(['update-price', 'update-name'])
const salePrice = computed(() => {
    const tempArr = $props.info.storageSkus.map((item) => {
        return +item.salePrice
    })
    const min = Math.min(...tempArr)
    const max = Math.max(...tempArr)
    if (max === min) {
        return divTenThousand(max)
    } else {
        return `${divTenThousand(min)}~${divTenThousand(max)}`
    }
})
const price = computed(() => {
    const tempArr = $props.info.storageSkus.map((item) => {
        return +`${Number(item.commission) + Number(item.salePrice)}`
    })
    const min = Math.min(...tempArr)
    const max = Math.max(...tempArr)
    if (max === min) {
        return divTenThousand(max)
    } else {
        return `${divTenThousand(min)}~${divTenThousand(max)}`
    }
})
const commission = computed(() => {
    const tempArr = $props.info.storageSkus.map((item) => {
        return +item.commission
    })
    const min = Math.min(...tempArr)
    const max = Math.max(...tempArr)
    if (max === min) {
        return divTenThousand(max)
    } else {
        return `${divTenThousand(min)}~${divTenThousand(max)}`
    }
})
// 全部划线价
const allPrice = ref(0)
// 全部销售价
const allSalePrice = ref(0)

const priceSkuItemRef = ref<InstanceType<typeof priceSkuItem>>()

watch(
    () => $props.info.productName,
    (val) => {
        name.value = val
    },
)
const handleClose = () => {
    allPrice.value = 0
    allSalePrice.value = 0
}
// 获取当前商品详细信息
async function dataDisplay() {
    const { code, data } = await doGetSingleCommodity($props.info.id, useShopInfoStore().shopInfo.id)
    if (code !== 200) {
        ElMessage.error('获取商品信息失败')
        return
    }
    return data
}

// 获取当前商品sku
const getCommoditySku = async () => {
    const { code, data } = await doGetCommoditySku(useShopInfoStore().shopInfo.id, $props.info.id)
    if (code !== 200) {
        ElMessage.error('获取商品信息失败')
        return
    }
    data.skus.forEach((item: any) => {
        item.initStock = 0
        item.price = divTenThousand(item.price)
        item.salePrice = divTenThousand(item.salePrice)
        item.commission = divTenThousand(item.commission)
        item.retailPrice = divTenThousand(Number(item.commission) + Number(item.salePrice))
    })
    return data
}

// 修改名称
const flag = ref(true)

const name = ref($props.info.productName)
const inputRef = ref()

const handleName = () => {
    flag.value = false
    nextTick(() => {
        inputRef.value.focus()
    })
}

const handleBlur = async () => {
    try {
        if (!name.value) {
            name.value = $props.info.name
            flag.value = true
            return
        }
        const { code } = await doNameUpdate({ id: $props.info.id, name: name.value })
        if (code === 200) {
            flag.value = true
            emit('update-name', name.value)
        }
    } catch (error) {
        ElMessage.error('修改商品名称失败')
        return
    }
}

const skus = ref<any[]>([])

const openEditPrice = async () => {
    try {
        const data = await getCommoditySku()
        skus.value = data.skus
        editPriceVisible.value = true
    } catch (error) {
        ElMessage.error('获取商品信息失败')
    }
}

const editPriceVisible = ref(false)
const handleEditPrice = async () => {
    try {
        const skus = priceSkuItemRef.value?.itemSku
        if (!skus?.length) return
        for (const item of skus) {
            if (item.price < item.salePrice) {
                return ElMessage.error('划线价应大于等于供货价')
            }
        }
        const data = skus?.map((item) => ({
            skuId: item.id,
            price: mulTenThousand(item.price).toNumber(),
            salePrice: mulTenThousand(item.salePrice).toNumber(),
        }))
        const { code, msg } = await doPriceUpdate(data, skus[0]?.productId)
        if (code !== 200) return ElMessage.error(msg || '更新商品失败')
        ElMessage.success('更新成功')
        editPriceVisible.value = false
        emit('update-price', data, skus[0]?.productId)
    } catch (error) {
        ElMessage.error('更新失败')
    }
}

const pic = computed(() => {
    return $props.info.albumPics.split(',')[0]
})

/**
 * 复制文本
 * @param text 文本
 */
const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then((res) => {
        ElMessage.success('复制成功！')
    })
}
</script>
<style lang="scss">
@import '@/assets/css/mixins/mixins';

@include b(commodity) {
    @include flex(flex-start);
    font-size: 12px;
    text-align: left;
    width: 100%;

    @include e(left) {
        width: 68px;
        height: 68px;
        margin-right: 10px;
    }

    @include e(right) {
        margin-right: 10px;
        width: 200px;
        // flex: 1;
        @include m(name-id) {
            margin-bottom: 8px;
            font-weight: bold;
        }
        @include m(name) {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        @include m(price-group) {
            position: relative;
            width: fit-content;
            display: flex;
            flex-direction: column;
            align-content: flex-start;
            gap: 2px;
        }

        @include m(price) {
            color: #ff7417;
            margin-right: 20px;
        }

        @include m(sup) {
            width: 120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.edit {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    height: 20px;
    width: 20px;
    cursor: pointer;
}
</style>
