<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-25 10:42:46
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-18 17:20:20
-->
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { doGetinvoiceSettings, doPostinvoiceSettings } from '@/apis/invoice'

/*
 *variable
 */
const $shopInfoStore = useShopInfoStore()

const invoiceSetupValue = ref({
    id: '',
    shopId: $shopInfoStore.getterShopInfo.id,
    invoiceSettingsClientType: 'SUPPLIER',
    invoiceSetupValue: [
        {
            invoiceType: 'NO_INVOICE',
            orderCompleteDays: 0,
            option: true,
        },
    ],
})
const orderCompleteDays = ref({
    VAT_GENERAL: 0,
    VAT_SPECIAL: 0,
    VAT_COMBINED: 0,
})

/*
 *lifeCircle
 */

onMounted(() => {
    getinvoiceset()
})
/*
 *function
 */
async function getinvoiceset() {
    const { code, data, msg } = await doGetinvoiceSettings({ invoiceSettingsClientType: 'SUPPLIER', shopId: $shopInfoStore.getterShopInfo.id })
    if (code !== 200) {
        ElMessage.error(msg || '获取发票设置失败')
        return
    }
    if (data.id) {
        invoiceSetupValue.value = data
        orderCompleteDays.value[data.invoiceSetupValue[0].invoiceType] = data.invoiceSetupValue[0].orderCompleteDays
    }
}

/**
 * @LastEditors: lexy
 * @description: 保存
 * @returns
 */
const save = async () => {
    invoiceSetupValue.value.invoiceSetupValue[0].orderCompleteDays = orderCompleteDays.value[invoiceSetupValue.value.invoiceSetupValue[0].invoiceType]

    const { code, data, msg } = await doPostinvoiceSettings(invoiceSetupValue.value)
    if (code !== 200) {
        ElMessage.error(msg || '保存发票设置失败')
        return
    }
    ElMessage.success('保存成功')
}
</script>

<template>
    <div class="order__tip" style="display: flex; align-items: center; justify-content: space-between">
        <div>
            <div class="order__tip--lump"></div>
            <span class="order__tip--title">发票设置</span>
        </div>
        <div style="color: #fd0505">是否向消费者提供发票，提供发票请选择可提供发票的类型，最长可设置30天</div>
    </div>
    <el-radio-group v-model="invoiceSetupValue.invoiceSetupValue[0].invoiceType" style="margin-left: 50px">
        <el-radio value="NO_INVOICE" style="width: 600px; margin-bottom: 10px">不提供发票</el-radio>
        <el-radio value="VAT_GENERAL" style="width: 600px; margin-bottom: 10px"
            >增值税电子普通发票，订单完成
            <el-input-number v-model="orderCompleteDays.VAT_GENERAL" :step="1" step-strictly :max="30" :min="0" :controls="false" />
            天后，停止开票
        </el-radio>
        <el-radio value="VAT_SPECIAL" style="width: 600px; margin-bottom: 10px"
            >增值税电子专用发票，订单完成
            <el-input-number v-model="orderCompleteDays.VAT_SPECIAL" :step="1" step-strictly :max="30" :min="0" :controls="false" />
            天后，停止开票
        </el-radio>
        <el-radio value="VAT_COMBINED" style="width: 600px; margin-bottom: 10px"
            >增值税电子专用发票 + 增值税电子普通发票，订单完成
            <el-input-number v-model="orderCompleteDays.VAT_COMBINED" :step="1" step-strictly :max="30" :min="0" :controls="false" />
            天后，停止开票
        </el-radio>
    </el-radio-group>
    <el-button type="primary" round class="order__save" @click="save">保存</el-button>
</template>

<style lang="scss" scoped>
@import '@/assets/css/mixins/mixins.scss';

@include b(order) {
    @include e(tip) {
        vertical-align: center;
        background-color: rgba(246, 248, 250, 1);
        padding: 15px 15px 15px 30px;
        margin-bottom: 30px;

        @include m(title) {
            margin-left: 12px;
            color: #586884;
            font-weight: 700;
        }

        @include m(lump) {
            display: inline-block;
            width: 3px;
            height: 12px;
            background-color: rgba(255, 153, 0, 1);
        }
    }
    @include e(save) {
        margin-left: 100px;
        margin-top: 40px;
    }
}
</style>
