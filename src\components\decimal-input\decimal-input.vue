<template>
    <el-input 
        v-bind="$attrs" 
        :model-value="displayValue" 
        @input="handleInput"
        @blur="handleBlur"
    >
        <template v-for="(_, name) in $slots" #[name]="slotData">
            <slot :name="name" v-bind="slotData" />
        </template>
    </el-input>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

const props = defineProps<{
    modelValue?: string | number
    decimalPlaces?: number
    text?: string | number
    min?: number
    max?: number
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: number | null): void
    (e: 'change', value: number | null): void
}>()

const internalValue = ref<string>('')
const decimalPlaces = computed(() => props.decimalPlaces ?? 2)

const displayValue = computed(() => internalValue.value)

const handleInput = (value: string) => {
    // 只过滤输入字符，允许数字、一个小数点、开头的负号
    if (/^-?\d*\.?\d*$/.test(value)) {
        // 不做任何数值转换和校验，保持原始输入
        internalValue.value = value
    }
}

// 失焦时进行完整的数值校验和格式化
const handleBlur = () => {
    const value = internalValue.value
    
    // 处理空值或只有负号的情况
    if (!value || value === '-' || value === '.') {
        internalValue.value = ''
        emit('update:modelValue', null)
        emit('change', null)
        return
    }

    const numValue = Number(value)
    if (!isNaN(numValue)) {
        let finalValue = numValue
        
        // 范围限制
        if (props.min !== undefined) finalValue = Math.max(props.min, finalValue)
        if (props.max !== undefined) finalValue = Math.min(props.max, finalValue)
        
        // 格式化小数位数
        const formattedValue = decimalPlaces.value === 0
            ? Math.floor(finalValue).toString()
            : finalValue.toFixed(decimalPlaces.value)
            
        internalValue.value = formattedValue
        emit('update:modelValue', Number(formattedValue))
        emit('change', Number(formattedValue))
    }
}

// watch 处理外部值变更，进行完整校验
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue === null || newValue === '') {
            internalValue.value = props.text?.toString() || ''
            return
        }

        const numValue = Number(newValue)
        if (!isNaN(numValue)) {
            let value = numValue
            // 范围校验
            if (props.min !== undefined) value = Math.max(props.min, value)
            if (props.max !== undefined) value = Math.min(props.max, value)
            
            // 格式化小数位数
            const formattedValue = decimalPlaces.value === 0
                ? Math.floor(value).toString()
                : value.toFixed(decimalPlaces.value)
                
            internalValue.value = formattedValue
            if (value !== numValue) {
                emit('update:modelValue', Number(formattedValue))
            }
        } else {
            internalValue.value = ''
        }
    },
    { immediate: true }
)
</script>
