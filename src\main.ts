/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-18 16:50:10
 * @LastEditors: lexy
 * @LastEditTime: 2023-04-17 16:50:08
 */
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import '@/assets/css/base.scss'
import '@/assets/css/font/iconfont.scss'
import '@/assets/css/font/iconfont.js'
import { initPlugin } from '@/libs/plugin'
import { Search, Swipe, SwipeItem, Icon, Image as VanImage, CountDown } from 'vant'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'vant/lib/index.css'

// 按需导入需引入 需导入API组件样式
import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/message-box/style/css'
import 'element-plus/es/components/cascader/style/css'
import 'element-plus/es/components/notification/style/css'
import 'element-plus/es/components/loading/style/css'
import Global from '@/libs/global'
import { createPinia } from 'pinia'

const app = createApp(App)
const pinia = createPinia()
app.use(router)
    .use(pinia)
    .use(initPlugin)
    .use(Global)
    .use(ElementPlus, {
        locale: zhCn,
    })
    .use(Search)
    .use(Swipe)
    .use(SwipeItem)
    .use(VanImage)
    .use(CountDown)
    .use(Icon)
    .mount('#app')
