// directives/preview.ts
import { DirectiveBinding } from 'vue'
import { ElImageViewer } from 'element-plus'
import { createApp, h } from 'vue'

// 为了解决 TypeScript 错误，声明一个扩展的接口
interface PreviewHTMLElement extends HTMLElement {
    _observer?: MutationObserver
}

const preview = {
    mounted(el: PreviewHTMLElement, binding: DirectiveBinding) {
        // 处理图片预览逻辑
        const setupImagePreview = () => {
            const images = el.getElementsByTagName('img')
            const urls: string[] = []

            if (images.length === 0) {
                // 如果没有图片，设置一个短暂的延迟再次尝试
                setTimeout(setupImagePreview, 100)
                return
            }

            for (let index = 0; index < images.length; index++) {
                const img = images[index]
                if (img.src) {
                    urls.push(img.src)
                    img.style.cursor = 'pointer'

                    img.addEventListener('click', () => {
                        const container = document.createElement('div')
                        document.body.appendChild(container)

                        const app = createApp({
                            data() {
                                return {
                                    visible: true,
                                    index: urls.indexOf(img.src),
                                }
                            },
                            render() {
                                return h(ElImageViewer, {
                                    urlList: urls,
                                    initialIndex: this.index,
                                    teleported: true,
                                    onClose: () => {
                                        this.visible = false
                                        setTimeout(() => {
                                            app.unmount()
                                            container.remove()
                                        }, 200)
                                    },
                                })
                            },
                        })

                        app.mount(container)
                    })
                }
            }
        }

        // 初始化图片预览
        setupImagePreview()

        // 可选：使用 MutationObserver 监听 DOM 变化
        const observer = new MutationObserver(() => {
            setupImagePreview()
        })

        observer.observe(el, {
            childList: true,
            subtree: true,
        })

        // 在组件卸载时，清除 observer
        el._observer = observer
    },

    unmounted(el: PreviewHTMLElement) {
        // 清除 observer 避免内存泄漏
        if (el._observer) {
            el._observer.disconnect()
            delete el._observer
        }
    },
}

export default preview
