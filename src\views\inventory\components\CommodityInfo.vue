<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 11:39:49
 * @LastEditors: lexy 
 * @LastEditTime: 2023-12-21 14:22:10
-->
<template>
    <div class="commodity">
        <div class="commodity__left">
            <el-image style="width: 68px; height: 68px" :src="pic" fit="fill"></el-image>
        </div>
        <div class="commodity__right">
            <el-tooltip :content="$props.info.productName" :teleported="false" effect="light">
                <div style="position: relative">
                    <div v-if="flag" class="commodity__right--name">
                        {{ $props.info.productName }}
                    </div>
                    <el-input v-else ref="inputRef" v-model="name" maxlength="35" @blur="handleBlur" />
                    <div v-if="flag" class="edit" @click="handleName">
                        <q-icon name="icon-bianji_o edit" color="#333"></q-icon>
                    </div>
                </div>
            </el-tooltip>
            <div style="position: relative; margin-top: 8px">
                <el-tooltip class="commodity__right--price" :content="$props.info.id" placement="top">
                    <span style="color: #0f40f5; cursor: pointer" @click="copyOrderNo($props.info.id)"> SPUID</span>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { doNameUpdate } from '@/apis/good'
import qIcon from '@/components/q-icon/q-icon.vue'
import { computed, ref, nextTick, defineEmits } from 'vue'
import type { PropType } from 'vue'
import { ApiCommodityType } from '@/views/goods/types'
import { ElMessage } from 'element-plus'
import useClipboard from 'vue-clipboard3'

/*
 *variable
 */
const { toClipboard } = useClipboard()
const $props = defineProps({
    info: {
        type: Object as PropType<ApiCommodityType>,
        default: null,
    },
})
const emit = defineEmits(['update-name'])
const copyOrderNo = async (data: string) => {
    try {
        await toClipboard(data)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}

watch(
    () => $props.info.productName,
    (val) => {
        name.value = val
    },
)

// 修改名称
const flag = ref(true)

const name = ref($props.info.productName)
const inputRef = ref()

const handleName = () => {
    flag.value = false
    nextTick(() => {
        inputRef.value.focus()
    })
}

const handleBlur = async () => {
    try {
        if (!name.value) {
            name.value = $props.info.name
            flag.value = true
            return
        }
        const { code } = await doNameUpdate({ id: $props.info.id, name: name.value })
        if (code === 200) {
            flag.value = true
            emit('update-name', name.value)
        }
    } catch (error) {
        return
    }
}

const pic = computed(() => {
    return $props.info.albumPics.split(',')[0]
})
</script>
<style lang="scss">
@import '@/assets/css/mixins/mixins';
@include b(commodity) {
    @include flex();
    font-size: 12px;
    text-align: left;
    @include e(left) {
        width: 68px;
        height: 68px;
        margin-right: 10px;
    }
    @include e(right) {
        margin-right: 10px;
        width: 160px;
        @include m(name) {
            width: 130px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            font-weight: bold;
        }
        @include m(price) {
            color: #ff7417;
            width: 130px;
            margin: 10px 0;
        }
        @include m(sup) {
            width: 120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
.edit {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    height: 20px;
    width: 20px;
    cursor: pointer;
}
</style>
