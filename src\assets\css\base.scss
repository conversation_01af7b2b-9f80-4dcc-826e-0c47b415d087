@import "./variable.scss";
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html,
body,
#app {
    width: 100%;
    height: 100%;
    // font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-light",
    //   "Helvetica Neue", "Hiragino Sans GB", Helvetica, "Microsoft YaHei", Arial,
    //   sans-serif;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 14px;
    color: #000;
}

/*滚动条基础设置（必须）*/
div::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/*滚动条的滑块按钮*/
div::-webkit-scrollbar-thumb {
    background-color: rgba(50, 50, 50, 0.2);
    border-radius: 40px;
}

.el-dropdown-menu__item {
    padding: 3px 15px !important;
}
a {
    text-decoration: none;
}
