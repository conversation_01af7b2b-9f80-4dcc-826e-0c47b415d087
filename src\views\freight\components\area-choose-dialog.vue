<!--
 * @description: 区域选择弹窗
 * @Author: lexy
 * @Date: 2022-07-12 15:19:12
 * @LastEditors: lexy
 * @LastEditTime: 2022-07-14 09:51:32
-->
<script setup lang="ts">
import { computed, PropType } from 'vue'
import QAreaChoose from '@/components/q-area-choose/q-area-choose.vue'
/*
 *variable
 */
const $props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    allArea: {
        type: Map as PropType<Map<string, number>>,
        default() {
            return new Map()
        },
    },
    currentArea: {
        type: Map as PropType<Map<string, number>>,
        default() {
            return new Map()
        },
    },
})
const $emit = defineEmits(['update:modelValue', 'choose'])
const show = computed(() => {
    return $props.modelValue
})
let currentRowArea = null
let newAllArea = null
/*
 *lifeCircle
 */
/*
 *function
 */
const handleCloseDialog = () => {
    $emit('update:modelValue', false)
}
const handleConfirm = () => {
    $emit('update:modelValue', false)
    console.log({
        newAllArea,
        currentRowArea,
    })
    $emit('choose', {
        newAllArea,
        currentRowArea,
    })
}
/**
 * @LastEditors: lexy
 * @description: 筛选当前table行选中区域
 */
const filterNewArea = (oldMap: Map<string, number>, oldRawMap: Map<string, number>, newMap: Map<string, number>) => {
    // if (oldMap.size > newMap.size) {
    //     const tempMap = new Map()
    //     console.log('xiugai')
    //     // 取消区域选中 [北京，天津]
    //     for (let [name, value] of newMap.entries()) {
    //         if (!oldRawMap.has(name)) {
    //             tempMap.set(name, value)
    //         } else {
    //             tempMap.delete(name)
    //         }
    //     }
    //     console.log('tempMap', tempMap)
    //     return tempMap
    // } else {
    //     const tempMap = new Map([...oldRawMap])
    //     console.log('xinzeng')
    //     // 新增区域选中
    //     for (let [name, value] of newMap.entries()) {
    //         if (!oldMap.has(name)) {
    //             tempMap.set(name, value)
    //         }
    //     }
    //     return tempMap
    // }
    // if(oldRawMap.size===0){}
}
const handleChoosedArea = (e) => {
    currentRowArea = filterNewArea($props.allArea, $props.currentArea, e)
    newAllArea = e
}
</script>

<template>
    <el-dialog v-model="show" @close="handleCloseDialog">
        <QAreaChoose :all-area="$props.allArea" :current-area="$props.currentArea" @choose="handleChoosedArea" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCloseDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped></style>
