<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-25 09:44:47
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-29 09:53:48
-->
<template>
    <div class="settings-tab">
        <el-tabs v-model="currentTab">
            <el-tab-pane label="店铺设置" name="ShopSet"></el-tab-pane>
            <el-tab-pane label="发票设置" name="InvoiceSet"></el-tab-pane>
            <el-tab-pane label="类目设置" name="CategorySet"></el-tab-pane>
            <!-- <el-tab-pane label="微信特约商户设置" name="WechatSpecialMerchantSet"></el-tab-pane> -->
        </el-tabs>
        <component :is="dynamicComponent[currentTab]"></component>
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, ref } from 'vue'
const dynamicComponent = reactive({
    ShopSet: defineAsyncComponent(() => import('./components/ShopSet.vue')),
    InvoiceSet: defineAsyncComponent(() => import('./components/InvoiceSet.vue')),
    CategorySet: defineAsyncComponent(() => import('./components/CategorySet.vue')),
    // WechatSpecialMerchantSet: defineAsyncComponent(() => import('./components/WechatSpecialMerchantSet.vue')),
})
const currentTab = ref('ShopSet')
</script>

<style scoped lang="scss">
.settings-tab {
    width: 100%;
}
</style>
