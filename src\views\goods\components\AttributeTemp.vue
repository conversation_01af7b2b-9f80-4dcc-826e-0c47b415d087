<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 14:58:55
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-10 14:21:28
-->
<template>
    <div class="page">
        <div v-if="hasList" class="emptyLine">暂无数据~</div>
        <div v-for="(item, index) in attributeList" v-else :key="index" class="tem__list table-border">
            <div class="tem__list--column">
                <div class="table-head-color tem__list--column--head">属性模板名称</div>
                <div style="background-color: #f6f7fb" class="tem__list--column--body table-border-top">
                    {{ item.name }}
                </div>
            </div>
            <div class="table-border-left tem__list--right">
                <div class="table-head-color tem__list--column--head">
                    <div class="tem__list--column--head--name">属性名称</div>
                    <div class="tem__list--column--head--par">属性特征</div>
                    <div class="tem__list--column--head--do">
                        <div class="link" @click="editAttributeTemplate(2, item)">编辑</div>
                        <div class="link" @click="deleteAttribute(item)">删除</div>
                    </div>
                </div>
                <div v-for="(listItem, listIndex) in item.attributeTemplates" :key="listIndex" class="table-border-top tem__list--navList">
                    <div class="tem__list--navList--param">{{ listItem.name }}</div>
                    <div class="tem__list--right">{{ listItem.content }}</div>
                    <div class="tem__list--right--icon">
                        <el-icon @click="deleteAttributeList(index, listIndex)"><i-ep-circleCloseFilled /></el-icon>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog v-model="dialogVisible" :title="editFlag ? '编辑商品属性模板' : '新增商品属性模板'" width="560px" :before-close="cancelAttributeTemplate" @close="editFlag = false">
            <el-form ref="attributeTemplateRef" :model="attributeTemplate" :rules="attributeTemplateRules">
                <el-form-item label="模板名称" prop="name">
                    <el-input v-model="attributeTemplate.name" style="width: 40%" :maxlength="15" placeholder="请输入模板名称"></el-input>
                </el-form-item>
                <el-form-item label="" prop="attributeTemplates">
                    <div class="attribute__table--head table-head-color">
                        <div style="width: 34%; margin-left: 20px">属性名称</div>
                        <div style="width: 50%">属性特征</div>
                        <div style="width: 60px"></div>
                    </div>
                    <div v-for="(item, index) in attributeTemplate.attributeTemplates" :key="index" class="table-head-color attribute__table--body">
                        <div class="attribute__table--body--item">
                            <div style="width: 50%">
                                <el-input v-model="item.name" maxlength="8" style="width: 80%" placeholder="请输入属性名称"></el-input>
                            </div>
                            <div style="width: 50%">
                                <el-input v-model="item.content" maxlength="30" style="width: 80%" placeholder="请输入属性特征"></el-input>
                            </div>
                            <div class="tem__list--right" style="width: 80px; text-align: right">
                                <el-icon @click="deleteParmList(index)"><i-ep-circleCloseFilled /></el-icon>
                            </div>
                        </div>
                    </div>
                    <div>
                        <el-button link type="primary" @click="addParmList">新增</el-button>
                    </div>
                </el-form-item>
            </el-form>
            <div style="text-align: center">
                <el-button @click="cancelAttributeTemplate">取 消</el-button>
                <el-button type="primary" @click="saveAttributeTemplate('attributeTemplate')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
    <PageManage
        :page-size="pageConfig.pageSize"
        :page-num="pageConfig.pageNum"
        :total="pageConfig.total"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
    ></PageManage>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, defineExpose, watch } from 'vue'
import { getAttsList, delAttr, delAttrSon, addAttrList, updateAttrList } from '@/apis/good'
import PageManage from '@/components/PageManage.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
const editFlag = ref(false)
const dialogVisible = ref(false)
const pageConfig = reactive({
    pageSize: 20,
    pageNum: 1,
    total: 0,
})

const hasList = ref(false)

const attributeList = ref([])

const attributeTemplate = ref({
    name: '',
    content: '',
    id: '',
    parentId: '0',
    attributeTemplates: [
        {
            content: '',
            name: '',
            id: '',
            parentId: '1',
        },
    ],
})
const attributeTemplateRef = ref()

const cusRule = (rule: any, value: any, callback: any) => {
    const tempArr = attributeTemplate.value.attributeTemplates.filter((item) => {
        return item.content && item.name
    })
    if (tempArr.length === attributeTemplate.value.attributeTemplates.length) {
        callback()
    } else {
        callback(new Error('属性名称与特征不能为空'))
    }
}

const attributeTemplateRules = reactive<FormRules>({
    name: [{ required: true, trigger: 'blur', message: '模板名称不能为空' }],
    attributeTemplates: [
        {
            required: true,
            trigger: 'blur',
            validator: cusRule,
        },
    ],
})

/**
 * 获取属性模板
 */
const getAttList = () => {
    const param = {
        current: pageConfig.pageNum,
        size: pageConfig.pageSize,
    }
    getAttsList(param).then((res) => {
        attributeList.value = res.data.records
        hasList.value = res.data.records.length === 0 ? true : false
        pageConfig.total = res.data.total
    })
}

onMounted(() => {
    getAttList()
})

/**
 * @method editParmList
 * @description 编辑模板
 */
const editAttributeTemplate = (type, item) => {
    dialogVisible.value = true
    attributeTemplate.value = JSON.parse(JSON.stringify(item))
    editFlag.value = type === 2 ? true : false
}

/**
 * 删除列表中属性
 */
const deleteAttributeList = (index, listIndex) => {
    ElMessageBox.confirm('确定要删除选中属性模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        const attributeTemplate = JSON.parse(JSON.stringify(attributeList.value[index]))
        const ids = attributeTemplate.attributeTemplates[listIndex].id
        delAttrSon(ids)
            .then(() => {
                ElMessage.success('删除成功')
                getAttList()
            })
            .catch((err) => {
                console.log(err)
            })
    })
}

/**
 * 删除属性模板
 */
const deleteAttribute = (item) => {
    ElMessageBox.confirm('确定要删除选中属性模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        delAttr(item.id)
            .then(() => {
                ElMessage.success('删除成功')
                getAttList()
            })
            .catch((err) => {
                console.log(err)
                ElMessage.error(err)
            })
    })
}

/**
 * 添加模板名称
 */
const addParmList = () => {
    const list = attributeTemplate.value.attributeTemplates
    list.push({
        content: '',
        name: '',
        id: '',
        parentId: '1',
    })
}

/**
 * 删除属性名称
 */
const deleteParmList = (index) => {
    if (attributeTemplate.value.attributeTemplates.length > 1) {
        attributeTemplate.value.attributeTemplates.splice(index, 1)
    } else {
        ElMessage.error('不能删除最后一条数据')
    }
}

/**
 * 取消操作
 */
const cancelAttributeTemplate = () => {
    ElMessageBox.confirm('确定要退出编辑属性列表页面? 退出后，未保存的信息将不会保留!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        dialogVisible.value = false
    })
}

/**
 * 保存模板
 */
const saveAttributeTemplate = async (formName) => {
    const formNameValid = attributeTemplateRef.value
    await formNameValid.validate((valid) => {
        if (!valid) return
        if (editFlag.value) {
            attributeTemplate.value.attributeTemplates.forEach((item) => {
                item.parentId = attributeTemplate.value.id
            })
            updateAttrList(attributeTemplate.value)
                .then((res) => {
                    if (res.code === 200) {
                        ElMessage.success('编辑成功')
                        getAttList()
                        dialogVisible.value = false
                    }
                })
                .catch((err) => {
                    ElMessage.error(`${err}`)
                })
        } else {
            addAttrList(attributeTemplate.value)
                .then((res) => {
                    if (res.code === 200) {
                        ElMessage.success('添加成功')
                        getAttList()
                        dialogVisible.value = false
                    }
                })
                .catch((err) => {
                    ElMessage.error(`${err.msg}`)
                })
        }
    })
}

/**
 * @method handleSizeChange
 * @description 每页 条
 */
const handleSizeChange = (val) => {
    pageConfig.pageSize = val
    getAttList()
}

/**
 * @method handleCurrentChange
 * @description 当前页
 */
const handleCurrentChange = (val) => {
    pageConfig.pageNum = val
    getAttList()
}
defineExpose({
    dialogVisible,
    attributeTemplate,
})
</script>

<style lang="scss" scoped>
@import '@/assets/css/goods/index.scss';

.noList {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #3d3d3d;
    width: 100%;
}

.page {
    height: calc(100vh - 220px);
    overflow: auto;
    margin-left: -15px;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}

.notemp {
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.tem__list {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    &--column {
        width: 180px;
        display: flex;
        flex-direction: column;
    }
    &--column--head {
        display: flex;
        justify-content: center;
        padding-top: 20px;
        padding-bottom: 20px;
        &--name {
            padding-left: 20px;
            padding-right: 20px;
            width: 40%;
        }
        &--par {
            flex: auto;
        }
        &--do {
            width: 120px;
            // padding-left: 20px;
            padding-right: 20px;
            text-align: right;
            display: flex;
            cursor: pointer;
            justify-content: space-around;
        }
    }
    &--column--body {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        flex: auto;
    }
    &--navList {
        display: flex;
        line-height: 44px;
        &--param {
            width: 40%;
            padding-left: 20px;
            padding-right: 20px;
        }
    }
    &--right {
        flex: auto;
        &--icon {
            flex: none;
            width: 120px;
            padding-left: 20px;
            padding-right: 20px;
            text-align: right;
        }
    }
}

.attribute__table--head {
    display: flex;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin-bottom: 10px;
}

.attribute__table--body {
    padding: 8px 10px;
    // display: flex;
    &--item {
        display: flex;
        align-items: center;
    }
}

.emptyLine {
    width: 100%;
    height: 80px;
    background-color: white;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    margin-top: 15px;
    font-size: 14px;
    color: #b3b3b3;
    border-bottom: 1px solid #ebeef5;
    border-top: 1px solid #ebeef5;
}
.link {
    color: #53a8ff;
    cursor: pointer;
}
::v-deep .el-form-item__content {
    display: block;
}
</style>
