/*
 * @description:
 * @Author: lexy
 * @Date: 2023-03-15 09:53:58
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-15 16:06:44
 */
import Mock from 'mockjs'
export const groupModel = function (current: number, size: number) {
    const template = {
        current,
        size,
        total: 100,
    }
    template[`records|${size}`] = [
        {
            id: '@id',
            status: Mock.Random.pick(['OPENING', 'PREHEAT', 'OPEN', 'FINISHED', 'VIOLATION']),
            name: '@word',
            startDate: '@datetime("yyyy-MM-dd  HH:mm:ss")',
            endDate: '@datetime("yyyy-MM-dd  HH:mm:ss")',
            'productNum|1-10': 10,
            'users|1-10': 10,
            'orders|1-10': 10,
            'amount|1-1000': 1000,
        },
    ]
    return Mock.mock(template)
}
export const groupOrderModel = function (current: number, size: number) {
    const template = {
        current,
        size,
        total: 100,
    }
    template[`records|${size}`] = [
        {
            activityId: '@id',
            teamNo: '@id',
            orderNo: '@id',
            commanderId: '@id',
            commanderAvatar: Mock.Random.image('36x36', '#FF6600', '团长头像'),
            commanderNickname: '@first',
            productName: '@word',
            productImage: Mock.Random.image('36x36', '#FF6600', '团长头像'),
            openTime: '@datetime("yyyy-MM-dd  HH:mm:ss")',
            'currentNum|1-10': 10,
            'totalNum|1-10': 10,
            'amount|1-1000': 1000,
            status: Mock.Random.pick(['ING', 'SUCCESS', 'FAIL']),
        },
    ]
    return Mock.mock(template)
}
