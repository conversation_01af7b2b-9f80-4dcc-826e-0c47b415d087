<template>
    <div class="form">
        <el-form class="form-flex">
            <MCard v-model="showCard">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="数据类型" label-width="90px">
                            <el-select v-model="searchFromData.dataType" placeholder="请选择数据类型">
                                <el-option v-for="(item, index) in dataTypeList" :key="index" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="状态" label-width="90px">
                            <el-select v-model="searchFromData.status" placeholder="请选择状态">
                                <el-option v-for="(item, index) in statusOptions" :key="index" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="导出用户" label-width="90px">
                            <el-input v-model="searchFromData.userPhone" placeholder="请输入导出用户" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item label="导出时间" label-width="90px">
                            <el-date-picker
                                v-model="searchFromData.exportDate"
                                type="datetimerange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button class="form__btn" type="primary" round @click="handleSearch">搜索</el-button>
                        <el-button class="form__btn" round @click="hanndleReset">重置</el-button>
                    </el-col>
                </el-row>
            </MCard>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { DATA_TYPE_ENUM, EXPORT_STATUS_ENUM } from './types'
import { cloneDeep } from 'lodash'

const showCard = ref(false)
const $emit = defineEmits(['search', 'changeShow'])
const searchFromData = reactive({
    dataType: '' as keyof typeof DATA_TYPE_ENUM | '',
    status: '' as keyof typeof EXPORT_STATUS_ENUM | '',
    exportDate: '',
    userPhone: '',
})
const dataTypeList = ref<{ value: keyof typeof DATA_TYPE_ENUM; label: string }[]>([])
const statusOptions = ref<{ value: keyof typeof EXPORT_STATUS_ENUM; label: string }[]>([])
const initialDataTypeList = () => {
    dataTypeList.value = []
    for (const dataType in DATA_TYPE_ENUM) {
        const dataTypeStr = dataType as keyof typeof DATA_TYPE_ENUM
        dataTypeList.value.push({ value: dataTypeStr, label: DATA_TYPE_ENUM[dataTypeStr] })
    }
}
initialDataTypeList()

const initialStatusOptions = () => {
    statusOptions.value = []
    for (const status in EXPORT_STATUS_ENUM) {
        const statusStr = status as keyof typeof EXPORT_STATUS_ENUM
        statusOptions.value.push({ value: statusStr, label: EXPORT_STATUS_ENUM[statusStr] })
    }
}
initialStatusOptions()

const handleSearch = () => {
    const cloneSearchForm: any = cloneDeep(searchFromData)
    if (Array.isArray(searchFromData.exportDate)) {
        cloneSearchForm.exportStartDate = searchFromData.exportDate?.[0]
        cloneSearchForm.exportEndDate = searchFromData.exportDate?.[1]
    }
    delete cloneSearchForm.exportDate
    $emit('search', cloneSearchForm)
}
const hanndleReset = () => {
    // @ts-ignore
    Object.keys(searchFromData).forEach((key) => (searchFromData[key] = ''))
    handleSearch()
}

watch(
    () => showCard.value,
    (val) => $emit('changeShow', val),
)
</script>
