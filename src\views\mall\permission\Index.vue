<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-25 00:00:56
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-09 19:19:09
-->
<!--
 * @description: 权限设置
 * @Author: lexy
-->
<template>
    <div class="permission-tab">
        <el-tabs v-model="currentTab">
            <el-tab-pane label="管理员列表" name="Admin" lazy>
                <admin-list />
            </el-tab-pane>
            <el-tab-pane label="角色管理" name="ROLE" lazy>
                <role-list />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup lang="ts">
import AdminList from './AdminList.vue'
import RoleList from './RoleList.vue'
import { ref } from 'vue'

const currentTab = ref('Admin')
</script>

<style scoped lang="scss">
.permission-tab {
    width: 100%;
}
</style>
