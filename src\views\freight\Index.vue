<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-09 10:26:14
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-08 17:54:46
-->
<script setup lang="ts">
import { ref, defineAsyncComponent, shallowRef } from 'vue'
/*
 *variable
 */
// 动态组件列表
const reactiveComponent = shallowRef({
    freightTemplate: defineAsyncComponent(() => import('./components/freight-template.vue')),
    freightAdd: defineAsyncComponent(() => import('./components/freight-add.vue')),
    freightServe: defineAsyncComponent(() => import('./components/freight-serve.vue')),
    freightPrint: defineAsyncComponent(() => import('./components/freight-print.vue')),
    freightSet: defineAsyncComponent(() => import('./components/freight-set.vue')),
})
// tabber状态
const activeName = ref<'freightTemplate' | 'freightAdd' | 'freightServe' | 'freightPrint' | 'freightSet'>('freightTemplate')
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <!-- 导航栏 -->
    <el-tabs v-model="activeName">
        <el-tab-pane label="运费模板" name="freightTemplate"></el-tab-pane>
        <el-tab-pane label="地址管理" name="freightAdd"></el-tab-pane>
        <el-tab-pane label="物流服务" name="freightServe"></el-tab-pane>
        <!-- <el-tab-pane label="物流设置" name="freightSet"></el-tab-pane> -->
        <el-tab-pane label="打印设置" name="freightPrint"></el-tab-pane>
    </el-tabs>
    <component :is="reactiveComponent[activeName]"></component>
</template>

<style lang="scss" scoped></style>
