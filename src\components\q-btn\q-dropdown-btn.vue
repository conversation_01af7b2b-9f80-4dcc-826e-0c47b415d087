<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 13:18:36
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-09 15:10:08
-->
<script setup lang="ts">
import { ref, PropType } from 'vue'
export type CommondType = Record<'name' | 'label', string>
/*
 *variable
 */
const $emit = defineEmits(['rightClick', 'leftClick'])
const $props = defineProps({
    title: {
        type: String,
        default: '审核',
    },
    option: {
        type: Array as PropType<CommondType[]>,
        default() {
            return [{ label: '暂无操作', name: 'default' }]
        },
    },
    bgColor: { type: String, default: '#eaf5fe' },
    color: { type: String, default: '#fff' },
    size: { type: String as PropType<'large ' | 'default' | 'small'>, default: 'large' },
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <el-button-group>
        <el-button type="primary" round @click="$emit('leftClick')">{{ $props.title }}</el-button>
        <el-button type="primary" round>
            <el-dropdown @command="$emit('rightClick', $event)">
                <span class="el-dropdown-link"> 更多设置 </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item v-for="(item, index) in $props.option" :key="index" :command="item.name">{{ item.label }}</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </el-button>
    </el-button-group>
</template>

<style scoped lang="scss">
.el-dropdown-link {
    color: #fff;
    outline: unset;
}
</style>
