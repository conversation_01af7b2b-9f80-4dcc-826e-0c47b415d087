<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-01 16:17:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-03 11:44:53
-->
<template>
    <el-header>
        <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            :prefix-icon="Search"
            clearable
            @keyup.enter="inputChange"
            @clear="inputChange"
            @blur="inputBlur"
            @focus="inputFocus"
            @input="onInput"
        />
    </el-header>
</template>
<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'

const emits = defineEmits(['keywordChange', 'searchFocus'])
const searchKeyword = ref('')

const onInput = (value: string) => {
    !value && inputChange()
}

const inputChange = () => {
    const keyword = searchKeyword.value
    emits('keywordChange', keyword)
}
const inputBlur = () => {
    emits('searchFocus', false)
}
const inputFocus = () => {
    emits('searchFocus', true)
}
</script>
<style scoped>
.el-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--el-border-color);
}
</style>
