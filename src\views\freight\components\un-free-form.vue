<!--
 * @description: 不包邮
 * @Author: lexy
 * @Date: 2022-09-15 16:19:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-31 14:06:03
-->
<script setup lang="ts">
import { ref, PropType, computed } from 'vue'
import AreaChoose from '@/components/q-area-choose/area-choose.vue'
import { ElMessage } from 'element-plus'
import { useVModel } from '@vueuse/core'
import uuid from '@/utils/uuid'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
interface ChooseAreaItem {
    upperCode: string
    upperName: string
    length: number
    lowerName: string[]
    lowerCode: string[]
}
/*
 *variable
 */
const $props = defineProps({
    tableData: {
        type: Array as PropType<
            {
                id: string
                firstAmount: number
                firstQuantity: string
                logisticsId: number
                regionJson: any[]
                secondAmount: number
                secondQuantity: string
                valuationModel: string
            }[]
        >,
        // eslint-disable-next-line vue/require-valid-default-prop
        default: [
            {
                id: uuid(10),
                firstAmount: 0, //第一个金额
                firstQuantity: '0', //第一数量
                logisticsId: 0, //物流编号
                regionJson: [], // 地区
                secondAmount: 0, // 第二个金额
                secondQuantity: '0', // 第二个数量
                valuationModel: 'PKGS', // 计价模式
            },
        ],
    },
    isPKGS: { type: Boolean, required: true },
})
const emit = defineEmits(['update:tableData'])
const _tableData = useVModel($props, 'tableData', emit)
const freeDialogTag = ref(false)
// 记录选择区域操作下标
const currentId = ref('0')
/*
 *lifeCircle
 */
/*
 *function
 */

const handleAddRegion = (id: string) => {
    freeDialogTag.value = true
    currentId.value = id
}
// 当前选中区域数组
const unFreeCurArea = computed<ChooseAreaItem[]>(() => {
    return _tableData.value.find((item) => item.id === currentId.value)?.regionJson || []
})
// 所有选中区域
const unFreeAllCurArea = computed<ChooseAreaItem[]>(() => {
    const tableList = _tableData.value
    return tableList.map((item) => item.regionJson).flat(1)
})
/**
 * @LastEditors: lexy
 * @description: 区域选择回调
 */
const handleChangeArea = (e: ChooseAreaItem[]) => {
    _tableData.value.find((item) => item.id === currentId.value).regionJson = e
    // _tableData.value[currentId.value].regionJson = e
}
/**
 * @LastEditors: lexy
 * @description: 获取选中类名
 */
const getAreaName = (data: ChooseAreaItem[]) => {
    console.log('da获取选中类名ta', data)
    return data
        .map((item) => {
            if (item.lowerCode.length === item.length) {
                return item.upperName
            }
            return `${item.upperName}(${item.lowerCode.length}/${item.length})`
        })
        .join(',')
}
/**
 * @LastEditors: lexy
 * @description:增加 / 删除 某一项基本物流基本信息
 * @param {*} index
 * @returns {*}
 */
const HandleFirstTabItem = (index: number, row) => {
    if (index === -1) {
        const item = {
            id: uuid(10),
            firstAmount: 0, //第一个金额
            firstQuantity: '0', //第一数量
            logisticsId: 0, //物流编号
            regionJson: [], // 地区
            secondAmount: 0, // 第二个金额
            secondQuantity: '0', // 第二个数量
            valuationModel: 'PKGS', // 计价模式
        }
        _tableData.value.push(item)
    } else {
        if (index === 0) return ElMessage.error('至少保留一个配送区域')
        _tableData.value = _tableData.value.filter((item) => item.id !== row.id)
    }
}
</script>

<template>
    <el-table :cell-style="{ padding: '15px 0' }" :data="_tableData" :header-cell-style="{ fontSize: '14px', color: '#333' }">
        <el-table-column prop="regionJson" label="选择区域">
            <template #default="{ row }">
                <div v-if="row.regionJson.length" @click="handleAddRegion(row.id)">{{ getAreaName(row.regionJson) }}</div>
                <el-button type="primary" link @click="handleAddRegion(row.id)">
                    {{ row.regionJson.length ? '编辑' : '添加区域' }}
                </el-button>
            </template>
        </el-table-column>
        <el-table-column prop="firstQuantity" :label="$props.isPKGS ? '首件数（件）' : '首重（kg）'">
            <template #default="{ row }">
                <decimal-input v-model="row.firstQuantity" class="number-input" :min="0" :decimal-places="$props.isPKGS ? 0 : 1" :input-style="{ textAlign: 'center' }" />
            </template>
        </el-table-column>
        <el-table-column prop="firstAmount" label="首费（元）">
            <template #default="{ row }">
                <decimal-input v-model="row.firstAmount" class="number-input" :min="0" :decimal-places="2" :input-style="{ textAlign: 'center' }" />
            </template>
        </el-table-column>
        <el-table-column prop="secondQuantity" :label="$props.isPKGS ? '续件数（件）' : '续重（kg）'">
            <template #default="{ row }">
                <decimal-input v-model="row.secondQuantity" class="number-input" :min="0" :decimal-places="$props.isPKGS ? 0 : 1" :input-style="{ textAlign: 'center' }" />
            </template>
        </el-table-column>
        <el-table-column prop="secondAmount" label="续费（元）">
            <template #default="{ row }">
                <decimal-input v-model="row.secondAmount" class="number-input" :min="0" :decimal-places="2" :input-style="{ textAlign: 'center' }" />
            </template>
        </el-table-column>
        <el-table-column prop="name" label="操作" min-width="50%">
            <template #default="{ row, $index }">
                <div class="dialogTab_container__right_btn">
                    <el-button v-if="_tableData.length - 1 === $index" type="primary" link @click="HandleFirstTabItem(-1, row)">添加</el-button>
                    <el-button type="danger" :disabled="_tableData.length === 1" link @click="HandleFirstTabItem(_tableData.length - 1, row)">删除</el-button>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <AreaChoose v-model:show="freeDialogTag" :current-choose="unFreeCurArea" :all-choose-arr="unFreeAllCurArea" @change="handleChangeArea" />
</template>

<style scoped lang="scss">
@include b(number-input) {
    width: 80px;
}
@include b(dialogTab_container) {
    @include e(input) {
        text-align: center;
    }
    @include e(right_btn) {
        display: flex;
        justify-content: space-evenly;
    }
}
</style>
