<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-28 09:42:33
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-31 14:13:02
-->
<script setup lang="ts">
import { PropType } from 'vue'
import { REGEX } from '@/constant'
/*
 *variable
 */
const $props = defineProps({
    remarkKey: { type: [String, Number], required: true },
    remark: { type: Object as PropType<{ [x: string]: string }>, required: true },
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <div v-if="$props.remarkKey !== 'newOrderNotify'" class="remark-view">
        <text class="remark-view--key">备注：</text>
        <div v-if="REGEX.HTTP_URL.test(remark[$props.remarkKey])" class="remark-view--image">
            <el-image style="width: 40px; height: 40px" :src="remark[$props.remarkKey]" :preview-src-list="[remark[$props.remarkKey]]" fit="cover" />
        </div>
        <span v-else class="remark-view--remarkKey"> {{ remark[$props.remarkKey] }}</span>
    </div>
</template>

<style scoped lang="scss">
@include b(remark-view) {
    display: flex;
    align-items: center;
}
</style>
