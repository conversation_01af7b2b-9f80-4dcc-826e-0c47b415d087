import { doGetExamineGoodsList, doPutReviewGoodsInfo } from '@/apis'
import useExamineRejectReason from './useExamineRejectReason'
import { ElMessage, ElMessageBox } from 'element-plus'

const useExamineListHooks = () => {
    const { showReasonDialog, rejectReson, handleShowRejectReason } = useExamineRejectReason()
    const $router = useRouter()
    const searchParams = reactive({
        name: '',
        platformCategoryId: '',
        sellType: '',
        secondPlatformCategoryId: '',
    })
    const currentTab = ref<'' | 'ALREADY_PASSED' | 'UNDER_REVIEW' | 'REFUSE'>('')
    const tableHeight = ref('calc(100vh - 300px)')

    const initList = async () => {
        let goodsList = [],
            total = 0
        try {
            const result = await doGetExamineGoodsList({ ...searchParams, ...tableList.page, productAuditStatus: currentTab.value })
            if (result.code === 200) {
                goodsList = result.data.records
                total = result.data.total
            }
        } finally {
            tableList.goods = goodsList
            tableList.total = total
        }
    }

    const salePriceRange = computed(() => (salePrices: string[] = []) => {
        const min = Math.min(...salePrices.map((item) => parseInt(item))) / 10000
        const max = Math.max(...salePrices.map((item) => parseInt(item))) / 10000
        if (max === min) {
            return max
        } else {
            return `${min}-${max}`
        }
    })
    const getSearch = (e: typeof searchParams) => {
        Object.keys(searchParams).forEach((key) => (searchParams[key] = e[key]))
        initList()
    }
    const goodsStatus = {
        全部: '',
        待审核: 'UNDER_REVIEW',
        已通过: 'ALREADY_PASSED',
        已拒绝: 'REFUSE',
    }

    const handleTabClick = () => {
        tableList.page.current = 1
        initList()
    }

    const handleSearchShow = (e: boolean) => {
        if (e) {
            tableHeight.value = 'calc(100vh - 440px)'
        } else {
            tableHeight.value = 'calc(100vh - 300px)'
        }
    }
    const tableList = reactive({
        page: { size: 10, current: 1 },
        goods: [],
        total: 0,
    })
    initList()
    const handleEditExamineGoods = (id: string) => {
        $router.push({
            path: '/goods/list/edit',
            query: {
                id,
            },
        })
    }
    const handleCopyExamineGoods = (id: string) => {
        $router.push({
            path: '/goods/list/edit',
            query: {
                id,
                isCopy: 'true',
            },
        })
    }
    const handleCommand = (command: string, row: any) => {
        console.log('handleCommand', command, row)
        switch (command) {
            case 'copy':
                handleCopyExamineGoods(row?.id)
                break
            case 'reason':
                handleShowRejectReason(row?.explain)
                break
            case 'commit':
                handleReCommitExamineGood(row?.id)
                break
            default:
                break
        }
    }
    const handleReCommitExamineGood = (id: string) => {
        ElMessageBox.confirm('请确认是否提交商品审核 ？', '请确认').then(async () => {
            const { code, msg } = await doPutReviewGoodsInfo(id)
            if (code === 200) {
                ElMessage.success({ message: msg || '提交成功' })
                initList()
            } else {
                ElMessage.error({ message: msg || '提交失败' })
            }
        })
    }
    return {
        tableHeight,
        getSearch,
        handleSearchShow,
        currentTab,
        goodsStatus,
        handleTabClick,
        tableList,
        salePriceRange,
        initList,
        handleEditExamineGoods,
        handleCopyExamineGoods,
        handleCommand,
        showReasonDialog,
        rejectReson,
    }
}

export default useExamineListHooks
