<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-01 16:17:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-13 13:53:45
-->
<template>
    <m-layout>
        <template #breadcrumb><m-breadcrumb /> </template>
        <template #aside>
            <navigate />
        </template>
        <template #view>
            <router-view></router-view>
        </template>
    </m-layout>
</template>

<script lang="ts" setup>
import MBreadcrumb from './MBreadcrumb.vue'
import MLayout from './MLayout.vue'
import Navigate from './Navigate.vue'
</script>
<style lang="scss">
.lineFlex {
    display: flex;
    align-items: center;
    height: 60px;
    cursor: pointer;
    outline: none;
}

.m__breadcrumb {
    &--item {
        .is-link {
            font-weight: 400 !important;
            color: #606266 !important;
            cursor: pointer;
        }

        &:last-child .el-breadcrumb__inner {
            font-weight: 700 !important;
            text-decoration: none;
            transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            color: #303133;
        }
    }
}

.business {
    position: fixed;
    border-radius: 50px;
    width: 106px;
    height: 38px;
    background-color: #fff;
    bottom: 11px;
    right: 13px;

    p {
        position: absolute;
        font-family: 'SimHei';
        font-size: 15px;
        font-weight: 600;
        top: 11px;
        left: 21px;
        color: #59a9f5;
    }

    .img {
        position: absolute;
        right: 20px;
        top: 13px;
        width: 14px;
        height: 14px;
    }
}

.el-dropdown-link {
    display: flex;
    height: 50px;
    align-items: center;
}
</style>
