/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-18 15:31:06
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-04 09:47:51
 */
import directives from '@/directives'
import selectLoad from '@/directives/selectLoad'
import { VueDraggableNext } from 'vue-draggable-next'
import Vue3DraggableResizable from 'vue3-draggable-resizable'
import VueDOMPurifyHTML from 'vue-dompurify-html'
// import { mockServer } from '@/mocks'
// 注册mock服务
// mockServer() && mockServer()?.start()
export const initPlugin = {
    install(Vue: import('vue').App<any>) {
        // 注册指令
        registerDirective(Vue)
        Vue.directive('loadMore', selectLoad)
        // 注册组件
        registerComponent(Vue)
        Vue.use(VueDOMPurifyHTML)
    },
}
const registerDirective = (Vue: import('vue').App<any>) => {
    Object.keys(directives).forEach((item) => {
        Vue.directive(item, directives[item as keyof typeof directives])
    })
}
const registerComponent = (Vue: import('vue').App<any>) => {
    Vue.component('VueDraggableNext', VueDraggableNext)
    Vue.component('VueDragResize', Vue3DraggableResizable)
}
