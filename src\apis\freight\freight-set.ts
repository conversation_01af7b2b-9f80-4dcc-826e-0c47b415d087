/*
 * @description:
 * @Author: lexy
 * @Date: 2022-08-12 16:47:32
 * @LastEditors: lexy
 * @LastEditTime: 2022-08-12 17:22:43
 */
import { get, put, post, del } from '@/apis/http'
/**
 * @LastEditors: lexy
 * @description:快递设置新增/修改
 * @returns {*}
 */
export const doCourierUpdateAndEdit = (customer: string, key: string, secret: string, id?: string) => {
    return post({ url: 'gruul-mall-freight/logistics/settings/edit', data: { customer, id, key, secret } })
}
/**
 * @LastEditors: lexy
 * @description:快递设置信息获取
 * @returns {*}
 */
export const doGetCourierInfo = () => {
    return get({ url: 'gruul-mall-freight/logistics/settings/get' })
}
