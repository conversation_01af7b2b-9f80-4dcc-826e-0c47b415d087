<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-22 15:43:32
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-04 17:18:19
-->
<script setup lang="ts">
import { computed, defineEmits, reactive, ref, PropType, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { doSetPurchase } from '@/apis/good'
import { CommoditySpecTable } from '../types'
import ReleaseTitle from '../releaseCommodity/components/ReleaseTitle.vue'
interface PurchaseSkuType {
    skuId: string
    limitType: string
    limitNum: number
}
interface PurchaseFormType {
    productId: string
    skuStocks: PurchaseSkuType[]
}
/*
 *variable
 */
const $props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    sku: {
        type: Array as PropType<CommoditySpecTable[]>,
        default() {
            return []
        },
    },
    productId: {
        type: String,
        default: '',
    },
    skuIsMulti: {
        type: Boolean,
        default: false,
    },
})
const $emit = defineEmits(['close', 'update:sku'])
const modelSku = useVModel($props, 'sku', $emit)

const submitForm = reactive<PurchaseFormType>({
    productId: '',
    skuStocks: [
        {
            skuId: '',
            limitType: '',
            limitNum: 0,
        },
    ],
})
// 用于控制整个品的限购类型、数量
const goodLimitNum = ref<number>(0)
const goodLimitType = ref<'UNLIMITED' | 'PRODUCT_LIMITED'>('UNLIMITED')

const dialogVisible = computed(() => $props.show)
const skuLimitNum = ref<number>(0)
const allSkuLimitNum = computed(() => {
    skuLimitNum.value = 0
    return modelSku.value.reduce((acc, item) => {
        if (item.limitType === 'SKU_LIMITED') {
            skuLimitNum.value++
            return acc + item.limitNum
        }
        return acc
    }, 0)
})
/*
 *lifeCircle
 */
watch(
    () => dialogVisible.value,
    (val) => {
        if (val) {
            modelSku.value = $props.sku.map((item) => {
                if (item.limitType === 'PRODUCT_LIMITED') {
                    goodLimitNum.value = item.limitNum
                    goodLimitType.value = 'PRODUCT_LIMITED'

                    item.limitType = 'UNLIMITED'
                    item.limitNum = 0
                }
                return item
            })
        }
    },
)
/*
 *function
 */
/**
 * 切换商品限购类型
 */
const changeGoodLimitType = () => {
    if (goodLimitType.value === 'PRODUCT_LIMITED') {
        goodLimitNum.value = 1
    } else {
        goodLimitNum.value = 0
    }
}
/**
 * 切换规格限购类型
 */
const changeSkuLimitType = (index: number, val: string) => {
    if (val === 'UNLIMITED') {
        modelSku.value[index].limitNum = 0
    } else {
        if (goodLimitType.value === 'PRODUCT_LIMITED') {
            if (allSkuLimitNum.value >= goodLimitNum.value) {
                ElMessage.warning('规格限购数量不能大于商品限购数量')
                modelSku.value[index].limitNum = 0
                modelSku.value[index].limitType = 'UNLIMITED'
                return
            }
        }
    }
}
/**
 * 关闭限购设置弹窗
 * 重置限购设置
 */
const handleCloseDialog = () => {
    submitForm.productId = ''
    submitForm.skuStocks = [
        {
            skuId: '',
            limitType: '',
            limitNum: 0,
        },
    ]
    goodLimitNum.value = 0
    goodLimitType.value = 'UNLIMITED'
    $emit('close')
}
/**
 * 提交限购设置
 */
const handleSubmit = async () => {
    if (goodLimitType.value === 'PRODUCT_LIMITED' && allSkuLimitNum.value > goodLimitNum.value) {
        ElMessage.warning('规格限购总数量不能大于商品限购数量')
        return
    }

    submitForm.productId = $props.productId
    submitForm.skuStocks = $props.sku.map((item) => ({
        skuId: item.id,
        limitType: item.limitType !== 'UNLIMITED' ? item.limitType : goodLimitType.value,
        limitNum: item.limitType !== 'UNLIMITED' ? item.limitNum : goodLimitNum.value,
    }))
    const { code, success } = await doSetPurchase(submitForm.productId, submitForm.skuStocks)
    if (code === 200 && success) {
        ElMessage.success('设置限购成功')
        $emit('close', 'refresh')
    }
}

/**
 * 商品限购数量
 */
const handleGoodLimitNum = (val: any) => {
    if (val > 99 || allSkuLimitNum.value > val) {
        ElMessage.warning('商品限购数量需大于规格限购总数量，且最大不能超过99')
        return
    }
    goodLimitNum.value = val < 99 ? val : 99
}

/**
 * 批量设置规格限购
 */
const handleBatch = (e: any) => {
    if (skuLimitNum.value === 0) {
        return
    }
    if (e === 0) {
        ElMessage.warning('限购数不能为0')
        return
    }
    if (e) {
        if (e * skuLimitNum.value > goodLimitNum.value) {
            ElMessage.warning('规格限购总数量不能大于商品限购数量')
            return
        }

        modelSku.value = modelSku.value.map((item) => {
            if (item.limitType === 'SKU_LIMITED') {
                item.limitNum = e
            }
            return item
        })
    }
}
</script>

<template>
    <el-dialog v-model="dialogVisible" title="限购设置" center @close="handleCloseDialog">
        <release-title title="商品限购设置" style="margin-top: 20px">
            <span style="color: rgba(108, 108, 108, 1); font-weight: 400; font-size: 12px; margin-left: 20px">如果选择商品限购，则下属全部规格都将计入限购数量，无论是否限制单独的规格限购 </span>
        </release-title>
        <!-- 仅对不限购的sku进行控制 -->
        <div style="display: flex; align-items: center; margin: 20px 0 0 20px">
            <span>限购</span>
            <el-input
                v-model="goodLimitNum"
                placeholder="请输入限购数量"
                class="input-with-select"
                style="max-width: 300px; margin-left: 20px"
                type="number"
                :disabled="goodLimitType === 'UNLIMITED'"
                @input="handleGoodLimitNum"
            >
                <template #prepend>
                    <el-select v-model="goodLimitType" placeholder="限购类型" @change="changeGoodLimitType">
                        <el-option label="不限购" value="UNLIMITED"></el-option>
                        <el-option label="商品限购" value="PRODUCT_LIMITED"></el-option>
                    </el-select>
                </template>
            </el-input>
        </div>
        <template v-if="$props.skuIsMulti">
            <release-title title="规格限购设置" style="justify-content: space-between">
                规格批量
                <el-input-number
                    placeholder="限购数"
                    style="max-width: 300px; float: right"
                    type="number"
                    :controls="false"
                    :min="1"
                    :precision="0"
                    :max="goodLimitType === 'UNLIMITED' ? 99 : goodLimitNum"
                    @input="handleBatch"
                />
            </release-title>
            <el-table :data="$props.sku" height="40vh" style="width: 100%">
                <el-table-column label="商品图片" align="center" width="120px">
                    <template #default="{ row }">
                        <el-image style="width: 80px; height: 80px" :src="row.image"></el-image>
                    </template>
                </el-table-column>
                <el-table-column prop="specs" label="商品规格" align="center" />
                <el-table-column prop="specs" label="限购类型" align="center" width="130px">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.limitType" style="width: 100%" @change="(val) => changeSkuLimitType($index, val)">
                            <el-option label="不限购" value="UNLIMITED"></el-option>
                            <el-option label="规格限购" value="SKU_LIMITED"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="限购数量" align="center" width="200px">
                    <template #default="{ row }">
                        <el-input-number
                            v-model="row.limitNum"
                            :disabled="row.limitType === 'UNLIMITED'"
                            :controls="false"
                            :min="row.limitType === 'UNLIMITED' ? 0 : 1"
                            :precision="0"
                            :max="goodLimitType === 'UNLIMITED' ? 99 : goodLimitNum"
                            style="width: 100%"
                        />
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="handleSubmit">提交</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.input-with-select {
    :deep(.el-input-group__prepend) {
        padding: 0;
        width: 40%;
        background-color: var(--el-fill-color-blank);
    }
}
</style>
