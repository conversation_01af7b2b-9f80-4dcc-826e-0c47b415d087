<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-31 20:46:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 14:49:46
-->
<template>
    <div class="search" style="background: #f9f9f9">
        <MCard v-model="showMCard">
            <el-form :model="searchFormData">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="订单号" label-width="90px">
                            <el-input v-model="searchFormData.no" placeholder="请输入订单号" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="采购商" label-width="90px">
                            <el-input v-model="searchFormData.purchaser" placeholder="请输入采购商" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="下单时间" label-width="90px">
                            <el-date-picker v-model="searchFormData.date" format="YYYY/MM/DD" value-format="YYYY-MM-DD" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-form-item label-width="90px">
                        <el-button class="from_btn" type="primary" round @click="handleSearchOrder">搜索</el-button>
                        <el-button class="from_btn" round @click="handleReset">重置</el-button>
                        <el-button class="from_btn" type="primary" round @click="supplierExport">导出</el-button>
                    </el-form-item>
                </el-row>
            </el-form>
        </MCard>
    </div>
</template>

<script lang="ts" setup>
import MCard from '@/components/MCard.vue'
import { cloneDeep } from 'lodash'
const showMCard = ref(false)
const $route = useRoute()
const searchFormData = reactive({
    no: ($route.query.orderNo as string) || '', // 订单号
    purchaser: '', // 采购商
    date: '', // 采购时间
})
const emitFn = defineEmits(['search', 'cardVisibleChange', 'supplierExport'])
const handleSearchOrder = () => {
    const cloneSearchFormData: { [key: string]: any } = cloneDeep(searchFormData)
    if (cloneSearchFormData.date?.length) {
        cloneSearchFormData.startDate = cloneSearchFormData?.date?.[0]
        cloneSearchFormData.endDate = cloneSearchFormData?.date?.[1]
    }
    cloneSearchFormData.date = void 0
    emitFn('search', cloneSearchFormData)
}
watch(
    () => showMCard.value,
    (val) => {
        emitFn('cardVisibleChange', val)
    },
)
const handleReset = () => {
    Object.keys(searchFormData).forEach((key) => (searchFormData[key] = ''))
    handleSearchOrder()
}
const supplierExport = () => {
    emitFn('supplierExport', searchFormData)
}
</script>
