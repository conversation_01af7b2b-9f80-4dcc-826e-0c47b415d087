<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-11 20:15:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-13 13:49:54
-->
<template>
    <template v-if="message">
        <div class="detail-title">
            {{ message.title }}
        </div>
        <div class="detail-summary">
            <div v-show="message.createTime" class="detail-summary-tips">
                {{ message.createTime }}
            </div>
            <div class="detail-summary-content">
                {{ message.summary }}
            </div>
        </div>
        <div v-dompurify-html="message.content" class="detail-content" />
    </template>
</template>
<script setup lang="ts">
import { getMessageById } from '@/apis/message'
import { StompMessage } from '@/components/layout/message/message'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const message = ref<StompMessage | null>(null)
onMounted(() => {
    const messageId = route.query.id
    if (!messageId) {
        return
    }
    getMessageById(messageId).then((result) => {
        message.value = result.data
        console.log(result.data)
    })
})
</script>
<style scoped lang="scss">
@import '../../../../assets/css/global';

.detail-title {
    padding-top: 15px;
    text-align: center;
    color: $rows-color-title;
    font-size: 28px;
}

.detail-summary {
    padding: 20px 20px;
    color: $rows-color-subtitle;
    font-size: 10px;
}

.detail-summary-tips {
    text-align: right;
    color: $rows-text-color-grey;
    font-size: 10px;
}
.detail-summary-content {
    text-align: center;
    font-size: 8px;
    color: $rows-text-color-disable;
    margin-bottom: 10px;
}
.detail-content {
    letter-spacing: 1.1px;
    color: $rows-color-paragraph;
    font-size: 14px;
    line-height: 200%;
}
</style>
