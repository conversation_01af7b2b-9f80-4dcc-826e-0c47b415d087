<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-08 10:23:18
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="订单号">
                            <el-input v-model="searchType.no" placeholder="请输入" maxlength="23"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="类型">
                            <el-select
                                v-model="searchType.stockChangeType"
                                v-loadMore="{
                                    fn: loadMoreHandle,
                                }"
                                placeholder="请选择"
                                style="width: 224px"
                            >
                                <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="出入库时间">
                            <el-date-picker
                                v-model="searchType.date"
                                :clearable="false"
                                type="datetimerange"
                                range-separator="-"
                                start-placeholder="开始时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import MCard from '@/components/MCard.vue'
import { cloneDeep } from 'lodash'
import type { SearchEmitType } from './types'
// import DateUtil from '@/utils/date'
export type SearchType = Record<'no' | 'stockChangeType' | 'date', string>

/**
 * reactive variable
 */
const isShow = ref(false)
const typeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'ALLOCATION_INBOUND',
        label: '调拨入库',
    },
    {
        value: 'OTHER_INBOUND',
        label: '其它入库',
    },
    {
        value: 'ALLOCATION_OUTBOUND',
        label: '调拨出库',
    },
    {
        value: 'OTHER_OUTBOUND',
        label: '其它出库',
    },
])
const searchType = reactive({
    no: '',
    stockChangeType: '',
    date: '',
})
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
const loadMoreHandle = (e: any) => {
    $emit('onSearchParams')
}

function search() {
    const cloneSearchType: SearchEmitType & { date: any } = cloneDeep(searchType) as unknown as SearchEmitType & { date: any }
    if (Array.isArray(cloneSearchType.date) && cloneSearchType.date?.length > 0) {
        cloneSearchType.startTime = cloneSearchType.date?.[0]
        cloneSearchType.endTime = cloneSearchType.date?.[1]
    }
    delete cloneSearchType.date
    $emit('onSearchParams', cloneSearchType)
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    search()
}
</script>
