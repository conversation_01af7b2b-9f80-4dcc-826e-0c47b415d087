/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-27 18:38:21
 * @LastEditors: lexy
 * @LastEditTime: 2023-04-24 15:42:06
 */
import { AFSSTATUS } from '@/views/afs/types'
import { ApiLogistics01 } from '@/views/afs/types/api'
import type { PLATFORM } from '../types'
/**
 * @LastEditors: lexy
 * @description: 订单状态
 * @param  UNPAID 未支付
 * @param  PAID 已支付
 * @param  BUYER_CLOSED 买家关闭订单
 * @param  SYSTEM_CLOSED 系统关闭订单
 * @param  SELLER_CLOSED 卖家关闭订单
 * @param  TEAMING 拼团中
 * @param  TEAM_FAIL 拼团失败
 */
export enum ORDERSTATUS {
    UNPAID,
    PAID,
    BUYER_CLOSED,
    SYSTEM_CLOSED,
    SELLER_CLOSED,
    TEAMING,
    TEAM_FAIL,
}

export enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
/**
 * @LastEditors: lexy
 * @description: 订单tab状态
 * @param UNPAID 待支付
 * @param UN_DELIVERY 待发货
 * @param UN_RECEIVE 待收货
 * @param COMPLETED 已完成
 * @param CLOSED 已关闭
 */
export enum QUERYORDERSTATUS {
    UNPAID,
    UN_DELIVERY,
    UN_RECEIVE,
    COMPLETED,
    CLOSED,
}
/**
 * @LastEditors: lexy
 * @description: 订单类型
 * @param COMMON 商品订单
 * @param SPIKE 秒杀
 */
export enum ORDERTYPE {
    COMMON,
    SPIKE,
}
export enum ORDERPAYMENT {
    WECHAT,
    ALIPAY,
    BALANCE,
    YST,
}

export enum PAY_MODE {
    SCAN_WEIXIN = 'SCAN_WEIXIN',
    WECHAT_PUBLIC = 'WECHAT_PUBLIC',
    WECHATPAY_MINIPROGRAM = 'WECHATPAY_MINIPROGRAM',
    SCAN_ALIPAY = 'SCAN_ALIPAY',
    ALIPAY_SERVICE = 'ALIPAY_SERVICE',
}

/**
 * @LastEditors: lexy
 * @description: 订单支付状态
 * @param CLOSED 取消支付
 * @param UNPAID 未支付
 * @param PAID 已支付
 */
export enum ORDERPAYMENTSTATUS {
    CLOSED,
    UNPAID,
    PAID,
}
/**
 * @LastEditors: lexy
 * @description: 优惠类型
 * @param PLATFORM_COUPON 平台优惠券
 * @param SHOP_COUPON 店铺优惠券
 */
enum DISCOUNTSOURCETYPE {
    PLATFORM_COUPON,
    SHOP_COUPON,
}
/**
 * @LastEditors: lexy
 * @description: 优惠状态
 */
enum DISCOUNTSOURCESTATUS {
    NORMAL,
    CLOSED,
}
/**
 * @LastEditors: lexy
 * @description: 商铺订单状态
 * @param UNPAID 未支付
 * @param PAID 支付
 * @param SYSTEM_CLOSED 系统关闭
 * @param BUYER_CLOSED  买家关闭订单
 * @param SELLER_CLOSED  卖家关闭订单
 */
export enum SHOPORDERSTATUS {
    OK,
    SYSTEM_CLOSED,
    BUYER_CLOSED,
    SELLER_CLOSED,
}
/**
 * @LastEditors: lexy
 * @description:
 * @returns {*}
 */
export enum SHOPITEMSTATUS {
    OK,
    CLOSED,
}
/**
 * @LastEditors: lexy
 * @description: 包裹状态
 * @param WAITING_FOR_DELIVER 待发货
 * @param WAITING_FOR_RECEIVE 已发货待收货
 * @param BUYER_WAITING_FOR_COMMENT 买家确认收货 待评价
 * @param SYSTEM_WAITING_FOR_COMMENT 系统确认收货 待评价
 * @param BUYER_COMMENTED_COMPLETED 买家已评论 已完成
 * @param SYSTEM_COMMENTED_COMPLETED 系统自动好评 已完成
 */
export enum PACKAGESTATUS {
    WAITING_FOR_DELIVER,
    WAITING_FOR_RECEIVE,
    BUYER_WAITING_FOR_COMMENT,
    SYSTEM_WAITING_FOR_COMMENT,
    BUYER_COMMENTED_COMPLETED,
    SYSTEM_COMMENTED_COMPLETED,
}
/**
 * @LastEditors: lexy
 * @description: 配送方式
 */
export enum DISTRIBUTION {
    MERCHANT,
    EXPRESS, //快递配送
    INTRA_CITY_DISTRIBUTION, //同城配送
    SHOP_STORE, //店铺门店
    VIRTUAL, // 无需物流
}
/**
 * @LastEditors: lexy
 * @description: 订单类型
 * @param {string} buyerId 买家用户id
 * @param {string} no 订单号
 * @param {ORDERSTATUS} status 订单状态
 * @param {ORDERTYPE} type 订单类型
 * @param {OrderPayment} orderPayment 订单支付相关信息
 * @param {OrderDiscount} orderDiscounts 订单优惠相关
 * @param {ShopOrder} shopOrders 店铺订单相关
 */
export interface ApiOrder {
    id: string
    shopId: string
    buyerId: string
    buyerNickname: string
    createTime: string
    updateTime: string
    no: string
    status: keyof typeof ORDERSTATUS
    type: keyof typeof ORDERTYPE
    remark: string
    orderPayment: OrderPayment
    orderDiscounts: OrderDiscount[]
    shopOrders: ShopOrder[]
    orderReceiver: OrderReceiver
    shopOrderPackages: ApiLogistics01[]
    checked: boolean
    platform: PLATFORM
    extra: {
        distributionMode: keyof typeof DISTRIBUTION
    }
}
/**
 * @LastEditors: lexy
 * @description: 订单接收
 * @returns {*}
 */
export interface OrderReceiver {
    address: string
    areaCode: string[]
    id: string
    mobile: string
    name: string
}
/**
 * @LastEditors: lexy
 * @description: 支付相关信息
 * @param payerId 支付用户id
 * @param type 支付类型
 * @param status 支付状态
 * @param totalAmount 订单总金额
 * @param freightAmount 总运费
 * @param discountAmount 优惠总金额
 * @param payTime 支付时间
 * @param payAmount 支付总金额金额 = 订单总金额 - 优惠总金额
 */
export interface OrderPayment {
    createTime: string
    shopId: string
    orderId: string
    payerId: string
    type: keyof typeof ORDERPAYMENT
    status: keyof typeof ORDERPAYMENTSTATUS
    totalAmount: number
    freightAmount: number
    discountAmount: number
    payAmount: number
    payTime: string
    payMode: keyof typeof PAY_MODE
}
/**
 * @LastEditors: lexy
 * @description: 订单优惠
 * @param sourceType 优惠类型
 * @param sourceStatus 优惠状态
 * @param sourceId 优惠源Id
 * @param sourceAmount 优惠金额
 * @param sourceDesc 优惠信息描述
 * @param discountItems 优惠项对应商品
 */
export interface OrderDiscount {
    shopId: string
    orderId: string
    sourceType: keyof typeof DISCOUNTSOURCETYPE
    sourceStatus: keyof typeof DISCOUNTSOURCESTATUS
    sourceId: string
    sourceAmount: number
    sourceDesc: string
    discountItems: OrderDiscountItem[]
}
/**
 * @LastEditors: lexy
 * @description: 优惠对应的商品
 * @param packageId 店铺包裹id
 * @param packageItemId 店铺包裹商品id
 * @param discountId 优惠项id
 */
export interface OrderDiscountItem {
    itemId: string
    shopId: string
    packageId: string
    packageItemId: string
    discountId: string
    discountAmount: string
}
/**
 * @LastEditors: lexy
 * @description: 店铺订单相关
 * @param no 店铺订单号
 * @param remark 店铺订单备注
 */
export interface ShopOrder {
    no: string
    status: keyof typeof SHOPORDERSTATUS
    shopId: string
    orderId: string
    shopName: string
    shopLogo: string
    remark: string
    id: string
    orderReceiver?: OrderReceiver
    shopOrderItems: ShopOrderItem[]
}
export interface ShopOrderItem {
    afsNo: string
    afsStatus: keyof typeof AFSSTATUS
    dealPrice: string
    freightPrice: string
    freightTemplateId: string
    status: keyof typeof SHOPITEMSTATUS
    id: string
    image: string
    num: number
    packageStatus: keyof typeof PACKAGESTATUS
    orderId: string
    productId: string
    productName: string
    salePrice: string
    shopId: string
    skuId: string
    specs: string[]
    weight: number
    packageId?: string
}

/**
 * @LastEditors: lexy
 * @description: 商铺订单包裹相关
 * @param shopOrderId 店铺订单id
 * @param freightTemplateId 运费模板id
 * @param freightPrice 用户支付的运费
 * @param  status 包裹状态
 * @param  type  配送方式
 * @param  receiverName  收货人名称
 * @param  receiverMobile  收货人电话
 * @param  receiverAreaCode  省市区code
 * @param  receiverAddress  收货人详细地址
 * @param  receiveTime  确认收货时间
 * @param  shopOrderPackageItems  确认收货时间
 */
// interface ShopOrderPackage {
//     shopId: string
//     orderId: string
//     shopOrderId: string
//     freightTemplateId: string
//     freightPrice: number
//     status: keyof typeof PACKAGESTATUS
//     type: keyof typeof PACKAGETYPE
//     receiverName: string
//     receiverMobile: string
//     receiverAreaCode: string[]
//     receiverAddress: string
//     receiveTime: string
//     shopOrderPackageItems: ShopOrderPackageItem[]
// }
/**
 * @LastEditors: lexy
 * @description: 店铺订单包裹商品相关
 * @param packageId 配送包裹id
 * @param specs 商品规格
 * @param num 购买数量
 * @param image 该商品sku 图片
 * @param salePrice 销售单价
 * @param dealPrice 成交价(活动价: 如秒杀,等等)
 */
// export interface ShopOrderPackageItem {
//     shopId: string
//     orderId: string
//     packageId: string
//     productId: string
//     productImage: string
//     productName: string
//     skuId: string
//     specs: string[]
//     num: number
//     image: string
//     weight: number
//     salePrice: number
//     dealPrice: number
// }
// *-----------------
/**
 * @LastEditors: lexy
 * @description 订单列表的类型
 *  @param {current} current 页码
 * @param {size} size 每页展示几条
 * @param {total} total 总数
 */
export interface OrderDataType {
    records: ApiOrder[]
}
export interface OrderListSearchData {
    no: string
    buyerNickname: string
    productName: string
    receiverName: string
    startTime: string
    endTime: string
    platform: PlatformList
}
export interface Apipackage {
    confirmTime: string
    createTime: string
    deleted: boolean
    deliveryTime: string
    expressCompanyCode: string
    expressCompanyName: string
    expressNo: string
    id: string
    orderNo: string
    receiverAddress: string
    receiverMobile: string
    receiverName: string
    remark: string
    shopId: string
    status: string
    type: 'EXPRESS' | 'PRINT_EXPRESS' | 'WITHOUT'
    updateTime: string
    deliverShopName?: string
    deliverImages?: string
}
export interface PlatformList {
    WECHAT_MINI_APP: '小程序'
    WECHAT_MP: '公众号'
    H5: 'H5商城'
    APP: 'APP商城'
    PC: 'PC商城'
}
export interface ExtraMap {
    AllDeliveryCount: string
    miniDeliveryCount: string
}
