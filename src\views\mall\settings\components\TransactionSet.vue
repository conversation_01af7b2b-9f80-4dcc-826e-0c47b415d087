<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-25 10:42:46
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-27 20:20:51
-->
<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { gettrade, posttrade } from '@/apis/mall/settings'
import type { FormInstance } from 'element-plus'
type FormType = 'MOBILE' | 'CITIZEN_ID' | 'TEXT' | 'NUMBER' | 'IMAGE' | 'DATE' | 'TIME' | 'DATETIME' | ''
interface ApiForm {
    key: string
    type: FormType
    required: boolean
    placeholder: string
}
/*
 *variable
 */
const isLock = ref(false)
const form = ref({
    orderNotify: false,
})
const custom = ref<ApiForm>({
    key: '',
    type: '',
    required: false,
    placeholder: '请输入',
})
const forms = ref<ApiForm[]>([])
const id = ref(null)
const dialogVisible = ref(false)
const formOptions = reactive([
    {
        label: '电话',
        value: 'MOBILE',
    },
    {
        label: '身份证',
        value: 'CITIZEN_ID',
    },
    {
        label: '文本',
        value: 'TEXT',
    },
    {
        label: '数字',
        value: 'NUMBER',
    },
    {
        label: '图片',
        value: 'IMAGE',
    },
    {
        label: '日期',
        value: 'DATE',
    },
    {
        label: '时间',
        value: 'TIME',
    },
    {
        label: '日期时间',
        value: 'DATETIME',
    },
])

const coustomRules = reactive({
    key: [
        {
            required: true,
            message: '表单名称不可为空',
            trigger: 'blur',
        },
        {
            min: 1,
            max: 8,
            message: '表单名称最多8位',
            trigger: 'blur',
        },
    ],
    type: [
        {
            required: true,
            message: '表单类型不可为空',
            trigger: 'blur',
        },
    ],
    placeholder: [
        {
            required: true,
            message: '表单提示语不可为空',
            trigger: 'blur',
        },
    ],
})
const currentIndex = ref(null)
const isEdit = ref(false)
const Custom = ref<FormInstance>(null)
/*
 *lifeCircle
 */

onMounted(() => {
    Gettrade()
})
/*
 *function
 */

/**
 * @LastEditors: lexy
 * @description: 获取交易设置消息
 * @returns
 */
const Gettrade = () => {
    gettrade({}).then((res) => {
        if (res.data) {
            form.value.orderNotify = res.data.orderNotify
            if (res.data.customFrom) {
                forms.value = res.data.customFrom
            }
            id.value = res.data.id
        }
    })
}
/**
 * @LastEditors: lexy
 * @description: 关闭弹窗
 * @returns
 */
const toggleDialogVisible = () => {
    isLock.value = false
    dialogVisible.value = false
    custom.value = {
        key: '',
        type: '',
        required: false,
        placeholder: '',
    }
    isEdit.value = false
}
/**
 * @LastEditors: lexy
 * @description: 获取表单格式
 * @returns
 */
const getFormName = (key) => {
    const data = formOptions.find((item) => item.value === key)
    return data && data.label
}
/**
 * @LastEditors: lexy
 * @description: 获取表格删除
 * @returns
 */
const handleDel = (i) => {
    forms.value.splice(i, 1)
}
/**
 * @LastEditors: lexy
 * @description: 获取表格编辑
 * @returns
 */
const handleEdit = (item, i) => {
    custom.value = { ...item }
    currentIndex.value = i
    isEdit.value = true
    dialogVisible.value = true
}
/**
 * @LastEditors: lexy
 * @description: 获取弹窗确认
 * @returns
 */
const onConfirm = async () => {
    if (!dialogVisible.value) return
    try {
        const isValidate = await Custom.value.validate()
        if (!isValidate) return
        if (isEdit.value) {
            forms.value[currentIndex.value] = custom.value
        } else {
            forms.value.push(custom.value)
        }
        custom.value = {
            key: '',
            type: '',
            required: false,
            placeholder: '请输入',
        }
        isEdit.value = false
        dialogVisible.value = false
    } catch (error) {
        error
    }
}
/**
 * @LastEditors: lexy
 * @description: 保存
 * @returns
 */
const save = () => {
    posttrade({ ...form.value, customFrom: forms.value, id: id.value })
        .then(() => {
            ElMessage.success('保存成功')
            Gettrade()
        })
        .catch((err) => {
            ElMessage.error(err)
        })
}
</script>

<template>
    <div>
        <div class="order__tip">
            <div class="order__tip--lump"></div>
            <span class="order__tip--title">下单设置</span>
        </div>
        <el-form :model="form" label-width="120px">
            <el-form-item label="订单播报">
                <el-radio-group v-model="form.orderNotify">
                    <el-radio :value="true">开启</el-radio>
                    <el-radio :value="false">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="下单表单">
                <el-table :data="forms" style="width: 100%">
                    <el-table-column prop="key" label="表单名称"> </el-table-column>
                    <el-table-column prop="type" label="表单格式">
                        <template #default="scope">
                            {{ getFormName(scope.row.type) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="必填" prop="required">
                        <template #default="scope">
                            {{ scope.row.required ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="提示语" prop="placeholder"> </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="text" size="small" @click="handleEdit(scope.row, scope.$index)">编辑</el-button>

                            <el-button type="text" size="small" :disabled="forms.length === 1" @click="handleDel(scope.$index)">删除 </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-button type="text" @click="dialogVisible = true">添加 </el-button>
            </el-form-item>
        </el-form>
        <el-button type="primary" round class="order__save" @click="save">保存</el-button>

        <el-dialog v-model="dialogVisible" title="下单表单" width="40%">
            <el-form ref="Custom" :rules="coustomRules" :model="custom" label-width="80px">
                <el-form-item label="表单名称" prop="key">
                    <el-input v-model="custom.key" maxlength="8"></el-input>
                </el-form-item>
                <el-form-item label="表单格式" prop="type">
                    <el-select v-model="custom.type" style="width: 100%" placeholder="请选择">
                        <el-option v-for="item of formOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否必填" prop="required">
                    <el-radio-group v-model="custom.required">
                        <el-radio :value="true">开启</el-radio>
                        <el-radio :value="false">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="提示语" prop="placeholder">
                    <el-input v-model="custom.placeholder" maxlength="60"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span>
                    <el-button @click="toggleDialogVisible">取 消</el-button>
                    <el-button type="primary" @click="onConfirm">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
@import '@/assets/css/mixins/mixins.scss';

@include b(order) {
    @include e(tip) {
        vertical-align: center;
        background-color: rgba(246, 248, 250, 1);
        padding: 15px 15px 15px 30px;
        margin-bottom: 30px;

        @include m(title) {
            margin-left: 12px;
            color: #586884;
            font-weight: 700;
        }

        @include m(lump) {
            display: inline-block;
            width: 3px;
            height: 12px;
            background-color: rgba(255, 153, 0, 1);
        }
    }
    @include e(save) {
        margin-left: 100px;
        margin-top: 40px;
    }
}
</style>
