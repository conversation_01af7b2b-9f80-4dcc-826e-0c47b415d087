/*
 * @description:
 * @Author: zrb
 * @Date: 2022-08-19 14:05:23
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-02 11:32:45
 * // 可以自定义最大天数，默认为30天  const { disabledDate, handleCalendarChange } = useRangeLimitedDate(15, true)
 * const disabledDate = useDisableHistoryDate()
 * const disabledDate = useDisableFutureDate()
 */
import { ref, computed } from 'vue'

// 日期禁用函数类型
type DisabledDateFunc = (time: Date) => boolean

/**
 * 禁用未来时间的hook
 * @returns 禁用日期函数
 */
export function useDisableFutureDate(): DisabledDateFunc {
    return (time: Date) => {
        // 获取今天的日期和时间
        const today = new Date()
        // 如果选择的日期时间在今天之后，则禁用
        return time.getTime() > today.getTime()
    }
}

/**
 * 禁用历史时间的hook
 * @returns 禁用日期函数
 */
export function useDisableHistoryDate(): DisabledDateFunc {
    return (time: Date) => {
        // 获取今天的日期和时间（00:00:00）
        const todayStart = new Date(new Date().toLocaleDateString()).getTime()
        // 如果选择的日期时间在今天之前，则禁用
        return time.getTime() < todayStart
    }
}

/**
 * 限制选择日期范围的hook
 * @param maxDays 最大可选天数
 * @param disableFutureDate 是否同时禁用未来日期
 * @param bidirectional 是否双向限制范围（允许前后各选maxDays天），默认为false（只限制之后的日期）
 * @returns 禁用日期函数和日历变化处理函数
 */
export function useRangeLimitedDate(maxDays = 30, disableFutureDate = false, bidirectional = false) {
    // 记录选择的开始日期
    const startDate = ref<Date | null>(null)
    // 记录选择的结束日期
    const endDate = ref<Date | null>(null)
    // 标记是否应用范围限制
    const applyRangeLimit = ref(false)

    // 一天的毫秒数
    const oneDayMs = 24 * 60 * 60 * 1000

    // 当前日期
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    // 计算允许的日期范围
    const dateRange = computed(() => {
        if (!startDate.value) return null

        const startTime = startDate.value.getTime()

        // 如果是双向限制，则允许前后各maxDays天
        if (bidirectional) {
            // 最小允许日期（前N天）
            const minDate = new Date(startTime - maxDays * oneDayMs)

            // 最大允许日期（后N天）
            let maxDate = new Date(startTime + maxDays * oneDayMs)

            // 如果需要限制未来日期，取今天和最大日期的较小值
            if (disableFutureDate) {
                maxDate = maxDate.getTime() > today.getTime() ? today : maxDate
            }

            return { minDate, maxDate }
        } else {
            // 单向限制，只限制最大日期
            let maxDate = new Date(startTime + maxDays * oneDayMs)

            // 如果需要限制未来日期，取今天和最大日期的较小值
            if (disableFutureDate) {
                maxDate = maxDate.getTime() > today.getTime() ? today : maxDate
            }

            return { minDate: null, maxDate }
        }
    })

    // 处理面板可见性变化
    const handleVisibleChange = (visible: boolean) => {
        if (!visible) {
            // 面板关闭时，如果没有完成选择，重置状态
            if (!endDate.value) {
                startDate.value = null
            }
            // 重置范围限制标记
            applyRangeLimit.value = false
        }
    }

    // 处理日期变化
    const handleCalendarChange = (dates: Date[] | null) => {
        if (!dates || dates.length === 0) {
            // 清空选择
            startDate.value = null
            endDate.value = null
            applyRangeLimit.value = false
            return
        }

        // 安全地获取日期值，过滤掉null
        const validDates = dates.filter((d) => d !== null) as Date[]

        if (validDates.length === 1) {
            // 选择了第一个日期
            startDate.value = validDates[0]
            endDate.value = null
            // 标记为应用范围限制，但仍显示第一个日期
            applyRangeLimit.value = true
        } else if (validDates.length === 2) {
            // 选择了两个日期
            const [start, end] = validDates

            const startTime = start.getTime()
            const endTime = end.getTime()
            const diffDays = Math.abs(Math.round((endTime - startTime) / oneDayMs))

            // 如果超出了最大天数范围
            if (diffDays > maxDays) {
                // 计算新的结束日期
                if (endTime > startTime) {
                    // 结束日期在开始日期之后，截断到后N天
                    const newEndDate = new Date(startTime + maxDays * oneDayMs)
                    startDate.value = start
                    endDate.value = newEndDate
                } else {
                    // 结束日期在开始日期之前，截断到前N天
                    const newEndDate = new Date(startTime - maxDays * oneDayMs)
                    startDate.value = start
                    endDate.value = newEndDate
                }
            } else {
                // 正常情况，两个日期都在允许范围内
                startDate.value = start
                endDate.value = end
            }
        }
    }

    // 日期禁用函数
    const disabledDate: DisabledDateFunc = (time: Date) => {
        // 当前时间戳
        const timeValue = time.getTime()

        // 如果需要禁用未来日期
        if (disableFutureDate && timeValue > today.getTime()) {
            return true
        }

        // 仅当应用范围限制且有开始日期时才限制日期范围
        if (applyRangeLimit.value && startDate.value && dateRange.value) {
            const { minDate, maxDate } = dateRange.value

            // 如果是双向限制，则同时检查最小和最大日期
            if (bidirectional && minDate) {
                // 禁用所有小于最小允许日期的日期（前N天）
                if (timeValue < minDate.getTime()) {
                    return true
                }
            } else {
                // 单向限制，只禁用小于开始日期的日期
                if (timeValue < startDate.value.getTime()) {
                    return true
                }
            }

            // 禁用所有超出最大范围的日期（后N天）
            if (maxDate && timeValue > maxDate.getTime()) {
                return true
            }
        }

        // 默认不禁用
        return false
    }

    // 返回方法和状态
    return {
        disabledDate,
        handleCalendarChange,
        handleVisibleChange,

        // 暴露内部状态供测试和调试
        startDate,
        endDate,
        dateRange,
    }
}

/**
 * 简单使用示例:
 *
 <script setup lang="ts">
import { useRangeLimitedDate } from '@/hooks'

// 单向限制模式 - 只能选择30天内的后续日期
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(30, false, false)

// 双向限制模式 - 可以向前选30天，也可以向后选30天
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(30, false, true)

// 同时限制未来日期 - 可以向前选30天，向后不超过今天
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(30, true, true)
</script>

<template>
  <el-date-picker
    v-model="dateRange"
    type="daterange"
    :disabled-date="disabledDate"
    @calendar-change="handleCalendarChange"
    @visible-change="handleVisibleChange"
  />
</template>
 */
