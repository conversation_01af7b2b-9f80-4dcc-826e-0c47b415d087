<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:28:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-17 10:24:17
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="申请单号">
                            <el-input v-model="searchType.id" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="抬头">
                            <el-input v-model="searchType.header" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="税号">
                            <el-input v-model="searchType.taxIdentNo" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="关联订单号">
                            <el-input v-model="searchType.orderNo" placeholder="请输入" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="类型">
                            <el-select v-model="searchType.invoiceHeaderType" placeholder="请选择" style="width: 224px">
                                <el-option v-for="item in TypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="申请时间">
                            <el-date-picker
                                v-model="searchType.date"
                                :clearable="false"
                                type="datetimerange"
                                range-separator="-"
                                start-placeholder="开始时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRaw, watch } from 'vue'
import MCard from '@/components/MCard.vue'
export type SearchType = Record<'supplierGoodsName' | 'supplierProductId' | 'productType', string>
/**
 * reactive variable
 */
const isShow = ref(false)
const TypeList = reactive([
    {
        value: '',
        label: '全部',
    },
    {
        value: 'ENTERPRISE',
        label: '企业',
    },
    {
        value: 'PERSONAL',
        label: '个人',
    },
])

const searchType = reactive({
    id: '',
    header: '',
    taxIdentNo: '',
    orderNo: '',
    invoiceHeaderType: '',
    date: '',
})
const $emit = defineEmits(['onSearchParams', 'changeShow'])

/**
 * lifeCircle
 */
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
function search() {
    $emit('onSearchParams', toRaw(searchType))
}
const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    search()
}
</script>
