<!--
 * @description: 会员专享
 * @Author: lexy
 * @Date: 2025-02-24 19:25:51
 * @LastEditors: lexy 
 * @LastEditTime: 2025-02-24 19:25:51
-->
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import HeadSearch from './components/head-search.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { doGetPromotionList, doDelPromotion, doOffPromotion } from '@apis/marketing'
import { ElMessage, ElMessageBox } from 'element-plus'
import { OnlyPromotionItemType, OnlyPromotionSearchParams, OnlyStatusMap, OnlyStatusColorMap } from '@/apis/marketing/model'
import { FullScreenLoadingHelper } from '@/libs/Loading'
/*
 *variable
 */
const router = useRouter()
const content = ref('')
const onlyPromotionList = ref<OnlyPromotionItemType[]>([])
const dialogVisible = ref(false)
const searchParam = reactive<OnlyPromotionSearchParams>({})
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
/*
 *lifeCircle
 */
/*
 *function
 */
async function initOnlyPromotionList() {
    const { date, onlyStatus, keyword } = searchParam
    const time = { startDate: '', endDate: '' }
    if (Array.isArray(date)) {
        time.startDate = date[0] as unknown as string
        time.endDate = date[1] as unknown as string
    }
    const params = {
        ...pageConfig,
        ...time,
        shopId: useShopInfoStore().shopInfo.id,
        onlyStatus,
        keyword,
    }
    const { code, data } = await doGetPromotionList(params)
    if (code !== 200) return ElMessage.error('获取活动列表失败')
    onlyPromotionList.value = data.records?.map((item: OnlyPromotionItemType) => {
        return { ...item, isChecked: false }
    })
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}

const handleDel = (row: OnlyPromotionItemType) => {
    if (row.onlyStatus === 'PROCESSING') {
        ElMessage.info('进行中的活动不能被删除')
        return
    }
    ElMessageBox.confirm('确定删除该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: async (action: string) => {
            if (action === 'cancel') {
                return
            }
            const { code } = await doDelPromotion([row.id])
            if (code !== 200) {
                ElMessage.error('删除失败')
                return
            }
            ElMessage.success('删除成功')
            onlyPromotionList.value = onlyPromotionList.value.filter((item) => item.id !== row.id)
            pageConfig.total--
        },
    })
}
const selectionItems = ref<Array<OnlyPromotionItemType>>([])
const handleSelectionChange = (res: OnlyPromotionItemType[]) => {
    selectionItems.value = res
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    pageConfig.size = value
    initOnlyPromotionList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initOnlyPromotionList()
}
const handleBatchDel = () => {
    ElMessageBox.confirm('确定删除选中活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: async (action: string) => {
            if (action === 'cancel') {
                return
            }
            FullScreenLoadingHelper.openLoading(true)
            const ids = selectionItems.value.filter((item) => item.onlyStatus !== 'PROCESSING').map((item) => item.id)
            const { code } = await doDelPromotion(ids)
            if (code !== 200) {
                FullScreenLoadingHelper.closeLoading(true)
                ElMessage.error('删除失败')
                return
            }
            ElMessage.success('删除成功')
            setTimeout(() => {
                FullScreenLoadingHelper.closeLoading(true)
                initOnlyPromotionList()
            }, 2000)
        },
    })
}
const handleOff = (id: string) => {
    ElMessageBox.confirm('确定下架选中活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: async (action: string) => {
            if (action === 'cancel') {
                return
            }
            const { code } = await doOffPromotion(id)
            if (code !== 200) {
                ElMessage.error('下架失败')
                return
            }
            ElMessage.success('下架成功')
            initOnlyPromotionList()
        },
    })
}
const handleIllegal = async (violationReason: string) => {
    content.value = violationReason
    dialogVisible.value = true
}
const handleClose = () => {
    content.value = ''
}
const handleEdit = (shopId: string, onlyId: string, isLookUp = true) => {
    router.push({
        name: 'onlyPromotionCheck',
        query: { onlyId, shopId, isLookUp },
    })
}
const handleAdd = () => {
    router.push({
        name: 'onlyPromotionCreate',
    })
}
</script>

<template>
    <div>
        <head-search v-model="searchParam" :batch-disabled="!selectionItems.length" @add="handleAdd" @search="initOnlyPromotionList" @batch-del="handleBatchDel" />
        <el-table ref="multipleTableRef" :data="onlyPromotionList" :style="{ height: 'calc(100vh - 220px)', width: '100%' }" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" fixed :selectable="(row:OnlyPromotionItemType) => row.onlyStatus !== 'PROCESSING'" />
            <el-table-column label="活动名称" width="120">
                <template #default="{ row }">{{ row.onlyName }}</template>
            </el-table-column>
            <el-table-column align="center" label="活动时间" width="200">
                <template #default="{ row }">
                    <time>{{ row.startTime }}至{{ row.endTime }}</time>
                </template>
            </el-table-column>
            <el-table-column align="center" label="活动状态" width="100">
                <template #default="{ row }">
                    <el-text :type="OnlyStatusColorMap[row.onlyStatus as keyof typeof OnlyStatusMap]">
                        {{ OnlyStatusMap[row.onlyStatus as keyof typeof OnlyStatusMap] }}
                    </el-text>
                </template>
            </el-table-column>
            <el-table-column align="center" label="活动商品数" width="100">
                <template #default="{ row }">{{ row.productNum }}</template>
            </el-table-column>
            <el-table-column align="center" label="参加人数" width="100">
                <template #default="{ row }">
                    <span>{{ row.peopleNum || 0 }}人</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="支付单数" width="100">
                <template #default="{ row }">
                    <span>{{ row.payOrder || 0 }}单</span>
                </template>
            </el-table-column>

            <el-table-column label="操作" fixed="right" width="200">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row.shopId, row.id, true)">查看</el-button>
                    <el-button v-if="row.onlyStatus === 'PROCESSING'" link type="primary" @click="handleOff(row.id)">下架</el-button>
                    <el-button v-if="row.onlyStatus === 'ILLEGAL_SELL_OFF'" link type="primary" @click="handleIllegal(row.violationReason)">违规原因</el-button>
                    <el-button v-if="row.onlyStatus !== 'PROCESSING'" link type="danger" @click="handleDel(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-row align="middle" justify="end">
            <!-- 好用的分页器 -->
            <page-manage
                v-model="pageConfig"
                :load-init="true"
                :page-size="pageConfig.size"
                :total="pageConfig.total"
                @reload="initOnlyPromotionList"
                @handle-size-change="handleSizeChange"
                @handle-current-change="handleCurrentChange"
            />
        </el-row>
    </div>
    <!-- 违规原因 s -->
    <el-dialog v-model="dialogVisible" title="违规原因" width="500" center @close="handleClose">
        <span>{{ content }}</span>
    </el-dialog>
    <!-- 违规原因 e -->
</template>

<style lang="scss" scoped>
@include b(container) {
    overflow-y: scroll;
}
:deep(.el-button.is-link) {
    padding: 0;
}
:deep(.el-button + .el-button) {
    margin-left: 10px;
}
</style>
