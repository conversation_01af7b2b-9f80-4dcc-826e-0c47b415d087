<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-16 14:44:59
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-14 16:27:04
-->
<template>
    <div>
        <search @on-search-params="getSearch" @change-show="handleSearchShow"></search>
        <tab-line default-active="属性" :line-arr="Object.keys(goodsStatus)" operate-text="新增" @tab-click="handleTabClick" @operate-click="handleAdd" />
        <div style="height: 5px"></div>
        <div :style="{ height: tableHeight }">
            <q-table v-if="!tableList.loading" :data="tableList.goods" :style="{ height: tableHeight }" class="table">
                <q-table-column :label="`${titleStatus}名称`" align="center" prop="featureName">
                    <template #default="{ row }">
                        {{ row.featuresValue.featureName }}
                    </template>
                </q-table-column>
                <q-table-column :label="`${titleStatus}值`" align="center" prop="address">
                    <template #default="{ row }">
                        <div :title="row.featuresValue.featureValues.map((item: any) => item.firstValue).join(',')" class="attributeValues">
                            {{ row.featuresValue.featureValues.map((item: any) => item.firstValue).join(',') }}
                        </div>
                    </template>
                </q-table-column>
                <q-table-column v-if="titleStatus === '属性'" label="是否必选" align="center">
                    <template #default="{ row }">
                        <div>{{ row.featuresValue.isRequired ? '是' : '否' }}</div>
                    </template>
                </q-table-column>
                <q-table-column v-if="titleStatus === '属性'" label="是否多选" align="center">
                    <template #default="{ row }">
                        {{ row.featuresValue.isMultiSelect ? '是' : '否' }}
                    </template>
                </q-table-column>
                <q-table-column label="操作" align="center">
                    <template #default="{ row }">
                        <div style="height: 50px"></div>
                        <el-link type="primary" style="margin-right: 10px" @click="handleEdit(row)">编辑</el-link>
                        <el-link type="danger" @click="handleDel(row)">删除</el-link>
                    </template>
                </q-table-column>
            </q-table>
        </div>
        <PageManage v-model="tableList.page" load-init :total="tableList.total" @reload="initList" />
        <el-dialog v-model="dialogVisible" :title="title" width="700px" center>
            <para-dialog v-if="dialogVisible" ref="paraDialogRef" :row-info="rowInfo" :title-status="titleStatus" />
            <template #footer>
                <div>
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" style="margin-left: 100px" @click="handleConfirm">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { onBeforeMount } from 'vue'
import search, { SearchType } from './components/search.vue'
import paraDialog from './components/paraDialog.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import tabLine from '@/views/goods/components/tabLine.vue'
import { doGetfeatureList, dofeatureSave, dofeatureUpdate, dofeatureDel } from '@/apis/good'
import { ElMessage, ElMessageBox } from 'element-plus'
const { mulTenThousand, divTenThousand } = useConvert()
// const tableHeight = ref('calc(100vh - 290px)')
const tableHeight = ref('calc(100vh - 260px)')

// 更该search显隐
const handleSearchShow = (e: boolean) => {
    if (e) {
        tableHeight.value = 'calc(100vh - 360px)'
    } else {
        tableHeight.value = 'calc(100vh - 290px)'
    }
}

const searchParams = ref({
    featuresType: 'ATTRIBUTE',
    name: '',
})

// 获取搜索参数
const getSearch = (e: SearchType) => {
    searchParams.value = { ...searchParams.value, ...e }
    initList()
}

const tableList = reactive({
    page: { size: 10, current: 1 },
    loading: false,
    goods: [],
    total: 0,
})

onBeforeMount(() => {
    initList()
})

const dialogVisible = ref(false)

// 删除
const handleDel = async (row: any) => {
    const featuresType = titleStatus.value === '属性' ? 'ATTRIBUTE' : 'ARGUMENTS'
    const data = [row.id]
    ElMessageBox.confirm(`确认删除当前${titleStatus.value}？`, '提示').then(async () => {
        try {
            await dofeatureDel(featuresType, data)
            initList()
        } catch (error) {
            return
        }
    })
}

const goodsStatus = {
    属性: 'ATTRIBUTE',
    参数: 'ARGUMENTS',
}
const titleStatus = ref('属性')
// 切换tab
const handleTabClick = (status: keyof typeof goodsStatus) => {
    searchParams.value.featuresType = goodsStatus[status]
    titleStatus.value = status
    initList()
}
// 初始化list
const initList = async () => {
    tableList.loading = true
    try {
        const params = {
            ...tableList.page,
            ...searchParams.value,
        }
        const { data } = await doGetfeatureList(params)
        tableList.page = {
            size: data.size,
            current: data.current,
        }
        tableList.total = data.total
        data.records.forEach((element: any) => {
            element.featuresValue.featureValues.forEach((item: any) => {
                item.secondValue = divTenThousand(item.secondValue)
            })
        })
        tableList.goods = data.records
    } catch (error) {
        console.log(error)
    }
    tableList.loading = false
}

// title
const title = ref('新增属性')

const initInfo = () => ({
    featuresType: titleStatus.value === '属性' ? 'ATTRIBUTE' : 'ARGUMENTS',
    featuresValue: {
        featureName: '',
        isRequired: true,
        isMultiSelect: true,
        featureValues: [{ firstValue: '', secondValue: 0 }],
    },
})

// 子组件默认参数
const rowInfo = ref(initInfo())

// 修改
const handleEdit = (row: any) => {
    title.value = `编辑${titleStatus.value}`
    rowInfo.value = row
    dialogVisible.value = true
}
// 新增
const handleAdd = () => {
    title.value = `新增${titleStatus.value}`
    rowInfo.value = initInfo()
    dialogVisible.value = true
}

const paraDialogRef = ref<InstanceType<typeof paraDialog>>()
// 点击确定
const handleConfirm = async () => {
    const params = { ...paraDialogRef.value?.params }
    if (!params) return
    if (!params.featuresValue.featureName) {
        return ElMessage.error({ message: '请输入名称' })
    }
    const featureValues = params.featuresValue.featureValues.map((item: any) => ({
        ...item,
        secondValue: mulTenThousand(item.secondValue),
    }))
    if (params?.id) {
        // 编辑
        await dofeatureUpdate({ ...params, featuresValue: { ...params.featuresValue, featureValues } })
    } else {
        // 新增
        await dofeatureSave({ ...params, featuresValue: { ...params.featuresValue, featureValues } })
    }
    await initList()
    dialogVisible.value = false
    rowInfo.value = initInfo()
}
</script>

<style scoped lang="scss">
@include b(table) {
    overflow: auto;
    transition: height 0.5s;
}
@include b(attributeValues) {
    text-align: center;
    @include utils-ellipsis(1);
    width: 200px;
}
</style>
