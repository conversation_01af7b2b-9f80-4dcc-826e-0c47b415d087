<!--
 * @description: 备注弹窗 根据传入类型调用接口
 * @Author: lexy
 * @Date: 2022-07-24 12:23:27
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-21 18:22:47
-->
<script setup lang="ts">
import { ref, PropType, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { doPutOrderRemark, doPutAfsOrderRemark } from '@/apis/remark'
enum RemarkType {
    'AFS' = '售后订单',
    'GOODS' = '商品订单',
}
/*
 *variable
 */
const $props = defineProps({
    isShow: {
        type: Boolean,
        default() {
            return false
        },
    },
    ids: {
        type: Array as PropType<string[]>,
        default() {
            return []
        },
    },
    remark: {
        type: String,
        default: '',
    },
    remarkType: {
        type: String as PropType<keyof typeof RemarkType>,
        required: true,
    },
})
const $emit = defineEmits(['update:isShow', 'update:ids', 'success', 'update:remark'])
const _isShow = useVModel($props, 'isShow', $emit)
const remark = ref('')
/*
 *lifeCircle
 */
watch(
    () => $props.remark,
    (val) => {
        remark.value = val
    },
)
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 备注取消事件
 * @returns {*}
 */
const handleRemarkCancel = async () => {
    try {
        await ElMessageBox.confirm('取消后备注内容不被保留，是否确定?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        $emit('update:remark', '')
        $emit('update:ids', [])
        remark.value = ''
    } catch (error) {
        return
    }
    _isShow.value = false
}
/**
 * @LastEditors: lexy
 * @description: 确定
 * @returns {*}
 */
const handleRemarkEnter = async () => {
    if (!remark.value) return ElMessage.info('请输入备注')
    let result: { code: number; data: any; msg?: string }
    switch ($props.remarkType) {
        case 'GOODS':
            result = await doPutOrderRemark($props.ids, remark.value)
            break
        case 'AFS':
            result = await doPutAfsOrderRemark($props.ids, remark.value)
            break
        default:
            result = { code: 11, data: '' }
            break
    }
    if (result.code === 200) {
        ElMessage.success('备注成功')
        remark.value = ''
        $emit('update:remark', '')
        $emit('success')
        _isShow.value = false
    } else {
        ElMessage.error('备注失败')
    }
}
const handleClose = () => {
    $emit('update:remark', '')
    $emit('update:ids', [])
    remark.value = ''
    _isShow.value = false
}

// _isShow = false
</script>

<template>
    <el-dialog v-model="_isShow" width="40%" @close="handleClose">
        <template #header>
            <h3>备注</h3>
        </template>
        <div class="title">您已选择{{ $props.ids.length }}笔订单</div>
        <el-input v-model.trim="remark" :rows="6" type="textarea" placeholder="请输入备注" maxlength="150" />
        <template #footer>
            <el-button @click="handleRemarkCancel">取消</el-button>
            <el-button type="primary" @click="handleRemarkEnter">确认</el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(title) {
    font-size: 14px;
    color: #333333;
    padding: 0 0 14px 0;
}
</style>
