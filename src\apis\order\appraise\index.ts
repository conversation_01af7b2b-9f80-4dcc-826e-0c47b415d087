/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-13 14:54:02
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-13 16:50:06
 */
import { get, put, post } from '@/apis/http'
/**
 * @LastEditors: lexy
 * @description:评价管理列表
 * @returns {*}
 */
export const doGetEvaluate = (params: any) => {
    return get({
        url: 'gruul-mall-order/order/evaluate',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 批量设为精选/取消精选
 * @param {boolean} isExcellent
 * @param {string} data id[]
 * @returns {*}
 */
export const doPutIsExcellentEvaluate = (isExcellent: boolean, data: string[]) => {
    return put({
        url: `gruul-mall-order/order/evaluate/excellent/${isExcellent}`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 商家回复
 * @param {boolean} evaluateId 评估id
 * @param {string} data 回复内容
 * @returns {*}
 */
export const doPostShopReplyEvaluate = (evaluateId: string, reply: string) => {
    return post({
        url: `gruul-mall-order/order/evaluate/reply`,
        data: {
            evaluateId,
            reply,
        },
    })
}
