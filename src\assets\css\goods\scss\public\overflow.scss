/*溢出（overflow）*/

.of-auto {
    overflow: auto;
}

.of-hidden {
    overflow: hidden;
}

.of-inherit {
    overflow: inherit;
}

.of-scroll {
    overflow: scroll;
}

.of-scroll-y {
    // overflow-y: scroll;
    overflow-x: hidden;
    overflow-y: auto;
}

.of-visible {
    overflow: visible;
}

.scroll-nobar {
    overflow: hidden;
    margin-right: -17px;
    overflow-x: hidden;
    overflow-y: auto;
}


.scroll-line::-webkit-scrollbar {
    width: 0px;
}

.scroll-line::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
}

.scroll-line::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.2);
}