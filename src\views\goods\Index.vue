<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-08 09:45:24
-->
<script lang="ts" setup>
import { useRouter } from 'vue-router'
import search, { SearchType } from './components/Search.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CommodityInfo from './components/CommodityInfo.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import PurchaseDialog from './components/purchase-dialog.vue'
import InventoryDialog from './components/inventory-dialog.vue'

import { doGetCommodity, doUpdateSellStatus, doDeleteCommodity, doGetProductPurchasing } from '@/apis/good'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import type { ApiCommodityType, CommoditySpecTable } from './types'
import { ExamineGoodsEnum, SellTypeEnum } from './types'
import CommodityDetails from './details/index.vue'
import useExamineListHooks from './examine/hooks/useExamineListHooks'
const { handleCommand, showReasonDialog, rejectReson } = useExamineListHooks()
/*
 *variable
 */
const commandList = reactive([
    {
        name: 'SELL_OFF',
        label: '批量下架',
    },
    {
        name: 'delete',
        label: '批量删除',
    },
])
const sideList = reactive([
    { name: 'preview', label: '查看' },
    {
        name: 'delete',
        label: '删除',
    },
    {
        name: 'copy',
        label: '复制',
    },
    {
        name: 'purchase',
        label: '限购设置',
    },
])
const SELLONsideList = reactive([
    { name: 'preview', label: '查看' },
    {
        name: 'copy',
        label: '复制',
    },
    {
        name: 'purchase',
        label: '限购设置',
    },
])
const platformsideList = reactive([
    { name: 'preview', label: '查看' },
    {
        name: 'copy',
        label: '复制',
    },
    {
        name: 'delete',
        label: '删除',
    },
    {
        name: 'sellOff',
        label: '违规原因',
    },
])
/* zrb:审核中和审核失败操作按钮 */
const refuseList = reactive([
    { name: 'edit', label: '编辑' },
    {
        name: 'commit',
        label: '提交审核',
    },
    {
        name: 'reason',
        label: '拒绝原因',
    },
    {
        name: 'copy',
        label: '复制',
    },
])
const underReviewList = reactive([
    {
        name: 'copy',
        label: '复制',
    },
    {
        name: 'delete',
        label: '删除',
    },
])
const tableList = reactive({
    page: { size: 10, current: 1 },
    goods: [],
    total: 0,
})
const searchParams = ref({
    supplierGoodsName: '',
    platformCategoryId: '',
    supplierProductStatus: '',
})
const multiSelect = ref<ApiCommodityType[]>([])
const currentRow = ref<Partial<ApiCommodityType>>({})
const $router = useRouter()
//限购弹窗
const purchaseVisible = ref(false)
//库存弹窗
const inventoryVisible = ref(false)
// 详情弹窗
const previewVisible = ref(false)
// 当前选中行sku类型信息 true 为多规格 false 为单规格
const currentSkuIsMulti = ref<boolean>(false)
// 当前选中行sku信息
const currentSku = ref<CommoditySpecTable[]>()
// const tableHeight = ref('calc(100vh - 300px)')
const tableHeight = ref('calc(100vh - 300px)')

const violationTypeMap = {
    PROHIBITED: '违禁品',
    COUNTERFEIT: '假冒伪劣',
    EXCESSIVE_PLATFORM_INTERVENTION: '平台介入率太高',
    TITLE_IRREGULARITY: '标题有问题',
    OTHER: '其他',
}
// 违规信息
const explainData = reactive({ rummager: '', violationType: '', violationExplain: '', violationEvidence: [] as string[], examineDateTime: '' })
/*
 *lifeCircle
 */
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 库存与限购返回
 * @param {*} productId
 * @returns {*}
 */
async function initProductPurchasing(productId: string, type: 'purchase' | 'inventory') {
    const { code, data } = await doGetProductPurchasing(productId)
    if (code !== 200) return ElMessage.error('商品信息获取失败')
    currentSku.value = data.map((item: CommoditySpecTable) => {
        item.num = 0
        return item
    })
    type === 'purchase' ? (purchaseVisible.value = true) : (inventoryVisible.value = true)
}
const initList = async () => {
    const { data } = await doGetCommodity({
        ...searchParams.value,
        ...tableList.page,
    })
    tableList.total = data.total
    tableList.goods = data.records
}

const getSearch = (e: SearchType) => {
    searchParams.value = { ...searchParams.value, ...e }
    initList()
}
/**
 * @LastEditors: lexy
 * @description: 商品上下架
 * @param {boolean} isMulti
 */
const changeCommodityStatus = async (isMulti: boolean, status: string) => {
    if (multiSelect.value.length === 0 && isMulti) {
        ElMessage.error('请选择商品')
        return
    }
    let ids = isMulti ? multiSelect.value.map((item) => item.id) : [currentRow.value.id as string]
    const { code, success } = await doUpdateSellStatus(ids, status)
    if (code === 200 && success) {
        ElMessage.success('更新成功')
        if (!isMulti) return true
        else initList()
    }
    if (!isMulti) return false
}
const delCommodity = (isMulti: boolean) => {
    if (isMulti && multiSelect.value.length === 0) {
        ElMessage.error('请选择商品')
        return
    }
    ElMessageBox.confirm('确定需要删除商品？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
    }).then(async () => {
        let ids = isMulti ? multiSelect.value.map((item) => item.id) : [currentRow.value.id as string]
        const { code, success, msg } = await doDeleteCommodity(ids.join(','))
        if (code === 200 && success) {
            ElMessage.success('删除成功')
            initList()
        } else {
            ElMessage.error(msg)
        }
    })
}
const handleDel = (isMulti: boolean, row: ApiCommodityType) => {
    currentRow.value = row
    delCommodity(isMulti)
}
const batchOptionHandle = (e: string) => {
    e === 'delete' ? delCommodity(true) : changeCommodityStatus(true, e)
}
/**
 * @LastEditors: lexy
 * @description: 操作下拉
 * @param {*} e
 * @returns {*}
 */
const singleOptionHandle = (e: string, row: ApiCommodityType) => {
    currentRow.value = row
    if (currentRow.value?.id) {
        if (e === 'purchase') {
            currentSkuIsMulti.value = currentRow.value.multipleSpecs
            initProductPurchasing(currentRow.value.id, 'purchase')
            return
        } else if (e === 'inventory') {
            initProductPurchasing(currentRow.value.id, 'inventory')
            return
        } else if (e === 'copy') {
            copyHandle(currentRow.value.id)
            return
        } else if (e === 'preview') {
            previewHandle(row)
            return
        } else if (e === 'commit' || e === 'reason') {
            handleCommand(e, currentRow.value)
            return
        } else if (e === 'edit') {
            editHandle(currentRow.value.id)
            return
        } else if (e === 'SELL_OFF' || e === 'SELL_ON') {
            return changeCommodityStatus(false, e)
        } else if (e === 'delete') {
            delCommodity(false)
            return
        }
    }
}

const editHandle = (id: string) => {
    $router.push({
        path: '/goods/list/edit',
        query: {
            id,
        },
    })
}
const copyHandle = (id: string) => {
    $router.push({
        path: '/goods/list/edit',
        query: {
            id,
            isCopy: true,
        },
    })
}
const handleClosePurchase = (e: 'refresh') => {
    if (e) {
        // 设置限购成功后刷新列表
        initList()
    }
    purchaseVisible.value = false
}
const handleCloseInventory = (e: 'refresh') => {
    if (e) {
        // 设置库存成功后刷新列表
        initList()
    }
    inventoryVisible.value = false
}
// async function initSkuList() {
//     const { id, shopId } = currentRow.value
//     const { code, success, data } = await doGetCommoditySku(shopId, id)
//     if (code === 200 && success) {
//         currentSku.value = data.skus.map((item) => {
//             item.num = 0
//             return item
//         })
//     }
// }
/**
 * @LastEditors: lexy
 * @description: 列表库存计算
 */
const handleComputeStocks = (storageSkus: CommoditySpecTable[]) => {
    console.log(storageSkus)
    let show = ''
    if (
        storageSkus?.every((item) => {
            return item.stockType === 'UNLIMITED'
        })
    ) {
        show = '无限库存'
    } else {
        show =
            storageSkus?.reduce((pre, cur) => {
                return pre + Number(cur.stock)
            }, 0) + ''
    }
    return show
}
/**
 * @LastEditors: lexy
 * @description: 判断商品规格是否不足10个
 */
const handleStockLess = (storageSkus: CommoditySpecTable[]) => {
    const filterLimitStockArr = storageSkus?.filter((item) => {
        return item.stockType === 'LIMITED'
    })
    const checkLimitStockArr = filterLimitStockArr?.filter((item) => {
        return Number(item.stock) <= 10
    })
    if (!filterLimitStockArr.length || !checkLimitStockArr.length) return ''
    return filterLimitStockArr.length === checkLimitStockArr.length ? '库存不足' : '部分库存不足'
}
const handleSearchShow = (e: boolean) => {
    if (e) {
        tableHeight.value = 'calc(100vh - 440px)'
    } else {
        tableHeight.value = 'calc(100vh - 300px)'
    }
}
// 计算已售
const computedSalesVolume = (storageSkus: CommoditySpecTable[]) => {
    return storageSkus?.reduce((pre, cur) => {
        return pre + Number(cur.salesVolume)
    }, 0)
}

// 违规原因弹框
const sellOffVisible = ref(false)
// 违规商品点击更多
const handleCheck = (status: string, row: ApiCommodityType) => {
    if (status === 'delete') {
        handleDel(false, row)
    } else if (status === 'copy') {
        copyHandle(row.id)
    } else if (status === 'preview') {
        previewHandle(row)
    } else {
        const productViolation = row?.extra?.productViolation || {}
        Object.keys(explainData).forEach((key) => {
            const explainDataKey = key as keyof typeof explainData
            if (explainDataKey === 'violationEvidence') {
                explainData.violationEvidence = productViolation?.violationEvidence ? productViolation.violationEvidence.split(',') : []
            } else if (explainDataKey === 'violationType') {
                const list: string[] = []
                productViolation?.violationType?.forEach((item: keyof typeof violationTypeMap) => {
                    list.push(violationTypeMap[item])
                })
                explainData.violationType = list.join(',')
            } else {
                explainData[key] = productViolation?.[key] || ''
            }
        })
        sellOffVisible.value = true
    }
}

const goodsStatus = {
    全部: '',
    已上架: 'SELL_ON',
    已下架: 'SELL_OFF',
    已拒绝: 'REFUSE',
    违规下架: 'PLATFORM_SELL_OFF',
}

const currentTab = ref<'' | 'SELL_ON' | 'SELL_OFF' | 'REFUSE' | 'PLATFORM_SELL_OFF'>('')

const selectableFunc = (data: any) => {
    return data.supplierProductStatus !== ExamineGoodsEnum.REFUSE && data.supplierProductStatus !== ExamineGoodsEnum.UNDER_REVIEW
}

//tab点击事件
const handleTabClick = (status: '' | 'SELL_ON' | 'SELL_OFF' | 'PLATFORM_SELL_OFF') => {
    searchParams.value.supplierProductStatus = status
    initList()
}
const handleUpdatePrice = (e: any[], v: any, r: any) => {
    r.storageSkus[0].salePrice = e[0].salePrice
}
const handleUpdatename = (e: any, r: any) => {
    r.productName = e
}
const previewHandle = (row: any) => {
    currentRow.value = row
    previewVisible.value = true
}
</script>
<template>
    <search @on-search-params="getSearch" @change-show="handleSearchShow" />
    <!-- <Area /> -->
    <!-- <tab-line default-active="全部" :line-arr="Object.keys(goodsStatus)" @tab-click="handleTabClick" /> -->
    <el-tabs v-model="currentTab" style="margin-top: 15px" @tab-change="handleTabClick">
        <el-tab-pane v-for="(item, key) in goodsStatus" :key="item" :label="key" :name="item"></el-tab-pane>
    </el-tabs>
    <div class="tool">
        <router-link to="/goods/list/new">
            <el-button style="margin-right: 10px" type="primary" round>发布商品</el-button>
        </router-link>
        <q-dropdown-btn title="批量上架" :option="commandList" @left-click="changeCommodityStatus(true, 'SELL_ON')" @right-click="batchOptionHandle" />
    </div>
    <q-table v-model:checkedItem="multiSelect" :data="tableList.goods" :style="{ height: tableHeight }" :selection="true" :selectable="selectableFunc" class="table">
        <q-table-column label="商品" align="left" width="320px">
            <template #default="scope">
                <CommodityInfo :info="scope.row" @update-price="(e:any[], v:any) => handleUpdatePrice(e, v, scope.row)" @update-name="(e:any) => handleUpdatename(e,scope.row)" />
            </template>
        </q-table-column>
        <q-table-column label="库存" align="center" style="flex-direction: column" width="100px">
            <template #default="{ row }">
                <div v-if="row.storageSkus?.length" style="position: relative; width: 100%; text-align: center">
                    {{ handleComputeStocks(row.storageSkus) }}
                    <div v-if="handleComputeStocks(row.storageSkus) !== '无限库存'" class="edit" style="cursor: pointer" @click="() => singleOptionHandle('inventory', row)">
                        <q-icon name="icon-bianji_o" color="#333"></q-icon>
                    </div>
                </div>
                <div class="warning">
                    {{ handleStockLess(row.storageSkus) }}
                </div>
            </template>
        </q-table-column>
        <q-table-column label="已售" align="center" width="100px">
            <template #default="{ row }">
                <div>{{ computedSalesVolume(row.storageSkus) }}</div>
            </template>
        </q-table-column>
        <q-table-column label="销售方式" align="center" width="100px">
            <template #default="{ row }">
                <div>{{ SellTypeEnum[row.sellType] }}</div>
            </template>
        </q-table-column>
        <q-table-column label="上下架" align="center" width="80px">
            <template #default="{ row }">
                <!-- https://github.com/element-plus/element-plus/issues/12657 -->
                <!-- 存在违规下架的参数值，因违规下架的参数值为PLATFORM_SELL_OFF，既不属于SELL_ON，也不属于SELL_OFF
                    因此会导致switch组件触发一次change事件，导致当前v-model绑定的row.status强制置为否定项也就是SELL_OFF，从而出现组件下架状态渲染错误的情况
                -->
                <div v-if="['SELL_ON', 'SELL_OFF'].includes(row.status)">
                    <el-switch
                        v-model="row.status"
                        inline-prompt
                        active-text="下架"
                        inactive-text="上架"
                        active-value="SELL_ON"
                        inactive-value="SELL_OFF"
                        :before-change="() => singleOptionHandle(row.status === 'SELL_ON' ? 'SELL_OFF' : 'SELL_ON', row)"
                    />
                </div>
                <div v-else>
                    <el-switch model-value="SELL_OFF" inline-prompt active-text="下架" inactive-text="上架" active-value="SELL_ON" inactive-value="SELL_OFF" disabled />
                </div>
            </template>
        </q-table-column>
        <q-table-column label="状态" align="center" width="80px">
            <template #default="scope">
                <div v-if="scope.row.status === 'SELL_ON'" class="sell sell__on">已上架</div>
                <div v-else-if="scope.row.status === 'PLATFORM_SELL_OFF'" class="sell sell__platform">违规下架</div>
                <div v-else class="sell sell__off">已下架</div>
            </template>
        </q-table-column>
        <q-table-column label="审核状态" align="center" width="80px">
            <template #default="{ row }">
                {{ ExamineGoodsEnum[row?.supplierProductStatus as keyof typeof ExamineGoodsEnum] }}
            </template>
        </q-table-column>
        <q-table-column label="操作" align="center" width="180px">
            <template #default="scope">
                <q-dropdown-btn
                    v-if="scope.row.status === 'PLATFORM_SELL_OFF'"
                    title="编辑"
                    :option="platformsideList"
                    @right-click="(e: any) => handleCheck(e, scope.row)"
                    @left-click="editHandle(scope.row.id)"
                />
                <q-dropdown-btn
                    v-else-if="scope.row.status === 'REFUSE'"
                    title="查看"
                    :option="refuseList"
                    @right-click="(e: any) => singleOptionHandle(e, scope.row)"
                    @left-click="previewHandle(scope.row.id)"
                />
                <q-dropdown-btn
                    v-else-if="scope.row.status === 'SELL_ON'"
                    title="编辑"
                    :option="SELLONsideList"
                    @right-click="(e: any) => singleOptionHandle(e, scope.row)"
                    @left-click="editHandle(scope.row.id)"
                />
                <q-dropdown-btn
                    v-else-if="scope.row.status === 'UNDER_REVIEW'"
                    title="查看"
                    :option="underReviewList"
                    @right-click="(e: any) => singleOptionHandle(e, scope.row)"
                    @left-click="previewHandle(scope.row)"
                />
                <q-dropdown-btn
                    v-else
                    title="编辑"
                    :option="sideList.filter((item) => item.name !== scope.row.status)"
                    @right-click="(e: any) => singleOptionHandle(e, scope.row)"
                    @left-click="editHandle(scope.row.id)"
                />
            </template>
        </q-table-column>
    </q-table>
    <PageManage v-model="tableList.page" load-init :total="tableList.total" @reload="initList" />
    <purchase-dialog :show="purchaseVisible" :sku="currentSku" :sku-is-multi="currentSkuIsMulti" :product-id="currentRow && currentRow.id" @close="handleClosePurchase" />
    <inventory-dialog :show="inventoryVisible" :sku="currentSku" :product-id="currentRow && currentRow.id" @close="handleCloseInventory" />
    <el-dialog v-model="sellOffVisible" title="违规原因" width="500px" center>
        <div style="line-height: 30px">
            <el-row :gutter="8">
                <el-col :span="12">
                    <div>检查员：{{ explainData.rummager }}</div>
                </el-col>
                <el-col :span="12">
                    <div>检查时间：{{ explainData.examineDateTime }}</div>
                </el-col>
            </el-row>
            <div>类型：{{ explainData.violationType }}</div>
            <div>说明：{{ explainData.violationExplain }}</div>
            <div>
                相关证据：
                <el-image
                    v-for="(pic, picIndex) in explainData.violationEvidence"
                    :key="picIndex"
                    :src="pic"
                    class="violation-evidence"
                    :preview-src-list="explainData.violationEvidence"
                    :initial-index="picIndex"
                />
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="previewVisible" title="商品详情" center width="900px" top="5vh" destroy-on-close>
        <CommodityDetails :commodity-id="currentRow.id" />
    </el-dialog>
    <el-dialog v-model="showReasonDialog" title="拒绝理由" center destroy-on-close>
        <span>原因：{{ rejectReson }}</span>
    </el-dialog>
</template>
<style lang="scss">
@include b(tool) {
    height: 70px;
    @include flex(flex-start);
}

@include b(warning) {
    color: #e6a23c;
    margin-top: 5px;
}

@include b(table) {
    overflow: auto;
    transition: height 0.5s;
}

@include b(sell) {
    width: 76px;
    height: 26px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    line-height: 26px;

    @include e(on) {
        background: #f5faf3;
        color: #82c26b;
    }

    @include e(off) {
        background: #fef6f3;
        color: #f4a584;
    }

    @include e(platform) {
        background: #ffefee;
        color: #f12f22;
    }
}
.violation-evidence {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: contain;
}
.violation-evidence + .violation-evidence {
    margin-left: 5px;
}
</style>
