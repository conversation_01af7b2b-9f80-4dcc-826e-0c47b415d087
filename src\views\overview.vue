<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-22 13:33:58
-->
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import QIcon from '@/components/q-icon/q-icon.vue'
import * as echarts from 'echarts/core'
import { GridComponent, GridComponentOption, TooltipComponent, LegendComponent, ToolboxComponent } from 'echarts/components'
import { LineChart, LineSeriesOption } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

import DateUtil from '@/utils/date'
import { doGetTradeVolume, doGetTransactionAmount, doGetVisitNumber, doGetNewCommodityNumber, doGetTradeStatistics, doGetOrderInfo } from '@/apis/overview'
import type { DATE_TYPE } from '@/apis/overview'
import { ElMessage } from 'element-plus'
echarts.use([Grid<PERSON>omponent, LineChart, CanvasRenderer, UniversalTransition, TooltipComponent, LegendComponent, ToolboxComponent])

type EChartsOption = echarts.ComposeOption<GridComponentOption | LineSeriesOption>
type GoodItemType = {
    productId: string
    productName: string
    shopId: string
    amount: number
    number: number
}
type RankAggregationType = {
    tradeVolumeList: GoodItemType[]
    tradeAmountList: GoodItemType[]
    tradeVolumeIndex: number
    tradeAmountIndex: number
}
type TradeStatisticItem = {
    XDate: string
    tradeAmount: number
    tradeNumber: number
}
/*
 *variable
 */
const { divTenThousand } = useConvert()
// 新增商品数量
const newGoodsCount = ref(-1)
// const visitUserCount = ref(-1)
const orderCountInfo = ref({
    pendingPaymentOrders: 0,
    pendingDeliveredOrders: 0,
    pendingReceivedOrders: 0,
})
const rankAggregation = reactive<RankAggregationType>({
    tradeVolumeList: [],
    tradeAmountList: [],
    tradeVolumeIndex: 0,
    tradeAmountIndex: 0,
})
const $router = useRouter()
// 今天访客数
const visitUserNumber = ref(-1)
// 交易统计筛选
const tradeFilterTime = ref([])
const enumTimeArr: { title: string; value: keyof typeof DATE_TYPE }[] = [
    { title: '今天', value: 'TODAY' },
    { title: '近七天', value: 'NEARLY_A_WEEK' },
    { title: '近一个月', value: 'NEARLY_A_MONTH' },
]
const dateTool = new DateUtil()
const echartsRef = ref()
/*
 *lifeCircle
 */

onMounted(() => {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const chartNode = document.getElementById('main')!
    const tradeChart = echarts.init(chartNode)
    echartsRef.value = tradeChart
})
// 获取商品交易量rank
initRankOftradeVolume()
// 获取商品交易额rank
initRankOftradeAmount()
// 获取访客数
initVisitNumber()
// 获取新增商品数量
initNewGoodsNumber()
// 获取交易统计
initTradeStatistis()
// 获取订单信息
initOrderInfo()

/*
 *function
 */
const handleChangeVolumeIndex = (idx: number) => {
    rankAggregation.tradeVolumeIndex = idx
    initRankOftradeVolume()
}
const handleChangeTradePick = (e: Date[]) => {
    e ? initTradeStatistis(dateTool.getYMDs(e[0]), dateTool.getYMDs(e[1])) : initTradeStatistis()
}
const handleChangeAmountIndex = (idx: number) => {
    rankAggregation.tradeAmountIndex = idx
    initRankOftradeAmount()
}
const handleNavToUnpaid = () => {
    $router.push({
        name: 'orderIndex',
        query: {
            name: 'UNPAID',
        },
    })
}
const handleNavToUnDelivery = () => {
    $router.push({
        name: 'orderIndex',
        query: {
            name: 'UN_DELIVERY',
        },
    })
}
const handleNavToUnReceive = () => {
    $router.push({
        name: 'orderIndex',
        query: {
            name: 'UN_RECEIVE',
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取交易量rank
 * @returns {*}
 */
async function initRankOftradeVolume() {
    const { code, data } = await doGetTradeVolume({
        dateRangeType: enumTimeArr[rankAggregation.tradeVolumeIndex].value,
        tradeStaticType: 'TRADE_NUMBER',
    })
    if (code !== 200) return ElMessage.error('获取商品交易量失败')
    if (data) {
        rankAggregation.tradeVolumeList = data
    } else {
        rankAggregation.tradeVolumeList = []
    }
}
/**
 * @LastEditors: lexy
 * @description: 获取交易额rank
 */
async function initRankOftradeAmount() {
    const { code, data } = await doGetTransactionAmount({
        dateRangeType: enumTimeArr[rankAggregation.tradeAmountIndex].value,
        tradeStaticType: 'TRADE_AMOUNT',
    })
    if (code !== 200) return ElMessage.error('获取商品交易额失败')
    if (data) {
        rankAggregation.tradeAmountList = data
    } else {
        rankAggregation.tradeAmountList = []
    }
}
/**
 * @LastEditors: lexy
 * @description: 获取新增咨询数
 */
async function initVisitNumber() {
    const { code, data } = await doGetVisitNumber()
    if (code !== 200) return ElMessage.error('获取咨询数失败')
    visitUserNumber.value = data
}
/**
 * @LastEditors: lexy
 * @description: 获取新增商品数量
 */
async function initNewGoodsNumber() {
    const { code, data } = await doGetNewCommodityNumber()
    if (code !== 200) return ElMessage.error('获取新增商品数失败')
    newGoodsCount.value = data
}
async function initEcharts(data: TradeStatisticItem[]) {
    let xAxisData = []
    // 交易量数组
    let seriesTradeVolumeArr: number[] = []
    // 交易额数组
    let seriesTradeAmountArr: string[] = []
    // 记录表格左侧数值最大数
    let recordMaxNumber = 0
    for (let i = 0; i < data.length; i++) {
        xAxisData.push(data[i].XDate)
        seriesTradeVolumeArr.push(data[i].tradeNumber)
        seriesTradeAmountArr.push(String(divTenThousand(data[i].tradeAmount)))
        if (recordMaxNumber < Number(divTenThousand(data[i].tradeAmount))) {
            recordMaxNumber = Number(divTenThousand(data[i].tradeAmount))
        }
    }
    console.log(recordMaxNumber, 'recordMaxNumber')
    let option: EChartsOption = {
        xAxis: {
            type: 'category',
            data: xAxisData,
        },
        yAxis: {
            type: 'value',
            name: '单位:元',
        },
        series: [
            {
                data: seriesTradeVolumeArr,
                name: '交易量',
                type: 'line',
                smooth: true,
            },
            {
                data: seriesTradeAmountArr,
                name: '交易额',
                type: 'line',
                smooth: true,
            },
        ],
        grid: [
            {
                show: false,
                z: 0,
                left: computeGridWidth(String(parseInt(String(recordMaxNumber))).length),
                top: 30,
                right: '7%',
                bottom: 40,
                containLabel: false,
                backgroundColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 1,
                borderColor: '#ccc',
            },
        ],
        legend: {
            data: ['交易量', '交易额'],
        },
        tooltip: {
            trigger: 'item',
            axisPointer: {
                type: 'cross',
            },
        },
    }
    echartsRef.value.setOption(option)
}
/**
 * @LastEditors: lexy
 * @description: 获取交易统计
 */
async function initTradeStatistis(startTime?: string, endTimes?: string) {
    const currentStamp = new Date().getTime()
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const beginTime = !startTime && !endTimes ? dateTool.getSubtracteDays(currentStamp, 7) : startTime!
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const endTime = !startTime && !endTimes ? dateTool.getYMDs(currentStamp) : endTimes!
    const { code, data } = await doGetTradeStatistics({
        dateRangeType: 'CUSTOM',
        beginTime,
        endTime,
    })
    if (code !== 200) return ElMessage.error('获取交易统计失败')
    const enumTimeArr = computeDateDiff(new Date(beginTime), new Date(endTime))
    if (!enumTimeArr.length) return ElMessage.error('日期未存在区间')
    const mapData = new Map<string, TradeStatisticItem>(data.map((value: TradeStatisticItem) => [value.XDate, value]))
    const resultArr = enumTimeArr.map((item) => {
        if (mapData.has(item.XDate)) {
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            return mapData.get(item.XDate)!
        }
        return item
    })
    initEcharts(resultArr)
}
/**
 * @LastEditors: lexy
 * @description: 获取订单信息
 */
async function initOrderInfo() {
    const { code, data } = await doGetOrderInfo()
    if (code !== 200) return ElMessage.error('获取订单信息失败')
    orderCountInfo.value = data
}
/**
 * @LastEditors: lexy
 * @description: 计算canvas grid宽度
 * @param {number} len 最大数位数
 */
function computeGridWidth(len: number) {
    return len <= 2 ? len * 30 : len * 12
}
/**
 * @LastEditors: lexy
 * @description: 计算时间戳相差天数
 * @param {number} startStamp 开始时间戳
 * @param {number} endStamp 结束时间戳
 */
function computeDateDiff(startStamp: Date, endStamp: Date) {
    // 一天时间戳
    const wholeDayStamp = 24 * 60 * 60 * 1000
    if (startStamp === endStamp) {
        return []
    }
    if (Math.abs(startStamp.getTime() - endStamp.getTime()) < wholeDayStamp) {
        return []
    }
    // 开始整点时间戳
    const startWholeStamp = startStamp.setHours(0, 0, 0, 0)
    // 结束整点时间戳
    const endWholeStamp = endStamp.setHours(0, 0, 0, 0)
    const allDayArr: TradeStatisticItem[] = []
    let diffWholeStamp = endWholeStamp - startWholeStamp
    while (diffWholeStamp >= 0) {
        allDayArr.push({
            XDate: dateTool.getYMDs(endWholeStamp - diffWholeStamp),
            tradeAmount: 0,
            tradeNumber: 0,
        })
        diffWholeStamp -= wholeDayStamp
    }
    return allDayArr
}
</script>

<template>
    <div class="overview">
        <div style="display: flex">
            <div class="survey">
                <span class="title">实时概况</span>
                <div class="survey__content">
                    <div class="survey__item">
                        <div>今日咨询数</div>
                        <div class="survey__item--num">{{ visitUserNumber }}</div>
                    </div>
                    <div class="survey__item">
                        <div>今日新增商品数</div>
                        <div class="survey__item--num">{{ newGoodsCount }}</div>
                    </div>
                </div>
            </div>
            <div class="statistics">
                <div class="statistics__top">
                    <span class="title">交易统计</span>
                    <el-date-picker
                        v-model="tradeFilterTime"
                        type="daterange"
                        style="width: 213px; flex-grow: 0"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        @change="handleChangeTradePick"
                    />
                </div>
                <div id="main" style="height: 392px; width: 660px"></div>
            </div>
        </div>
        <div class="schedule">
            <span class="title">今日待办</span>
            <div class="schedule__content">
                <div class="schedule__item" @click="handleNavToUnpaid">
                    <div style="display: flex; align-items: center">
                        <!-- <div class="schedule__item--icon"></div> -->
                        <q-icon size="28px" class="schedule__item--icon" name="icon-shangpinweigui" />
                        <div>待付款订单</div>
                    </div>
                    <div class="schedule__item--num">{{ orderCountInfo.pendingPaymentOrders }}</div>
                </div>
                <div class="schedule__item" @click="handleNavToUnDelivery">
                    <div style="display: flex; align-items: center">
                        <!-- <div class="schedule__item--icon"></div> -->
                        <q-icon size="28px" class="schedule__item--icon" name="icon-dianpu1" />
                        <div>待发货订单</div>
                    </div>
                    <div class="schedule__item--num">{{ orderCountInfo.pendingDeliveredOrders }}</div>
                </div>
                <div class="schedule__item" @click="handleNavToUnReceive">
                    <div style="display: flex; align-items: center">
                        <!-- <div class="schedule__item--icon"></div> -->
                        <q-icon size="28px" class="schedule__item--icon" name="icon-dianpu1" />
                        <div>待收货订单</div>
                    </div>
                    <div class="schedule__item--num">{{ orderCountInfo.pendingReceivedOrders }}</div>
                </div>
            </div>
        </div>
        <div style="display: flex">
            <div class="shop">
                <div class="com">
                    <span class="title">商品交易量TOP10</span>
                    <div class="com__top">
                        <span
                            v-for="(item, index) in enumTimeArr"
                            :key="item.title"
                            :class="[rankAggregation.tradeVolumeIndex === index && 'com__top--active']"
                            @click="handleChangeVolumeIndex(index)"
                            >{{ item.title }}</span
                        >
                    </div>
                </div>
                <el-table
                    :data="rankAggregation.tradeVolumeList"
                    style="width: 100%; margin-top: 18px"
                    :header-row-style="{
                        width: '439px',
                    }"
                    :header-cell-style="{
                        background: '#f7f8fa',
                        fontSize: '12px',
                        fontWeight: 400,
                        height: '30px',
                        padding: 0,
                        color: '#797979',
                    }"
                    table-layout="fixed"
                    :cell-style="{ fontSize: '12px', color: '#797979' }"
                >
                    <el-table-column label="排名">
                        <template #default="{ $index }">
                            <div style="color: #797979">{{ $index + 1 }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" width="180">
                        <template #default="{ row }">
                            <div class="shopName-overflow">{{ row.productName }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="销量" align="center">
                        <template #default="{ row }">
                            <div>{{ row.number }}</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div class="goods">
                <div class="com">
                    <span class="title">商品交易额TOP10</span>
                    <div class="com__top">
                        <span
                            v-for="(item, index) in enumTimeArr"
                            :key="item.title"
                            :class="[rankAggregation.tradeAmountIndex === index && 'com__top--active']"
                            @click="handleChangeAmountIndex(index)"
                            >{{ item.title }}</span
                        >
                    </div>
                </div>
                <el-table
                    :data="rankAggregation.tradeAmountList"
                    style="width: 100%; margin-top: 18px"
                    :header-row-style="{
                        width: '439px',
                    }"
                    :header-cell-style="{
                        background: '#f7f8fa',
                        fontSize: '12px',
                        fontWeight: 400,
                        height: '30px',
                        padding: 0,
                        color: '#797979',
                    }"
                    table-layout="fixed"
                    :cell-style="{ fontSize: '12px', color: '#797979' }"
                >
                    <el-table-column label="排名">
                        <template #default="{ $index }">
                            <div style="color: #797979">{{ $index + 1 }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" width="180">
                        <template #default="{ row }">
                            <div class="shopName-overflow">{{ row.productName }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="实付金额" align="center">
                        <template #default="{ row }">
                            <div>￥{{ divTenThousand(row.amount) }}</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@include b(overview) {
    background: #f2f2f2;
    margin: -20px -15px;
    // height: calc(100vh - 85px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}
@include b(com) {
    cursor: pointer;
    @include flex(space-between);
    @include e(top) {
        width: 130px;
        @include flex(space-between);
        @include m(active) {
            position: relative;
            &::after {
                content: '';
                display: block;
                width: 100%;
                position: absolute;
                bottom: -4px;
                left: 0;
                border: 1px solid #7a7cfd;
            }
        }
    }
}
@include b(statistics) {
    width: 682px;
    height: 445px;
    background: #ffffff;
    padding: 7px 20px 0 30px;
    @include e(top) {
        height: 46px;
        @include flex(space-between);
    }
}

@include b(survey) {
    margin-right: 10px;
    height: 445px;
    width: 319px;
    padding-left: 33px;
    padding-right: 33px;
    padding-top: 20px;
    background: #fff;
    margin-bottom: 10px;

    @include e(content) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    @include e(item) {
        margin-top: 50px;
        font-size: 12px;
        width: 125px;

        @include m(num) {
            font-size: 24px;
            font-weight: bold;
            margin-top: 15px;
            color: #515151;
        }
    }
}

@include b(schedule) {
    height: 183px;
    color: #7676c4;
    padding-left: 29px;
    padding-right: 85px;
    padding-top: 20px;
    background: #fff;
    margin-bottom: 10px;

    @include e(content) {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    @include e(item) {
        margin-top: 38px;
        padding-left: 28px;
        border-radius: 5px;
        font-size: 12px;
        cursor: pointer;
        @include m(icon) {
            width: 29px;
            height: 29px;
            margin-right: 6px;
            color: #f3bf79;
        }

        @include m(num) {
            font-size: 24px;
            font-weight: bold;
            margin-top: 9px;
            color: #515151;
            margin-left: 30px;
        }
    }
}

@include b(shop) {
    width: 501px;
    // height: 291px;
    background: #ffffff;
    border-radius: 0px;
    font-size: 12px;
    color: #797979;
    padding: 20px 30px 0;
    margin-right: 10px;

    @include e(head) {
        width: 439px;
        height: 30px;
        background: #f7f8fa;
        display: flex;
        align-items: center;
        margin-top: 18px;

        @include m(ranking) {
            margin-left: 23px;
        }

        @include m(shopname) {
            margin-left: 39px;
        }

        @include m(turnover) {
            margin-left: 203px;
        }
    }

    @include e(content) {
        width: 439px;
        height: 30px;
        display: flex;
        align-items: center;
        margin-top: 10px;

        @include m(ranking) {
            margin-left: 23px;
            width: 10px;
        }

        @include m(turnover) {
            flex: 1;
            text-align: right;
            margin-right: 85px;
        }
    }
}

@include b(goods) {
    width: 501px;
    // height: 291px;
    background: #ffffff;
    border-radius: 0px;
    font-size: 12px;
    color: #797979;
    padding: 20px 30px 0;

    @include e(head) {
        width: 439px;
        height: 30px;
        background: #f7f8fa;
        display: flex;
        align-items: center;
        margin-top: 18px;

        @include m(ranking) {
            margin-left: 23px;
        }

        @include m(goodsname) {
            margin-left: 39px;
        }

        @include m(money) {
            margin-left: 203px;
        }
    }

    @include e(content) {
        width: 439px;
        height: 30px;
        display: flex;
        align-items: center;
        margin-top: 10px;

        @include m(ranking) {
            margin-left: 23px;
            width: 10px;
        }

        @include m(goodsname) {
            cursor: pointer;
            margin-left: 52px;
            width: 153px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        @include m(money) {
            margin-left: 100px;
            width: 108px;
        }
    }
}

.title {
    font-size: 14px;
    color: #515151;
    font-weight: bold;
}

.basicgoods {
    background: #f7f5ff;
}

.basicshops {
    background: #f5fffc;
}

.basicprders {
    background: #fff9f1;
}
@include b(shopName-overflow) {
    width: 153px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
</style>
