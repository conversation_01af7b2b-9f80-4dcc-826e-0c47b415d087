<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-08 22:28:56
 * @LastEditors: lexy
 * @LastEditTime: 2022-06-15 14:48:31
-->
<script setup lang="ts">
import { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    prop: {
        type: String,
        default: '',
    },
    align: {
        type: String,
        default: 'center',
    },
    hasSlots: {
        type: Boolean,
        default: false,
    },
    row: {
        type: Object as PropType<any>,
        default() {
            return {}
        },
    },
})
/*
 *lifeCircle
 */
/*
 *function
 */
const transformAlign = (val: string) => {
    switch (val) {
        case 'left':
            return 'flex-start'
        case 'right':
            return 'flex-end'
        default:
            return 'center'
    }
}
</script>

<template>
    <div class="item__content" :style="{ justifyContent: transformAlign($props.align) }">
        <slot v-if="$props.hasSlots" :row="$props.row"></slot>
        <div v-else>{{ $props.row[$props.prop] }}</div>
    </div>
</template>

<style lang="scss" scoped></style>
