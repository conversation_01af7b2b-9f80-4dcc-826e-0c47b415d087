/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-11 10:46:11
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-02 11:25:22
 */
import { get, post } from '../http'
export const doGetFinance = (data: any) => {
    return post({
        url: 'gruul-mall-overview/overview/statement',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 查询店铺余额
 */
export const doGetShopBalance = () => {
    return get({
        url: 'gruul-mall-overview/overview/withdrawYts',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取店铺提现申请列表
 * @returns {*}
 */

export const doGetWithdrawList = (params: any) => {
    return get({
        url: 'gruul-mall-overview/overview/withdrawYts/page',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 提现申请
 */
export const doPostWithdraw = (amount: number, type: string) => {
    return post({
        url: 'gruul-mall-overview/overview/withdrawYts/withdraw',
        data: {
            amount,
            type,
        },
    })
}
