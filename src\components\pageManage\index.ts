/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-29 10:19:39
 * @LastEditors: lexy
 * @LastEditTime: 2022-05-12 23:11:56
 */
import { reactive } from 'vue'
/**
 * 分页参数
 */
export interface Page {
    size: number
    current: number
}

interface DefaultConfig extends Page {
    sizes: number[]
    total: number
}

export const defaultConfig = reactive<DefaultConfig>({
    size: 10,
    current: 1,
    total: 0,
    sizes: [10, 20, 50, 100],
})

export interface PageResult {
    page: Page | Record<string, never>
    total: number
    records: Array<any>
}

/**
 * 分页执行结果信息
 */
export interface IPage<T> extends Page {
    /**
     * 查询数据列表
     */
    records?: Array<T>
    /**
     * 总数
     */
    total?: number
}
