<template>
    <div class="search">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.name" placeholder="请输入商品名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台类目">
                            <el-cascader v-model="searchType.platformCategoryId" style="width: 224px"
                                :options="categoryList" :props="shopCascaderProps" placeholder="请选择展示分类"
                                :show-all-levels="false" clearable @change="handleChangeCascader" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="销售方式">
                            <el-select v-model="searchType.sellType" placeholder="请选择" style="width: 224px">
                                <el-option v-for="item in typeList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import MCard from '@/components/MCard.vue'
import { doGetPlatformCategory } from '@/apis/good'
import { CascaderInstance } from 'element-plus'

const isShow = ref(false)
const categoryList = ref<any[]>([])
const shopCascaderProps = {
    expandTrigger: 'hover' as 'click' | 'hover',
    label: 'name',
    value: 'id',
}
const typeList = [
    {
        value: '',
        label: '全部',
    },
    {
        value: 'PURCHASE',
        label: '采购商品',
    },
    {
        value: 'CONSIGNMENT',
        label: '代销商品',
    },
]
const $emit = defineEmits(['onSearchParams', 'changeShow'])
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
const searchType = ref({
    name: '',
    platformCategoryId: '',
    secondPlatformCategoryId: '',
    sellType: '',
})

const handleChangeCascader = (e: any) => {
    searchType.value.secondPlatformCategoryId = ''
    searchType.value.platformCategoryId = ''
    if (!e ||e.length === 0) return;

    let categoryId = e?.pop() || ''
    if (e.length < 2 && categoryId !== '0') {
        searchType.value.secondPlatformCategoryId = categoryId
    }
    searchType.value.platformCategoryId = categoryId
}
async function init() {
    const { data, code } = await doGetPlatformCategory()
    categoryList.value = checkCategoryEnable(1, data, 2)
}
/**
 * 检查是分类否可用
 * @param currentLevel   当前的层级
 * @param records        数据集
 * @param lastLevel      最低允许可见的层级（不建议低于2级）
 */
function checkCategoryEnable(currentLevel: number, records: any[], limitLevel?: number) {
    const realLimit = limitLevel || 3
    const isAllowedLevel = currentLevel >= realLimit
    for (let index = 0; index < records.length;) {
        const record = records[index]

        const children = (record.children || record.secondCategoryVos || record.categoryThirdlyVos) as any[]
        delete record.secondCategoryVos
        delete record.categoryThirdlyVos
        record.children = children

        if (isAllowedLevel) {
            record.disabled = false
            index++
            continue
        }
        const disable = !children || children.length === 0
        record.disabled = disable
        if (disable) {
            records.splice(index, 1)
            continue
        }
        checkCategoryEnable(currentLevel + 1, children, realLimit)
        if (children.length === 0) {
            records.splice(index, 1)
            continue
        }
        index++
    }

    return records
}

function search() {
    const { platformCategoryId, secondPlatformCategoryId } = searchType.value
    console.log(platformCategoryId,secondPlatformCategoryId)
    $emit('onSearchParams', toRaw({
        ...searchType.value,
        platformCategoryId: (secondPlatformCategoryId === platformCategoryId) || platformCategoryId === '0' ? '' : platformCategoryId,
    }))
}
const handleReset = () => {
    searchType.value = {
        name: '',
        platformCategoryId: '',
        secondPlatformCategoryId: '',
        sellType: '',
    }
    search()
}
init()
</script>

<style lang="scss" scoped>
@include b(search) {
    background: #f9f9f9;
}
</style>
