<template>
    <div class="aside-wrap">
        <el-dropdown
            ref="dropMenu"
            :disabled="showConfig"
            :hide-on-click="false"
            placement="bottom-start"
            trigger="hover"
            style="width: 100%"
            @command="commandHandle"
        >
            <div class="admin__aside--shop" style="outline: none">
                <div class="lineFlex">
                    <img :src="shopInfo.logo" class="shop--logo" />
                    <div class="shop--name">
                        <span class="el-dropdown-link">
                            <span :title="shopInfo.name">{{ shopInfo.name }}</span>
                        </span>
                    </div>
                </div>
                <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="setting">
                        <div class="dorp-cell">
                            <span>供应商中心</span>
                        </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="logout">
                        <div class="dorp-cell noborder">
                            <span>退出登录</span>
                            <i class="el-icon-switch-button"></i>
                        </div>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <div class="side-nav-wrap">
            <div class="side-nav-wrap-main">
                <div class="admin__menu" @scroll="onScroll">
                    <Menu v-model="showConfig" />
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Menu from './Menu.vue'
import { useRouter } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { myData } from '@/apis'

const router = useRouter()
const shopInfoStore = useShopInfoStore()
const shopInfo = ref({})
const scrollVal = ref(null)
const isScrolling = ref(false)
const showConfig = ref(false)
const dropMenu = ref(null)
onMounted(() => {
    initShopInfo()
    shopInfoStore.$subscribe(() => initShopInfo())
})

const onScroll = (val: any) => {
    if (!isScrolling.value) {
        isScrolling.value = true
    }
    const timer = setTimeout(() => {
        /* 判断是否已经滚动结束，不能写在scrollEnd，有bug */
        if (val === scrollVal.value) {
            //实现上移
            isScrolling.value = false
        }
        clearTimeout(timer)
    }, 500)
    scrollVal.value = val
}

const commandHandle = async (command: string) => {
    // 退出登录
    if (command === 'logout') {
        shopInfoStore.DEL_SHOP_INFO()
        router.push('/sign').then((_) => {})
    }
    // 账号信息
    if (command === 'setting') {
        router.push('/business').then((_) => {})
    }
    if (command === 'index') {
        const appBaseUrl = process.env.VUE_APP_BASEURL
        if (!appBaseUrl) return
        const url = appBaseUrl.replace(/\/api/, '')
        open(`${url}`, '_top')
    }
}
const dropMenuHandle = (open: boolean) => {
    const target = dropMenu.value
    if (!target) return
    open ? target.handleOpen() : target.handleClose()
}
const initShopInfo = async () => {
    // 如果有值就不再获取
    const cacheShopInfo = shopInfoStore.getterShopInfo
    const empty = !cacheShopInfo
    shopInfo.value = empty ? {} : cacheShopInfo
    if (empty || !cacheShopInfo.token) return
    try {
        const { data } = await myData()
        if (data) {
            shopInfoStore.SET_SHOP_ADMIN_DATA({ ...data, id: data.shopId })
        }
    } catch (error) {
        console.log(error)
    }
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/layout/mmenu.scss';

.black {
    color: #676767 !important;
}
</style>
