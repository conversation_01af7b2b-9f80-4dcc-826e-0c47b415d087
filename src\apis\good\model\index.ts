/*
 * @description:
 * @Author: lexy
 * @Date: 2023-03-17 10:36:35
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-09 13:59:07
 */
import { StockType } from '@/apis/marketing/model'
import type { Limit } from '@/views/goods/types'
export enum RETRIEVESORT {
    salePrice_desc,
    salePrice_asc,
    salesVolume_desc,
    salesVolume_asc,
    createTime_desc,
    createTime_asc,
    comprehensive_desc,
    comprehensive_asc,
}
export enum ACTIVITY_TYPE {
    // 普通订单
    COMMON = 'COMMON',
    // 秒杀
    SPIKE = 'SPIKE',
    // 拼团
    TEAM = 'TEAM',
    // 砍价
    BARGAIN = 'BARGAIN',
    // 套餐
    PACKAGE = 'PACKAGE',
    // 会员专享
    MEMBER_ONLY = 'MEMBER_ONLY'
}
export interface RetrieveParam {
    supplierGoodsName: string
    categoryFirstId: string
    categorySecondId: string
    categoryThirdId: string
    platformCategoryFirstId: string
    platformCategorySecondId: string
    platformCategoryThirdId: string
    productId: string[] | string
    excludeProductIds: string[]
    pageNum: number
    size: number
    sort: keyof typeof RETRIEVESORT | string
    minPrice: number | string
    maxPrice: number | string
    current: number
    pages: number
    shopId: string
    activity?: {
        activityType?: ACTIVITY_TYPE
        startTime?: string
        endTime?: string
    }
}
/**
 * @LastEditors: lexy
 * @description: 商品检索返回类型
 */
export interface ApiRetrieveComItemType {
    createTime: string
    id: string
    initSalesVolume: number
    albumPics: string
    productId: string
    productName: string
    salePrices: string[]
    salesVolume: string
    shopId: string
    shopName: string
    specs: string
    skuIds: string[]
    status: 'SELL_ON' | 'SELL_OFF'
    stockTypes: keyof (typeof Limit)[]
    widePic: string
    stocks: string[]
    isCheck?: boolean
    collectionUrl?: string
    extra?: {
        auditTime: string
        customDeductionRatio: string
        platformCategory: {
            one: string
            three: string
            two: string
        }
        productAttributes: any[]
        productParameters: any[]
        submitTime: string
    }
    productType?: string
    salesNum?: string
    sellType?: string
    supplierName?: string
    supplierProductStatus?: string
    storageSkus?: StorageSkuType[]
}

export interface StorageSkuType {
    activityType: keyof typeof ACTIVITY_TYPE
    commission: string
    id: string
    image: string
    initSalesVolume: string
    limitNum: number
    limitType: 'UNLIMITED' | 'LIMITED'
    minimumPurchase: number
    price: string
    productId: string
    salePrice: string
    salesVolume: string
    shopId: string
    specs: string[]
    stock: string
    stockType: StockType
    weight: number
    purchasePrice: string
}

export interface ApiRetrieve {
    list: ApiRetrieveComItemType[]
    total: number
    pageNum: number
    pageSize: number
    pages: number
    size: number
}
