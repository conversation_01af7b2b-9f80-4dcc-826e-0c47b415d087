<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 16:27:29
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 13:56:06
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="isUpCard">
            <el-form :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="供应商名称">
                            <el-input v-model="searchForm.name" placeholder="请填写供应商名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="手机号">
                            <el-input v-model="searchForm.mobile" placeholder="请填写手机号" maxlength="11"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="ID">
                            <el-input v-model="searchForm.supplierSn" placeholder="请填写供应商ID" maxlength="30"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" round @click="searchHandle">搜索</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import MCard from '@/components/MCard.vue'
const searchForm = reactive({
    name: '',
    mobile: '',
    supplierSn: '',
})
const isUpCard = ref(false)
watch(
    () => isUpCard.value,
    (val) => {
        $emit('searchCardChange', val)
    },
)
const $emit = defineEmits(['onSearchParam', 'searchCardChange'])
const searchHandle = () => {
    $emit('onSearchParam', searchForm)
}
</script>
