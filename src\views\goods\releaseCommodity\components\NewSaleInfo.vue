<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-28 19:59:30
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-16 19:00:26
-->
<script lang="ts" setup>
import GoodNorms from './goodNorms/GoodNorms.vue'
import GoodNormTable from './goodNorms/GoodNormTable.vue'
import GoodOnly from './goodNorms/GoodOnly.vue'
import { NormListType, NormType } from './goodNorms/index'
import { ElMessage } from 'element-plus'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { doGetCommoditySku } from '@/apis/good'
import ReleaseTitle from './ReleaseTitle.vue'
import { SkuInterface } from '../types'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
import useSaleInfo from '../hooks/useSaleInfo'
/**
 * variable
 */
const $route = useRoute()
const { divTenThousand, divHundred, mulHundred } = useConvert()
const currentFormRef = ref()
const GoodOnlyRef = ref()
const memoSpecList = ref([])
const goodNormTable = ref()

const $emit = defineEmits(['changeInstance'])
const instance = getCurrentInstance()

const { submitForm, diffPrice, validatePass, commissionValidatePass, minimumPurchaseValidatePass, realPriceValidatePass } = useSaleInfo()

const formRules = reactive({
    'skus[0].price': [
        {
            required: true,
            validator: validatePass,
            trigger: ['blur', 'change'],
        },
    ],
    'skus[0].commission': [
        {
            required: true,
            validator: commissionValidatePass,
            trigger: ['blur', 'change'],
        },
    ],
    'skus[0].retailPrice': [
        {
            required: true,
            validator: realPriceValidatePass,
            trigger: ['blur', 'change'],
        },
    ],
    'skus[0].minimumPurchase': [
        {
            required: true,
            validator: minimumPurchaseValidatePass,
            trigger: ['blur', 'change'],
        },
    ],
})
// 重量单位
const weightTypeOptions = [
    {
        label: '千克',
        value: 'KG',
    },
    {
        label: '克',
        value: 'G',
    },
]
/**
 * lifeCircle
 */

onActivated(() => {
    dataDisplay()
    $emit('changeInstance', instance?.refs)
})
/**
 * function
 */
const changeClassHandle = async (e: { type: string; list: NormType[] }) => {
    const { type, list } = e
    // 删除多规格转换单规格时候为skus中添加一条单规格数据
    if (!list.length) {
        submitForm.value.skus = [
            {
                id: '',
                image: '',
                initSalesVolume: 0,
                limitNum: 0,
                initStock: 0,
                limitType: 'UNLIMITED',
                price: 0,
                productId: '',
                commission: 0,
                retailPrice: 0,
                salePrice: 0,
                shopId: '',
                stockType: 'LIMITED',
                weight: 0,
                weightType: 'KG',
                minimumPurchase: 1,
                specs: [],
            },
        ]
        return
    }
    submitForm.value.specGroups = list
    if (type !== 'addSpec') {
        submitForm.value.skus = caleDescartes(list)
    }
}
/**
 * @LastEditors: lexy
 * @description: 改变规格列表
 */
const changeNormList = (e: NormListType[]) => {
    submitForm.value.skus = e.map((item) => ({
        ...item,
        price: Number(item.price),
        commission: Number(item.commission),
        retailPrice: Number(item.retailPrice),
        salePrice: divHundred(diffPrice(Number(mulHundred(item.retailPrice)), Number(mulHundred(item.commission)))),
    }))
}
async function dataDisplay() {
    if ($route.query.id) {
        const { code: skuCode, data: skuData } = await doGetCommoditySku(useShopInfoStore().shopInfo.id, $route.query.id)
        if (skuCode !== 200) {
            ElMessage.error('获取商品sku失败')
            return
        }
        // 处理价格以厘作为单位
        skuData.skus.forEach((item: any) => {
            item.initStock = Number(item.initStock)
            item.price = Number(divTenThousand(item.price))
            item.commission = Number(divTenThousand(item.commission))
            item.salePrice = Number(divTenThousand(item.salePrice))
            item.retailPrice = `${divHundred(Number(mulHundred(item.salePrice)) + Number(mulHundred(item.commission)))}`
        })
        submitForm.value = Object.assign(submitForm.value, skuData)

        submitForm.value.multipleSpecs = submitForm.value?.skus?.length > 1 || !!submitForm.value.multipleSpecs
    }
}
/**
 * @LastEditors: lexy
 * @description: 规格组合
 * @param {NormType[]} list
 */
function caleDescartes(list: NormType[]): SkuInterface[] {
    let assemble = []
    assemble = list.map((item) => {
        return item.children
    })
    if (assemble.length <= 1) {
        assemble = assemble[0]
    } else {
        assemble = assemble.reduce((total, currentValue) => {
            let res: any[] = []
            total.forEach((t) => {
                currentValue.forEach((cv) => {
                    if (t instanceof Array) {
                        res.push([...t, cv])
                    } else {
                        res.push([t, cv])
                    }
                })
            })
            return res
        })
    }
    return assemble.map((item) => {
        return addSku(item)
    })
}
/**
 * @LastEditors: lexy
 * @description: 添加规格
 * @param {*} productSpecNames
 * @returns {*}
 */
function addSku(productSpecNames: any): SkuInterface {
    let tempArr: string[] = []
    if (Array.isArray(productSpecNames)) {
        tempArr = productSpecNames.map((item) => {
            return item.name
        })
    } else {
        tempArr.push(productSpecNames.name)
    }
    const havespecs = submitForm.value.skus.find((item) => {
        return item.specs.every((spec) => tempArr.includes(spec))
    })
    if (havespecs) {
        havespecs.specs = tempArr
    }
    return havespecs
        ? havespecs
        : {
              specs: tempArr,
              stockType: 'LIMITED',
              initSalesVolume: 0,
              limitType: 'UNLIMITED',
              limitNum: 0,
              image: '',
              price: 0,
              initStock: 0,
              commission: 0,
              retailPrice: 0,
              salePrice: 0,
              weight: 0,
              weightType: 'KG',
              minimumPurchase: 1,
          }
}
const handleChange = (e: any) => {
    submitForm.value.multipleSpecs = e
    submitForm.value.specGroups = []
    submitForm.value.skus = []
    if (!e && submitForm.value.skus?.length === 0) {
        submitForm.value.skus = [
            {
                id: '',
                image: '',
                initSalesVolume: 0,
                limitNum: 0,
                initStock: 0,
                limitType: 'UNLIMITED',
                price: 0,
                productId: '',
                commission: 0,
                retailPrice: 0,
                salePrice: 0,
                shopId: '',
                stockType: submitForm.value.productType === 'REAL_PRODUCT' ? 'LIMITED' : 'UNLIMITED',
                weight: 0,
                weightType: 'KG',
                minimumPurchase: 1,
                specs: [],
            },
        ]
    }
}
onMounted(() => {
    if (submitForm.value?.productType === 'REAL_PRODUCT') {
        submitForm.value.skus?.forEach((sku) => {
            sku.stockType = 'LIMITED'
        })
    }
    // handleChange(submitForm.value?.skus?.length > 1 ? 'MUTI_SPEC' : 'SINGLE_SPEC')

    submitForm.value.multipleSpecs = submitForm.value?.skus?.length > 1 || !!submitForm.value.multipleSpecs

    if (submitForm.value.specGroups && submitForm.value.specGroups?.length) {
        submitForm.value.specGroups = submitForm.value.specGroups?.map((item: any) => ({
            ...item,
            children: item.children?.map((child: any) => ({ ...child, name: child.name?.slice(0, 64) })),
        }))
    }
})
function caculateSalePrice() {
    submitForm.value.skus[0].salePrice = parseFloat(((Number(mulHundred(submitForm.value.skus[0].retailPrice)) - Number(mulHundred(submitForm.value.skus[0].commission))) / 100).toFixed(2))
}

defineExpose({ currentFormRef, GoodOnlyRef, goodNormTable })
</script>
<template>
    <el-form ref="currentFormRef" :model="submitForm" :rules="formRules" label-width="100px" :disabled="submitForm.status === 'UNDER_REVIEW'">
        <release-title title="规格类型" />
        <el-form-item label="规格类型" style="margin-top: 10px">
            <el-radio-group v-model="submitForm.multipleSpecs" size="small" :disabled="!(!$route.query.id || $route.query.isCopy)" @change="handleChange">
                <el-radio :value="false">单规格</el-radio>
                <el-radio :value="true">多规格</el-radio>
            </el-radio-group>
        </el-form-item>
        <template v-if="submitForm.multipleSpecs">
            <GoodNorms :class-arr="submitForm.specGroups" :status="submitForm.status" @change-class="changeClassHandle" />
            <GoodNormTable
                v-if="submitForm.specGroups && submitForm.specGroups.length > 0"
                ref="goodNormTable"
                :list="submitForm.skus"
                :class-arr="submitForm.specGroups"
                :memo-spec-list="memoSpecList"
                @change-norm-list="changeNormList"
            />
        </template>
        <template v-else-if="submitForm.skus?.length">
            <el-row :gutter="8">
                <el-col :span="12">
                    <el-form-item label="划线价" prop="skus[0].price">
                        <div class="inputnumber">
                            <decimal-input v-model="submitForm.skus[0].price" :decimal-places="2" :max="9999999999" :min="0" class="input_number com__input--width">
                                <template #append>元</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="佣金" prop="skus[0].commission">
                        <div class="inputnumber">
                            <decimal-input v-model="submitForm.skus[0].commission" :decimal-places="2" :max="9999999999" :min="0" class="input_number com__input--width" @change="caculateSalePrice">
                                <template #append>元</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="零售价" prop="skus[0].retailPrice">
                        <div class="inputnumber">
                            <decimal-input v-model="submitForm.skus[0].retailPrice" :decimal-places="2" :max="9999999999" :min="0" class="input_number com__input--width" @change="caculateSalePrice">
                                <template #append>元</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="供货价" prop="skus[0].salePrice">
                        <div class="inputnumber">
                            <decimal-input v-model="submitForm.skus[0].salePrice" :decimal-places="2" :max="9999999999" :min="0" :disabled="true" class="input_number com__input--width">
                                <template #append>元</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col v-if="!$route.query.id || $route.query.isCopy" :span="12">
                    <el-form-item label="库存" prop="skus[0].initStock">
                        <div class="inputnumber">
                            <decimal-input
                                v-model="submitForm.skus[0].initStock"
                                :decimal-places="0"
                                :max="999999"
                                :min="0"
                                :disabled="!(!$route.query.id || $route.query.isCopy) || submitForm.skus[0].stockType === 'UNLIMITED'"
                                class="input_number com__input--width"
                            >
                                <template #append>个</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="12">
                    <el-form-item label="初始销量">
                        <div class="inputnumber">
                            <decimal-input v-model="submitForm.skus[0].initSalesVolume" :decimal-places="0" :max="999999" :min="0" class="input_number com__input--width">
                                <template #append>个</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col> -->
                <el-col :span="12">
                    <el-form-item label="限购数量">
                        <div class="inputnumber">
                            <el-input-number
                                v-model="submitForm.skus[0].limitNum"
                                :disabled="submitForm.skus[0].limitType === 'UNLIMITED' || !(!$route.query.id || $route.query.isCopy)"
                                :precision="0"
                                :max="99"
                                :min="1"
                                class="input_number com__input--width"
                            />
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="重量">
                        <div class="inputnumber weight">
                            <decimal-input v-model="submitForm.skus[0].weight" :decimal-places="3" :max="9999999999" :min="0" class="input_number com__input--width-weight">
                                <!-- <template #append>
                                </template> -->
                            </decimal-input>
                            <el-select v-model="submitForm.skus[0].weightType" placeholder="单位" style="width: 74px">
                                <el-option v-for="item in weightTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="起批数" prop="skus[0].minimumPurchase">
                        <div class="inputnumber">
                            <decimal-input
                                v-model="submitForm.skus[0].minimumPurchase"
                                :decimal-places="0"
                                :max="999999"
                                :min="0"
                                class="com__input--width"
                                :disabled="submitForm.sellType === 'CONSIGNMENT'"
                            >
                                <template #append>件</template>
                            </decimal-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="限购类型">
                        <el-select v-model="submitForm.skus[0].limitType" class="com__input--width" :disabled="!(!$route.query.id || $route.query.isCopy)">
                            <el-option label="不限购" value="UNLIMITED"></el-option>
                            <el-option label="商品限购" value="PRODUCT_LIMITED"></el-option>
                            <el-option v-if="submitForm.specGroups.length" label="规格限购" value="SKU_LIMITED"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="库存类型">
                        <el-select v-model="submitForm.skus[0].stockType" class="com__input--width" :disabled="!(!$route.query.id || $route.query.isCopy) || submitForm.productType === 'REAL_PRODUCT'">
                            <el-option label="有限库存" value="LIMITED"></el-option>
                            <el-option label="无限库存" value="UNLIMITED"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </template>
        <GoodOnly ref="GoodOnlyRef" />
    </el-form>
</template>
<style lang="scss">
@import '@/assets/css/goods/goodMultiSpec.scss';

@include b(inputnumber) {
    position: relative;
    width: 100%;

    @include e(icon) {
        width: 34px;
        height: 30px;
        position: absolute;
        right: 5px;
        top: 1px;
        background: #e6e8eb;
        color: #909399;
        text-align: center;
        /* border: 1px solid #e6e8eb; */
    }
}

.input_number {
    width: 90%;

    .el-input__inner {
        text-align: left;
    }
}

.weight {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.com__input--width-weight {
    width: 70%;
    margin-right: 4px;
}
</style>
