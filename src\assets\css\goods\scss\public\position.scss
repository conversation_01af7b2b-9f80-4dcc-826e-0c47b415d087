/****** position ******/

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.z--1 {
    z-index: -1;
}
.z-1 {
    z-index: 1;
}
.z-100 {
    z-index: 100;
}
.z-max {
    z-index: 999999999;
}
.b-0{
    bottom: 0;
}
.r-0{
    right: 0;
}
.r-all{
    right: 100%;
}
.fixed-foot {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 100;
}

.fixed-top {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
}
/* mask */
.mask-all {
    position: fixed;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
}
.mask-img-bottom{
    position: absolute;
    bottom: 0px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #ffffff;
    text-align: center;
    width: 100%;
}