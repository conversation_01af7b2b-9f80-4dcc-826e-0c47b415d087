<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-09-15 16:18:41
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-31 14:20:45
-->
<script lang="ts" setup>
import { ref, watch, PropType, computed } from 'vue'
import AreaChoose from '@/components/q-area-choose/area-choose.vue'
import { ElMessage } from 'element-plus'
import { useVModel } from '@vueuse/core'
import uuid from '@/utils/uuid'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
interface ChooseAreaItem {
    upperCode: string
    upperName: string
    length: number
    lowerName: string[]
    lowerCode: string[]
}
/*
 *variable
 */
const $props = defineProps({
    tableData: {
        type: Array as PropType<any[]>,
        // eslint-disable-next-line vue/require-valid-default-prop
        default: [
            {
                id: uuid(10),
                amountNum: 0, //数量
                logisticsId: 0, // 物流编号
                pieceNum: 0, //件数
                postType: 'PKGS',
                region: [], //地区
                weight: 0, //重量
            },
        ],
    },
    isPKGS: { type: Boolean, required: true },
    isEdit: { type: Boolean, required: true },
})
const emit = defineEmits(['update:tableData'])
const _tableData = useVModel($props, 'tableData', emit)
const currentChooseIndex = ref(0)
const freeDialogTag = ref(false)
/**
 * @LastEditors: lexy
 * @description: 包邮条件
 * @returns {*}
 */
const postType = [
    {
        value: 'PKGS',
        label: '件数',
    },
    {
        value: 'MONEY',
        label: '金额',
    },
    {
        value: 'PKGS_MONEY',
        label: '件数+金额',
    },
]
const postType_ = [
    {
        value: 'WEIGHT',
        label: '重量',
    },
    {
        value: 'MONEY',
        label: '金额',
    },
    {
        value: 'WEIGHT_MONEY',
        label: '重量+金额',
    },
]
/*
 *lifeCircle
 */
watch(
    () => $props.isPKGS,
    (val, oldval) => {
        if (!$props.isEdit) {
            if (val !== oldval && val) {
                _tableData.value.forEach((item) => {
                    item.postType = 'PKGS'
                })
            } else {
                _tableData.value.forEach((item) => {
                    item.postType = 'WEIGHT'
                })
            }
        }
    },
    {
        immediate: true,
    },
)
/*
 *function
 */
const handleAddRegion = (row: any, index: number) => {
    freeDialogTag.value = true
    currentChooseIndex.value = index
}

const handleShowDialog = (idx: number) => {
    freeDialogTag.value = true
    currentChooseIndex.value = idx
}
// 当前选中区域数组
const freeCurArea = computed<ChooseAreaItem[]>(() => {
    const index = currentChooseIndex.value
    const tableList = _tableData.value
    return tableList[index] ? tableList[index].region : []
})
// 所有选中区域
const freeAllCurArea = computed<ChooseAreaItem[]>(() => {
    const tableList = _tableData.value
    return tableList.map((item) => item.region).flat(1)
})
/**
 * @LastEditors: lexy
 * @description: 获取选中类名
 */
const getAreaName = (data: ChooseAreaItem[]) => {
    return data
        .map((item) => {
            if (item.lowerCode.length === item.length) {
                return item.upperName
            }
            return `${item.upperName}(${item.lowerCode.length}/${item.length})`
        })
        .join(',')
}
/**
 * @LastEditors: lexy
 * @description: 区域选择回调
 */
const handleChangeArea = (e: ChooseAreaItem[]) => {
    _tableData.value[currentChooseIndex.value].region = e
}
/**
 * @LastEditors: lexy
 * @description: 删除
 * @param {*} index
 * @param {*} row
 * @returns {*}
 */
const HandleSecondTabItem = (index: number, row: any) => {
    if (index === -1) {
        const item = {
            id: uuid(10),
            amountNum: 0, //数量
            logisticsId: 0, // 物流编号
            pieceNum: 0, //件数
            postType: $props.isPKGS ? 'PKGS' : 'WEIGHT',
            region: new Map(), //地区
            weight: 0, //重量
        }
        _tableData.value.push(item)
    } else {
        if (index === 0) return ElMessage.error('至少保留一个配送区域')
        _tableData.value = _tableData.value.filter((item) => item.id !== row.id)
    }
}

function weightMoneyAndParcelMoney(type: any) {
    return ['PKGS_MONEY', 'WEIGHT_MONEY'].includes(type)
}
function weightAndMoney(type: any) {
    return ['PKGS', 'WEIGHT'].includes(type)
}
</script>

<template>
    <el-table :cell-style="{ padding: '30px 0' }" :data="_tableData" :header-cell-style="{ fontSize: '14px', color: '#333' }">
        <el-table-column label="选择区域" min-width="90%" prop="name">
            <template #default="{ row, $index }">
                <div v-if="row.region.length" @click="handleShowDialog($index)">{{ getAreaName(row.region) }}</div>
                <el-button link type="primary" @click="handleAddRegion(row, $index)">
                    {{ row.region.length ? '编辑' : '添加区域' }}
                </el-button>
            </template>
        </el-table-column>
        <el-table-column :label="$props.isPKGS ? '首件数（件）' : '首重（kg）'" min-width="350%" prop="name">
            <template #default="{ row }">
                <el-row :gutter="20" align="middle">
                    <el-col :span="6">
                        <el-select v-if="$props.isPKGS" v-model="row.postType" placeholder="Select" size="small">
                            <el-option v-for="item in postType" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                        <el-select v-else v-model="row.postType" placeholder="Select" size="small">
                            <el-option v-for="item in postType_" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-col>
                    <el-col :span="18">
                        <el-row v-if="weightAndMoney(row.postType)" align="middle">
                            <decimal-input v-if="$props.isPKGS" v-model="row.pieceNum" :decimal-places="0" :min="0" :input-style="{ textAlign: 'center' }" class="number-input">
                                <template #prefix> 在&nbsp; </template>
                                <template #suffix> 件以上包邮 </template>
                            </decimal-input>
                            <decimal-input v-else v-model="row.weight" :decimal-places="3" :min="0" :input-style="{ textAlign: 'center' }" class="number-input">
                                <template #prefix> 在&nbsp; </template>
                                <template #suffix> kg以上包邮 </template>
                            </decimal-input>
                        </el-row>
                        <el-row v-else-if="row.postType === 'MONEY'" align="middle">
                            <decimal-input v-model="row.amountNum" :input-style="{ textAlign: 'center' }" :decimal-places="2" :min="0" class="number-input">
                                <template #prefix> 满 </template>
                                <template #suffix> &nbsp;元以上包邮 </template>
                            </decimal-input>
                        </el-row>
                        <el-row v-if="weightMoneyAndParcelMoney(row.postType)" align="middle">
                            <el-col :span="11">
                                <decimal-input v-if="$props.isPKGS" v-model="row.pieceNum" :input-style="{ textAlign: 'center' }" :decimal-places="0" :min="0" class="number-input">
                                    <template #prefix> 在 </template>
                                    <template #suffix> &nbsp;件以上 </template>
                                </decimal-input>
                                <decimal-input v-else v-model="row.weight" :input-style="{ textAlign: 'center' }" :decimal-places="3" :min="0" class="number-input">
                                    <template #prefix> 在 </template>
                                    <template #suffix> &nbsp;kg以上 </template>
                                </decimal-input>
                            </el-col>
                            <span> , </span>
                            <el-col :span="11">
                                <decimal-input v-model="row.amountNum" :input-style="{ textAlign: 'center' }" :decimal-places="2" :min="0" class="number-input">
                                    <template #prefix> 满&nbsp; </template>
                                    <template #suffix> &nbsp;元以上包邮 </template>
                                </decimal-input>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="90" prop="name" align="center">
            <template #default="{ row, $index }">
                <div class="dialogTab_container__right_btn">
                    <el-button v-if="_tableData.length - 1 === $index" link type="primary" @click="HandleSecondTabItem(-1, row)">添加</el-button>
                    <el-button :disabled="_tableData.length === 1" link type="danger" @click="HandleSecondTabItem(_tableData.length - 1, row)">删除</el-button>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <AreaChoose v-model:show="freeDialogTag" :all-choose-arr="freeAllCurArea" :current-choose="freeCurArea" @change="handleChangeArea" />
</template>

<style lang="scss" scoped>
@include b(number-input) {
    width: 100%;
}
@include b(dialogTab_container) {
    @include e(input) {
        text-align: center;
    }
    @include e(right_btn) {
        display: flex;
        justify-content: space-evenly;
    }
}
</style>
