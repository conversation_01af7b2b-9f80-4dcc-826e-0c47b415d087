<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-09-15 11:22:00
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-24 13:41:12
-->
<script lang="ts" setup>
import { shallowRef, onBeforeUnmount, defineEmits } from 'vue'
import { useVModel } from '@vueuse/core'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import { uploadFile } from './upload'
import { type IEditorConfig, type SlateElement } from '@wangeditor/editor'
type VideoElement = SlateElement & {
    src: string
    poster?: string
}
type InsertFnType = (url: string, alt: string, href: string) => void
type ImageElement = SlateElement & {
    src: string
    alt: string
    url: string
    href: string
}
interface MediaItem {
    file: File
    type: 'image' | 'video'
    url?: string
}
/*
 *variable
 */
const $props = defineProps({
    content: {
        type: String,
        default: '',
    },
    height: {
        type: String,
        default: '200px',
    },
})
const emit = defineEmits(['update:content'])
const _content = useVModel($props, 'content', emit)

/*
 *variable
 */

const editorRef = shallowRef()
const mediaQueue = ref<MediaItem[]>([])
const isUploading = ref(false)
let currentBatchSize = 0

const mode = 'simple'
const baseUrl = import.meta.env.VITE_BASE_URL
const editorConfig: Partial<IEditorConfig> = {
    MENU_CONF: {
        editImage: {
            onUpdatedImage(imageNode: ImageElement | null) {
                // TS 语法
                // onUpdatedImage(imageNode) {                    // JS 语法
                if (imageNode === null) return

                const { src, alt, url } = imageNode
            },
            checkImage: customCheckImageFn, // 也支持 async 函数
            parseImageSrc: customParseImageSrc, // 也支持 async 函数
        },
        uploadImage: {
            server: `${baseUrl}gruul-mall-carrier-pigeon/oss/upload`,
            // form-data fieldName ，默认值 'wangeditor-uploaded-image'
            fieldName: 'file',
            // 单个文件的最大体积限制，默认为 2M
            maxFileSize: 1 * 1024 * 1024, // 1M
            // 最多可上传几个文件，默认为 100
            maxNumberOfFiles: 10,
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: [],
            // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
            meta: {},
            // 将 meta 拼接到 url 参数中，默认 false
            metaWithUrl: false,
            // 自定义增加 http  header
            headers: {},
            // 跨域是否传递 cookie ，默认为 false
            withCredentials: true,
            // 超时时间，默认为 10 秒
            timeout: 5 * 1000, // 5 秒
            // 自定义上传
            async customUpload(file: File, insertFn: InsertFnType) {
                addMediaToQueue([file], 'image')
            },
        },
        insertImage: {
            onInsertedImage(imageNode: ImageElement | null) {
                if (imageNode === null) return
                const { src, alt, url, href } = imageNode
            },
            checkImage: customCheckImageFn, // 也支持 async 函数
            parseImageSrc: customParseImageSrc, // 也支持 async 函数
        },
        insertVideo: {
            onInsertedVideo(videoNode: VideoElement | null) {
                if (videoNode === null) return
                const { src } = videoNode
                console.log('inserted video', src)
            },
            checkVideo: customCheckVideoFn, // 也支持 async 函数
            parseVideoSrc: customParseVideoSrc, // 也支持 async 函数
        },
        uploadVideo: {
            // 自定义上传
            async customUpload(file: File, insertFn: InsertFnType) {
                addMediaToQueue([file], 'video')
            },
            // 自定义上传
            // file 即选中的文件
            // 自己实现上传，并得到视频 url poster
            // 最后插入视频
        },
    },
}
const toolbarConfig = {
    toolbarKeys: [
        // 默认工具栏配置
        'headerSelect',
        'blockquote',
        '|',
        'bold',
        'underline',
        'italic',
        'through',
        'color',
        'bgColor',
        'clearStyle',
        '|',
        'bulletedList',
        'numberedList',
        'todo',
        {
            key: 'group-justify',
            title: '对齐',
            iconSvg: '<svg>...</svg>',
            menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify'],
        },
        '|',
        'insertLink',
        'uploadImage', // 上传本地图片
        'insertImage', // 插入网络图片
        'insertVideo',
        'insertTable',
        'codeBlock',
        '|',
        'undo',
        'redo',
        'fullScreen',
    ],
}
/*
 *lifeCircle
 */

// 组件销毁时，也及时销毁编辑器，重要！
onBeforeUnmount(() => {
    if (editorRef.value) {
        editorRef.value.destroy()
    }
    mediaQueue.value = []
    isUploading.value = false
    currentBatchSize = 0
})
/*
 *function
 */
function addMediaToQueue(files: File[], type: 'image' | 'video') {
    const newItems = files.map((file) => ({ file, type }))
    mediaQueue.value.push(...newItems)
    if (!isUploading.value) {
        currentBatchSize = mediaQueue.value.length
        uploadNextMedia()
    }
}

async function uploadNextMedia() {
    if (mediaQueue.value.length === 0) {
        isUploading.value = false
        currentBatchSize = 0
        return
    }

    isUploading.value = true
    const item = mediaQueue.value[0]

    try {
        const url = await uploadFile('gruul-mall-carrier-pigeon/oss/upload', item.file)
        item.url = url
        await insertMediaToEditor(mediaQueue.value.length === 1)
    } catch (error) {
        console.error('Upload failed:', error)
    } finally {
        mediaQueue.value.shift()
        uploadNextMedia()
    }
}

function insertMediaToEditor(isLastInBatch: boolean) {
    if (!editorRef.value) return

    const editor = editorRef.value
    const item = mediaQueue.value[0]

    if (!item || !item.url) return

    const insertIndex = editor.children.length

    editor.insertNode(
        {
            type: item.type,
            src: item.url,
            alt: '',
            children: [{ text: '' }],
        },
        { at: [insertIndex] },
    )

    // 只在批次的最后一个元素后插入空段落
    if (isLastInBatch) {
        editor.insertNode({ type: 'paragraph', children: [{ text: '' }] }, { at: [insertIndex + 1] })
        moveSelectionToEnd(editor)
    }
}

function moveSelectionToEnd(editor) {
    const lastIndex = editor.children.length - 1
    if (lastIndex >= 0) {
        editor.select({ path: [lastIndex, 0], offset: 0 })
    }
}

// 自定义校验图片
function customCheckImageFn(src: string, alt: string, url: string): boolean | undefined | string {
    // TS 语法
    // function customCheckImageFn(src, alt, url) {                                                    // JS 语法
    if (!src) {
        return
    }
    if (src.indexOf('http') !== 0) {
        return '图片网址必须以 http/https 开头'
    }
    return true

    // 返回值有三种选择：
    // 1. 返回 true ，说明检查通过，编辑器将正常插入图片
    // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）
    // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入。但不会提示任何信息
}

// 转换图片链接
function customParseImageSrc(src: string): string {
    // TS 语法
    // function customParseImageSrc(src) {               // JS 语法
    if (src.indexOf('http') !== 0) {
        return `http://${src}`
    }
    return src
}
// 自定义校验视频
function customCheckVideoFn(src: string, poster: string): boolean | string | undefined {
    // TS 语法
    // function customCheckVideoFn(src, poster) {                                             // JS 语法
    if (!src) {
        return
    }
    if (src.indexOf('http') !== 0) {
        return '视频地址必须以 http/https 开头'
    }
    return true
    // 返回值有三种选择：
    // 1. 返回 true ，说明检查通过，编辑器将正常插入视频
    // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）
    // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入。但不会提示任何信息
}

// 自定义转换视频
function customParseVideoSrc(src: string): string {
    // 转换 bilibili url
    // TS 语法
    // if (src.includes('.bilibili.com')) {
    //     const arr = location.pathname.split('/')
    //     const vid = arr[arr.length - 1]
    //     return `<iframe src="//player.bilibili.com/player.html?bvid=${vid}" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"> </iframe>`
    // }
    return src
}
/**
 *  @LastEditors: lexy
 * @description: 编辑器回调函数
 */
const handleCreated = (editor: any) => {
    editorRef.value = editor
}
</script>

<template>
    <Toolbar :default-config="toolbarConfig" :editor="editorRef" :mode="mode" style="border-bottom: 1px solid #ccc" />
    <Editor v-model="_content" :default-config="editorConfig" :mode="mode" :style="{ height: $props.height }" @on-created="handleCreated" />
</template>

<style lang="scss" scoped></style>
