<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-04-14 20:17:45
-->
<template>
    <msw-tools v-if="isDev" base="/" />

    <router-view :key="globalStore.applicationKey" />
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { StompStarter } from '@/composables/stomp/StompStarter'
import useGlobalStore from './store/modules/global'
const isDev = ref(process.env.NODE_ENV === 'development')
onMounted(() => StompStarter.start())
const globalStore = useGlobalStore()
</script>

<style lang="scss">
@import '@/assets/css/mixins/mixins.scss';
.main {
    height: 100%;
}
#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: $rows-text-color;
    background-color: $rows-bg-color-grey;
}
</style>
