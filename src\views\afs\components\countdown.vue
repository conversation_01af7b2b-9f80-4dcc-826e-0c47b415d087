<!--
 * @description: 倒计时 将字符串型变量转换成整型int
 * @Author: lexy
 * @Date: 2022-08-19 10:45:04
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-09 14:24:20
-->
<script setup lang="ts">
import { ref, onBeforeUnmount, watch } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    ms: {
        type: Boolean,
        default: true,
    },
    miao: { type: Boolean, default: true },
    timeout: { type: [String, Number], default: 0 },
    unmounted: { type: Boolean, default: false },
})
/*
 *variable
 */
const time = ref('')
const end = ref(false)
let _T
/*
 *lifeCircle
 */
watch(
    () => $props.unmounted,
    (val) => {
        if (!val) {
            _T && clearTimeout(_T)
        }
    },
)
watch(
    () => $props.timeout,
    (val) => {
        clock(Number(val) - new Date().getTime())
    },
    { immediate: true },
)
/*
 *function
 */
// miao()
function clock(times) {
    $props.ms ? ms(times) : miao(times)
    //页面加载时设置需要倒计时的秒数，计算小时
}

/**
 * @LastEditors: lexy
 * @description: 毫秒
 * @param {*} times
 * @returns {*}
 */
function ms(times: number) {
    time.value = doGetTime(times)
    //计算秒
    if (times > 0) {
        times = times - 1000
        _T = setTimeout(function () {
            clearTimeout(_T)
            clock(times)
        }, 1000)
        return
    }
    end.value = true
}

function doGetTime(millisecond: number) {
    var days = Math.floor(millisecond / (1000 * 60 * 60 * 24))
    var hours = Math.floor((millisecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    var minutes = Math.floor((millisecond % (1000 * 60 * 60)) / (1000 * 60))
    var seconds = Math.floor((millisecond % (1000 * 60)) / 1000)
    return days + '天' + hours + '时' + minutes + '分' + seconds + '秒'
}
function miao(params: string) {}
</script>

<template>
    <time v-if="!end" class="time">剩余{{ time }}</time>
</template>

<style scoped lang="scss">
@include b(time) {
    font-size: 14px;
    font-weight: normal;
    color: #ff7417;
}
</style>
