<template>
    <div class="price-warning-container">
        <!-- 搜索表单 -->
        <search-form @search="handleSearch" @show-change="handleSearchShow" />

        <!-- 状态切换标签 -->
        <div class="status-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                <el-tab-pane :label="`全部未处理(${statusCounts.ALL})`" name="ALL" />
                <el-tab-pane :label="`发货超时(${statusCounts.PENDING_SHIPMENT})`" name="PENDING_SHIPMENT" />
                <el-tab-pane :label="`揽收异常(${statusCounts.PENDING_PICKUP})`" name="PENDING_PICKUP" />
                <el-tab-pane :label="`已处理(${statusCounts.PROCESSED})`" name="PROCESSED" />
            </el-tabs>
        </div>

        <!-- 表格 -->
        <div class="table-container">
            <el-table
                :data="tableData"
                class="price-warning-table"
                :class="{ 'table-up': !searchFormVisible }"
                style="margin-top: 13px; width: 100%"
                v-loading="loading"
                @selection-change="handleSelectionChange"
            >
                <!-- 选择列 -->
                <el-table-column type="selection" width="55" align="center" />

                <el-table-column label="订单号" width="150" align="center">
                    <template #default="{ row }">
                        <div class="order-no">{{ row.orderNo }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="成交时间" width="150" align="center">
                    <template #default="{ row }">
                        <div class="order-time">{{ row.orderTime || '-' }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="商品" width="200" align="center">
                    <template #default="{ row }">
                        <div class="product-name">{{ row.productName || '-' }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="发货时间" width="150" align="center">
                    <template #default="{ row }">
                        <div class="delivery-time">{{ row.deliveryTime || '-' }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="售后状态" width="150" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getAfsStatusType(row.afsStatus)">
                            {{ getAfsStatusText(row.afsStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="预警类型" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getNoticeTypeType(row.noticeType)">
                            {{ getNoticeTypeText(row.noticeType) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="预警时间" width="150" align="center">
                    <template #default="{ row }">
                        <div class="notice-time">{{ row.noticeTime }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="处理时间" width="150" align="center">
                    <template #default="{ row }">
                        <div class="deal-time">{{ row.dealTime }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="处理人" width="120" align="center">
                    <template #default="{ row }">
                        <div class="deal-name">{{ row.dealName || '-' }}</div>
                    </template>
                </el-table-column>

                <!-- 固定操作列 -->
                <el-table-column label="操作" width="220" fixed="right" align="center" class="action-column">
                    <template #default="{ row }">
                        <div class="action-buttons">
                            <el-button v-if="row.dealStatus === 'AWAITING_PROCESSING'" type="primary" link size="small" @click="handleProcess(row)"> 已处理 </el-button>
                            <el-button v-if="!row.deliveryTime" type="danger" link size="small" @click="handleUrgeShipment(row)"> 立即发货 </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <better-page-manage
                :page-num="pageConfig.current"
                :page-size="pageConfig.size"
                :total="pageConfig.total"
                @handle-current-change="handleCurrentChange"
                @handle-size-change="handleSizeChange"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import BetterPageManage from '@/components/PageManage.vue'
import SearchForm from './components/search-form.vue'
import { ElMessage, ElTag, ElButton, ElTabs, ElTabPane, ElTable, ElTableColumn } from 'element-plus'
import type { Records, SearchParams, StatusTypeMap, StatusTextMap } from '@/apis/logistics/types'
import { getLogisticsNoticeList, shipmentBatch } from '@/apis/logistics'
import { postAddContact } from '@/apis/order/purchase'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { useRouter } from 'vue-router'

// 响应式数据
const tableData = ref<Records[]>([])
const selectedItems = ref<Records[]>([])
const searchFormVisible = ref(false)
const loading = ref(false)

// 路由和店铺信息
const router = useRouter()
const shopInfoStore = useShopInfoStore()

// 分页配置
const pageConfig = reactive({
    current: 1,
    size: 20,
    total: 0,
})

// 搜索参数
const searchParams = ref<SearchParams>({})

// 状态标签配置
const activeTab = ref('ALL')
const statusCounts = ref({
    ALL: 30,
    PENDING_SHIPMENT: 10,
    PENDING_PICKUP: 20,
    PROCESSED: 0,
})

// 状态映射函数

const getAfsStatusType = (status: string) => {
    const statusMap: StatusTypeMap = {
        NONE: 'success',
        REFUND_REQUEST: 'warning',
        REFUND_AGREE: 'info',
        SYSTEM_REFUND_AGREE: 'info',
        REFUND_REJECT: 'danger',
        REFUNDED: 'success',
        RETURN_REFUND_REQUEST: 'warning',
        RETURN_REFUND_AGREE: 'info',
        SYSTEM_RETURN_REFUND_AGREE: 'info',
        RETURN_REFUND_REJECT: 'danger',
        RETURNED_REFUND: 'warning',
        RETURNED_REFUND_CONFIRM: 'info',
        SYSTEM_RETURNED_REFUND_CONFIRM: 'info',
        RETURNED_REFUND_REJECT: 'danger',
        RETURNED_REFUNDED: 'success',
        BUYER_CLOSED: 'info',
        SYSTEM_CLOSED: 'info',
    }
    return statusMap[status] || 'info'
}

const getAfsStatusText = (status: string) => {
    const statusMap: StatusTextMap = {
        NONE: '不处于售后状态',
        REFUND_REQUEST: '退款申请',
        REFUND_AGREE: '退款已同意',
        SYSTEM_REFUND_AGREE: '系统自动同意退款申请',
        REFUND_REJECT: '退款已拒绝',
        REFUNDED: '已退款',
        RETURN_REFUND_REQUEST: '退货退款申请',
        RETURN_REFUND_AGREE: '退货退款已同意',
        SYSTEM_RETURN_REFUND_AGREE: '系统自动同意退货退款申请',
        RETURN_REFUND_REJECT: '退货退款已拒绝',
        RETURNED_REFUND: '退货退款 买家已发货',
        RETURNED_REFUND_CONFIRM: '退货退款 卖家已确认收货',
        SYSTEM_RETURNED_REFUND_CONFIRM: '退货退款 系统自动确认收货',
        RETURNED_REFUND_REJECT: '退货退款 卖家拒绝收货退回 售后关闭',
        RETURNED_REFUNDED: '已退货退款 已完成',
        BUYER_CLOSED: '买家撤销申请',
        SYSTEM_CLOSED: '系统自动关闭',
    }
    return statusMap[status] || status
}

const getNoticeTypeType = (type: string) => {
    const typeMap: StatusTypeMap = {
        PENDING_SHIPMENT: 'warning',
        PENDING_PICKUP: 'info',
    }
    return typeMap[type] || 'info'
}

const getNoticeTypeText = (type: string) => {
    const typeMap: StatusTextMap = {
        PENDING_SHIPMENT: '待发货',
        PENDING_PICKUP: '待揽件',
    }
    return typeMap[type] || type
}

// 事件处理函数
const handleSearch = (params: SearchParams) => {
    console.log('搜索参数:', params)
    searchParams.value = { ...params }
    pageConfig.current = 1
    fetchData()
}

const handleSearchShow = (visible: boolean) => {
    searchFormVisible.value = visible
}

// 处理标签切换
const handleTabChange = (tabKey: string) => {
    activeTab.value = tabKey
    pageConfig.current = 1
    fetchData()
}

// 根据标签获取过滤条件
const getTabFilters = (tabKey: string) => {
    switch (tabKey) {
        case 'ALL':
            // 全部未处理：显示所有未处理的数据
            return { dealStatus: 'AWAITING_PROCESSING' }
        case 'PENDING_SHIPMENT':
            // 发货超时：预警类型为发货超时
            return {
                noticeType: 'PENDING_SHIPMENT',
                dealStatus: 'AWAITING_PROCESSING',
            }
        case 'PENDING_PICKUP':
            // 揽收异常：预警类型为揽收异常
            return {
                noticeType: 'PENDING_PICKUP',
                dealStatus: 'AWAITING_PROCESSING',
            }
        case 'PROCESSED':
            // 已处理：显示已处理的数据
            return { dealStatus: 'PROCESSED' }
        default:
            return {}
    }
}

// 更新标签计数
const updateTabCounts = (data: any) => {
    // 这里可以根据实际API返回的统计数据来更新计数
    // 暂时使用模拟数据，实际项目中应该从API获取真实的统计数据
    statusCounts.value = {
        ALL: data.total || 30,
        PENDING_SHIPMENT: data.pendingShipmentCount || 10,
        PENDING_PICKUP: data.pendingPickupCount || 20,
        PROCESSED: data.processedCount || 0,
    }
}

const handleCurrentChange = (page: number) => {
    pageConfig.current = page
    fetchData()
}

const handleSizeChange = (size: number) => {
    pageConfig.size = size
    pageConfig.current = 1
    fetchData()
}

// 处理表格选择变化
const handleSelectionChange = (selection: Records[]) => {
    selectedItems.value = selection
}

const handleProcess = (row: Records) => {
    // 已处理：点击后该警信息变为已处理，并记录处理时间和处理人
    console.log('已处理:', row)
    ElMessage.success('标记为已处理')
}

const handleUrgeShipment = async (row: Records) => {
    try {
        // 获取当前用户信息
        const currentUser = shopInfoStore.getterShopInfo
        if (!currentUser.userId) {
            ElMessage.error('获取用户信息失败')
            return
        }

        // 调用催发货API，只携带列表ID
        const response = await shipmentBatch(row.id)
        if (response.code === 200) {
            ElMessage.success('催发货通知发送成功')

            // 如果有supplierId，尝试添加联系人并跳转到聊天界面
            if (row.supplierId) {
                try {
                    // 添加聊天联系人 (发送方ID, 接收方ID)
                    const contactResponse = await postAddContact(currentUser.userId, row.supplierId.toString())
                    if (contactResponse.code === 200) {
                        ElMessage.success('正在跳转到聊天界面...')
                        // 跳转到店铺消息页面，并传递shopId参数
                        router.push({
                            path: '/mall/customer/service',
                            query: { id: row.supplierId.toString() },
                        })
                    } else {
                        console.warn('添加联系人失败:', contactResponse.msg)
                        // 即使添加联系人失败，也尝试跳转到聊天界面
                        router.push({
                            path: '/mall/customer/service',
                            query: { id: row.supplierId.toString() },
                        })
                    }
                } catch (contactError) {
                    console.error('添加联系人异常:', contactError)
                    // 即使出现异常，也尝试跳转到聊天界面
                    router.push({
                        path: '/mall/customer/service',
                        query: { id: row.supplierId.toString() },
                    })
                }
            } else {
                ElMessage.warning('无法获取供应商信息，无法跳转到聊天界面')
            }
        } else {
            ElMessage.error(response.msg || '催发货通知发送失败')
        }
    } catch (error) {
        console.error('催发货失败:', error)
        ElMessage.error('催发货操作失败')
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 根据当前标签添加过滤条件
        const tabFilters = getTabFilters(activeTab.value)

        const requestParams = {
            ...searchParams.value,
            ...tabFilters,
            current: pageConfig.current,
            size: pageConfig.size,
        }
        console.log('API请求参数:', requestParams)

        // 调用 API 获取数据
        const response = await getLogisticsNoticeList(requestParams)

        if (response.code === 200 || response.code === 0) {
            tableData.value = response.data.records
            pageConfig.total = response.data.total

            // 更新标签计数（这里可以根据实际API返回的统计数据来更新）
            updateTabCounts(response.data)
        } else {
            ElMessage.error(response.msg || '获取数据失败')
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style lang="scss" scoped>
.price-warning-container {
    padding: 20px;
    background: #f5f5f5;
    min-height: calc(100vh - 60px);
}

// 状态标签样式
.status-tabs {
    margin: 16px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    padding: 0 20px;

    :deep(.el-tabs__header) {
        margin: 0;
        border-bottom: 1px solid #e4e7ed;
    }

    :deep(.el-tabs__nav-wrap) {
        &::after {
            display: none;
        }
    }

    :deep(.el-tabs__nav) {
        display: flex;
    }

    :deep(.el-tabs__item) {
        padding: 16px 20px;
        font-size: 14px;
        font-weight: 500;
        color: #606266;

        &:hover {
            color: #409eff;
        }

        &.is-active {
            color: #409eff;
            font-weight: 600;
        }
    }

    :deep(.el-tabs__active-bar) {
        background-color: #409eff;
        height: 3px;
    }
}

.table-container {
    width: 100%;
    overflow-x: hidden;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    padding-right: 1px; /* 为右边框留出空间 */
}

.price-warning-table {
    min-width: 950px;
    width: 100%;
    background: #fff;

    &.table-up {
        margin-top: 0 !important;
    }

    // Element Plus 表格样式覆盖
    :deep(.el-table__header) {
        background: #fff;

        th {
            background: #fff !important;
            font-weight: 600;
            color: #262626;
            font-size: 14px;
            text-align: center;
            border-right: 1px solid #f0f0f0;
            padding: 12px 8px;
        }
    }

    :deep(.el-table__body) {
        tr {
            &:hover {
                background: #fafafa;
            }

            td {
                text-align: center;
                border-right: 1px solid #f0f0f0;
                vertical-align: middle;
                padding: 8px;
            }
        }
    }

    // 固定列样式
    :deep(.el-table__fixed-right) {
        background: #fff !important;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);

        .el-table__fixed-header-wrapper,
        .el-table__fixed-body-wrapper {
            background: #fff !important;
        }
    }
}

// 单元格内容样式
.order-no,
.order-time,
.product-name,
.delivery-time,
.notice-time,
.deal-time,
.deal-name {
    font-size: 14px;
    color: #262626;
    line-height: 1.4;
}

.product-name {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;
    align-items: center;
    padding: 8px 4px;

    .el-button {
        margin: 0;
        padding: 4px 8px;
        font-size: 12px;
        min-width: auto;
        white-space: nowrap;
        border-radius: 4px;

        &.el-button--small {
            height: 24px;
            line-height: 1;
        }

        // 限制按钮文字长度，超出显示省略号
        &:has-text {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 标签样式优化
:deep(.el-tag) {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    border: none;

    &.el-tag--warning {
        background: #fff7e6;
        color: #fa8c16;
    }

    &.el-tag--success {
        background: #f6ffed;
        color: #52c41a;
    }

    &.el-tag--danger {
        background: #fff2f0;
        color: #ff4d4f;
    }

    &.el-tag--info {
        background: #f0f0f0;
        color: #595959;
    }
}
</style>
