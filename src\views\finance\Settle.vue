<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-12-01 10:46:45
 * @LastEditors: lexy 
 * @LastEditTime: 2024-01-30 13:44:57
-->
<script setup lang="ts">
import { reactive } from 'vue'
import PageManage from '@/components/PageManage.vue'
import { doGetShopBalance, doGetWithdrawList, doPostWithdraw } from '@/apis/finance'
import { ElMessage } from 'element-plus'
import type { TableColumnCtx } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { doPostExportSupplierWithdrawData } from '@/apis/overview'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
const showDescriptionDialog = ref(false)
/*
 *variable
 */
const { divTenThousand } = useConvert()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const searchParams = reactive({
    status: 'APPLYING',
    startDate: '',
    endDate: '',
})
const choosedDate = ref([])
const balanceInfo = reactive({
    withdrawing: '0',
    withdrawalTotal: '0',
    undrawn: '0',
})
const withdrawList = ref([])
const showWithdrawDialog = ref(false)
const withdrawValue = ref(0.01)
/*
 *lifeCircle
 */
initBalance()
initWithdrawList()
/*
 *function
 */

const formatBankAccount = (row: any, column: TableColumnCtx<any>) => {
    return `***${row.bankAccount}`
}

const handleChangeCurrent = (e: number) => {
    pageConfig.current = e
    initWithdrawList()
}
const handleChangeSize = (e: number) => {
    pageConfig.current = 1
    pageConfig.size = e
    initWithdrawList()
}
const handleChangeTab = () => {
    initWithdrawList()
}
const handleChangeDate = (e: string[] | null) => {
    if (e) {
        searchParams.startDate = e[0]
        searchParams.endDate = e[1]
    } else {
        searchParams.startDate = ''
        searchParams.endDate = ''
    }
    initWithdrawList()
}
// 商家端 'SHOP'，供应商 'SUPPLIER'
const type = 'SUPPLIER'
const handleSubmit = async () => {
    try {
        if (withdrawValue.value < 0.01) {
            return ElMessage.error('输入值最小为0.01')
        }
        if (withdrawValue.value > Number(divTenThousand(balanceInfo.undrawn))) {
            return ElMessage.error('提现金额不可大于供应商余额')
        }
        const { code, msg } = await doPostWithdraw(withdrawValue.value * 10000, type)
        if (code === 200) {
            ElMessage.success('申请成功')
            showWithdrawDialog.value = false
            initBalance()
            initWithdrawList()
        } else {
            ElMessage.error(msg ? msg : '申请失败')
        }
    } catch (error) {
        ElMessage.error('申请失败')
    }
}
const handleCloseDialog = () => {
    withdrawValue.value = 0.01
}
const handleShowDialog = () => {
    showWithdrawDialog.value = true
}
async function initBalance() {
    const { code, data, msg } = await doGetShopBalance()
    if (code === 200) {
        balanceInfo.undrawn = data.undrawn
        balanceInfo.withdrawalTotal = data.withdrawalTotal
        balanceInfo.withdrawing = data.withdrawing
    } else {
        ElMessage.error(msg ? msg : '获取余额信息失败')
    }
}
async function initWithdrawList() {
    const { code, data, msg } = await doGetWithdrawList({ ...pageConfig, ...searchParams, type })
    if (code === 200) {
        withdrawList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error(msg ? msg : '获取提现列表失败')
    }
}
function convertStatus(val: string) {
    const statusType: { [x: string]: string } = {
        APPLYING: '待审核',
        SUCCESS: '已到账',
        CLOSED: '已拒绝',
        FORBIDDEN: '已拒绝',
    }
    return statusType[val]
}

const checkedData = ref<any[]>([])
const handleExport = async () => {
    const params: any = {}
    if (checkedData.value.length) {
        params.exportIds = checkedData.value.map((item) => item.id)
    }
    const { code, msg } = await doPostExportSupplierWithdrawData(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}
</script>

<template>
    <div class="settle">
        <div class="settle__item">
            <div class="settle__item--title">供应商余额</div>
            <div class="settle__item-bottom">
                <div class="settle__item--price">{{ divTenThousand(balanceInfo.undrawn).toFixed(2) }}</div>
                <div class="settle__item--btn" @click="handleShowDialog">提现</div>
            </div>
        </div>
        <div class="settle__item">
            <div class="settle__item--title">累计提现</div>
            <div class="settle__item-bottom">
                <div class="settle__item--price">{{ divTenThousand(balanceInfo.withdrawalTotal) }}</div>
            </div>
        </div>
        <div class="settle__item">
            <div class="settle__item--title">提现中</div>
            <div class="settle__item-bottom">
                <div class="settle__item--price">{{ divTenThousand(balanceInfo.withdrawing) }}</div>
            </div>
        </div>
    </div>
    <div class="settle__tabs">
        <el-tabs v-model="searchParams.status" @tab-change="handleChangeTab">
            <el-tab-pane label="待审核" name="APPLYING" />
            <el-tab-pane label="已到账" name="SUCCESS" />
            <el-tab-pane label="已拒绝" name="FORBIDDEN" />
        </el-tabs>
        <div class="settle__tabs--export">
            <el-button size="small" type="primary" @click="handleExport">导出</el-button>
            <el-icon class="export-icon" @click="showDescriptionDialog = true"><QuestionFilled /></el-icon>
        </div>
    </div>
    <el-date-picker
        v-model="choosedDate"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        type="daterange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="margin-bottom: 14px"
        @change="handleChangeDate"
    />
    <el-table :data="withdrawList" :header-cell-style="{ background: '#F6F8FA' }" :row-style="{ height: '68px' }" @selection-change="(data) => (checkedData = data)">
        <el-table-column width="55" fixed="left" type="selection" />
        <el-table-column label="商户订单号" prop="reqTraceNum" align="center" width="170" />
        <el-table-column label="银行名" prop="bankName" align="center" width="120" />
        <el-table-column label="银行账号" prop="bankName" align="center" width="100" :formatter="formatBankAccount" />
        <el-table-column label="提现金额" align="center">
            <template #default="{ row }">
                <div>{{ divTenThousand(row.orderAmount) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="手续费" align="center">
            <template #default="{ row }">
                <div>{{ divTenThousand(row.couponAmount) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template #default="{ row }">
                <div>{{ convertStatus(row.status) }}</div>
            </template>
        </el-table-column>
        <el-table-column v-if="searchParams.status === 'FORBIDDEN'" label="拒绝说明" prop="reason" align="center"></el-table-column>
        <el-table-column v-else label="备注" prop="remark" align="center"></el-table-column>
        <el-table-column label="申请时间" prop="applyTime" align="center" width="200"></el-table-column>
    </el-table>
    <page-manage :page-num="pageConfig.current" :page-size="pageConfig.size" :total="pageConfig.total" @handle-current-change="handleChangeCurrent" @handle-size-change="handleChangeSize" />
    <el-dialog v-model="showWithdrawDialog" title="提现信息" @close="handleCloseDialog">
        <template #header="{ titleId, titleClass }">
            <div class="dialog-header">
                <h4 :id="titleId" :class="titleClass">提现信息</h4>
                <h5 style="margin-bottom: 2px; color: #f53f3f">手续费：1元/笔</h5>
            </div>
        </template>
        <el-form>
            <el-form-item label="提现金额" required>
                <decimal-input v-model="withdrawValue" :max="999999" :min="1" :decimal-places="2" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="showWithdrawDialog = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
        </template>
    </el-dialog>
    <el-dialog v-model="showDescriptionDialog" :width="650" title="供应商余额说明">
        <p class="dialog-line">
            1、供应商余额是指店铺卖出商品或采购卖出商品（订单状态已完成计入）获得的经营收入，供应商可向平台发起提现即将资金由平台处转到供应商账户上以此实现供应商与平台之间的资金(分账)结算。
        </p>
        <p class="dialog-line">2、累计提现是指该商家所有提现成功的金额之和</p>
        <p class="dialog-line">3、提现中是指待审批提现工单中的提现金额之和，提现失败提现金额还给供应商余额，提现成功提现金额计入累计提现中</p>
        <!-- <p class="dialog-line">4、店铺采购商品如使用线上支付(店铺余额)，则供应商余额会增加相应金额，具体明细可查看对账单和采购订单。</p> -->
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(settle) {
    color: #fff;
    @include flex(space-between);
    margin-bottom: 10px;
    @include e(item) {
        width: 270px;
        height: 130px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 13px;
        padding: 0 30px;
        border-radius: 14px;
        @include m(title) {
            margin-bottom: 6px;
        }
        @include m(price) {
            font-size: 30px;
            font-weight: bold;
            &::before {
                content: '￥';
                display: inline-block;
                font-size: 13px;
                vertical-align: baseline;
            }
        }
        @include m(btn) {
            width: 50px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            cursor: pointer;
            border-radius: 10px;
            border: 1px solid #fff;
        }
        &:nth-child(1) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_1.png');
        }
        &:nth-child(2) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_2.png');
        }
        &:nth-child(3) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_3.png');
        }
        &:nth-child(n) {
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    @include e(item-bottom) {
        @include flex(space-between, flex-end);
    }
    @include e(tabs) {
        position: relative;
        @include m(export) {
            position: absolute;
            top: 10px;
            right: 0;
            @include flex();
            @include b(export-icon) {
                font-size: 22px;
                margin-left: 10px;
            }
        }
    }
}
.dialog-header {
    @include flex(flex-start, flex-end);
    gap: 16px;
}
</style>
