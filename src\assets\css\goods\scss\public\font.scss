/*字体颜色(font-color fc-)*/

.fc-primary {
    color: #409eff;
}

.fc-white,
.fc-f,
.fc-fff {
    color: #fff;
}

.fc-success {
    color: #67c23a;
}

.fc-fdde51 {
    color: #FDDE51;
}

.fc-warning {
    color: #eb9e05;
}

.fc-danger {
    color: #fa5555;
}

.fc-19c3a8 {
    color: #19c3a8;
}

.fc-info {
    color: #878d99;
}

.fc-30 {
    color: #303030;
}

.fc-01 {
    color: #010101;
}

.fc-1e83d3 {
    color: #1e83d3;
}

.fc-adaaa7 {
    color: #adaaa7;
}

.fc-fe5f4b {
    color: #fe5f4b;
}

.fc-c4578e {
    color: #C4578E;
}

.fc-FB8B52 {
    color: #FB8B52;
}

.fc-white,
.fc-f,
.fc-fff {
    color: #fff;
}

.fc-4f627b {
    color: #4f627b;
}

.fc-ff4343 {
    color: #ff4343;
}

.fc-ff0036 {
    color: #ff0036;
}

.fc-b2 {
    color: #b2b2b2;
}

.fc-7f {
    color: #7f7f7f;
}

.fc-d,
.fc-ddd {
    color: #ddd;
}

.fc-e,
.fc-eee {
    color: #eee;
}

.fc-333,
.fc-3 {
    color: #333;
}

.fc-666,
.fc-6 {
    color: #666;
}

.fc-999,
.fc-9 {
    color: #999;
}

/*字体大小(font-size  fs-) */

.fs-8 {
    font-size: 8px;
}

.fs-10 {
    font-size: 10px;
}

.fs-12 {
    font-size: .75rem;
}

.fs-14 {
    font-size: 14px;
}

.fs-16 {
    font-size: 16px;
}

.fs-18 {
    font-size: 1.125rem;
}

.fs-20 {
    font-size: 1.25rem;
}

.fs-22 {
    font-size: 22px;
}

.fs-24 {
    font-size: 24px;
}

.fs-26 {
    font-size: 26px;
}

.fs-28 {
    font-size: 28px;
}

.fs-30 {
    font-size: 1.875rem;
}

.fs-32 {
    font-size: 32px;
}

.fs-34 {
    font-size: 34px;
}

.fs-36 {
    font-size: 36px;
}

.fs-24 {
    font-size: 24px;
}

.fs-38 {
    font-size: 38px;
}

.fs-40 {
    font-size: 2.5rem;
}

.fs-42 {
    font-size: 42px;
}

.fs-44 {
    font-size: 44px;
}

.fs-48 {
    font-size: 48px;
}

.fs-50 {
    font-size: 50px;
}

.fs-52 {
    font-size: 52px;
}

.fs-54 {
    font-size: 54px;
}

.fs-56 {
    font-size: 56px;
}

.fs-58 {
    font-size: 58px;
}

.fs-60 {
    font-size: 60px;
}

.fs-70 {
    font-size: 70px;
}

.fs-80 {
    font-size: 80px;
}

.fs-90 {
    font-size: 90px;
}

.fs-100 {
    font-size: 100px;
}

/*加粗(font-weight fw-)*/

.fw-100 {
    font-weight: 100;
}

.fw-bold {
    font-weight: bold;
}

.fw-normal {
    font-weight: normal;
}