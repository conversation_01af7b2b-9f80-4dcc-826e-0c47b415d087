/*
 * @description: logistics-template-api
 * @Author: lexy
 * @Date: 2022-06-23 15:28:16
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-22 14:29:37
 */
import { get, post, put, del } from '../http'
/**
 * @LastEditors: lexy
 * @description: 获取模板列表
 * @param {number} current
 * @param {number} size
 * @returns {*}
 */
export const doGetLogisticsTemplateList = (current: number, size: number) => {
    return get({
        url: 'gruul-mall-freight/logistics/template/get/list/',
        params: {
            current,
            size,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 添加物流模板
 * @param {*} data
 * @returns {*}
 */
export const doAddLogisticsTemplate = (data) => {
    return post({
        url: 'gruul-mall-freight/logistics/template/save/info',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除物流模板
 * @param {*} id
 * @returns {*}
 */
export const doDelLogisticsTemplate = (id) => {
    return del({
        url: `gruul-mall-freight/logistics/template/delete/info/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取单个物流模板信息
 * @param {*} id
 * @returns {*}
 */
export const doGetLogisticsTemplateInfoById = (id) => {
    return get({
        url: `gruul-mall-freight/logistics/template/get/info/`,
        params: { id },
    })
}

/**
 * @LastEditors: lexy
 * @description:编辑单个物流模板信息
 * @param {*} data
 * @returns {*}
 */
export const doPutLogisticsTemplateInfoById = (data: any) => {
    return post({
        url: `gruul-mall-freight/logistics/template/update/info`,
        data,
    })
}
