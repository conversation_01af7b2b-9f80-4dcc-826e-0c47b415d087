/***** background-color *****/

.bc-white, .bc-f, .bc-fff{
    background-color: #fff;
}
.bc-primary {
    background-color: #409eff;
}
.bc-f8 {
    background-color: #f8f8f8;
}
.bc-eef1f6 {
    background-color: #eef1f6;
}
.bc-e9f3fb{
    background-color: #e9f3fb; 
}
.bc-f2 {
    background-color: #f2f2f2;
}
.bc-f4 {
    background-color: #f4f4f4;
}
.bc-fa {
    background-color: #fafafa;
}
.bc-eee, .bc-e{
    background-color: #eee;
}

.bc-ddd, .bc-d{
    background-color: #ddd;
}

.bc-999, .bc-9{
    background-color: #999;
}

.bc-f0f1f5{
    background-color: #F0F1F5;
}

.bc-e5e5e5,.bc-e5 {
    background-color: #e5e5e5;
}

.bc-D8DCE5 {
    background-color: #d8dce5;
}

.bc-1e83d3 {
    background-color: #1e83d3;
}

.bc-6bafe3 {
    background-color: #6bafe3;
}

.bc-b6de16 {
    background-color: #b6de16;
}
.bc-fdde51 {
    background-color: #FDDE51;
}
.bc-daf0dc {
    background-color: #DAF0DC;
}
.bc-333, .bc-3{
    background-color: #333;
}
.bc-black, .bc-0, .bc-000{
    background-color: #000000;
}
.bc-transparent{
    background-color: transparent;
}

.bc-success {
    background-color: #67c23a;
}

.bc-warning {
    background-color: #eb9e05;
}

.bc-danger {
    background-color: #fa5555;
}
.bc-fe4e63{
    background-color: #fe4e63;
}

.bc-info {
    background-color: #878d99;
}
.bc-rgba-0003{
    background-color: rgba(0,0,0,.3);
}
.bc-rgba-0005{
    background-color: rgba(0,0,0,.5);
}
.bc-rgba-0006{
    background-color: rgba(0,0,0,.6);
}
.bc-rgba-0007{
    background-color: rgba(0,0,0,.7);
}
.bc-rgba-0008{
    background-color: rgba(0,0,0,.8);
}