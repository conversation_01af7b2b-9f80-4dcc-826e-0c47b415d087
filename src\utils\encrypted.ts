import CryptoJS from 'crypto-js'

const KEY = import.meta.env.VITE_CRYPTO_KEY // 16字节密钥
const IV = import.meta.env.VITE_CRYPTO_IV // 16字节初始化向量

export default function encrypt(plainText: string) {
    const key = CryptoJS.enc.Utf8.parse(KEY)
    const iv = CryptoJS.enc.Utf8.parse(IV)

    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    })

    return encrypted.toString()
}
