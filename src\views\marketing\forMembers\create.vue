<!--
 * @description:  新增/编辑会员专享
 * @Author: lexy
 * @Date: 2025-02-27 14:21:34
-->
<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useEventListener } from '@vueuse/core'
import { integrationGoods, integrationGoodsEdit, getSpanId, objectSpanMethod } from './index'
import { doPostPromotion, doGetPromotionDetail, doGetPromotionProductsList, doGetMemberType } from '@/apis'
import QChooseGoodsPopup from '@/components/q-choose-goods-popup/q-choose-goods-popup.vue'
import type { FormInstance } from 'element-plus'
import type { ChoosedGoodCallBack } from '@/components/q-choose-goods-popup/types'
import type { OnlyProductSkuType, OnlyPromotionType, StockType, MemberType, OnlyPromotionSearchParams } from '@/apis/marketing/model'

import DecimalInput from '@/components/decimal-input/decimal-input.vue'
type SearchConfig = 'supplierProductStatus' | 'maxPrice' | 'minPrice' | 'keyword' | 'platformCategoryFirstId' | 'platformCategorySecondId' | 'platformCategoryThirdId'
/*
 *variable
 */
const loading = ref(false)
const router = useRouter()
const onlyId = useRoute().query?.onlyId as string
const { divTenThousand } = useConvert()
const ruleFormRef = ref<FormInstance>()
const searchConfig = reactive<Pick<OnlyPromotionSearchParams, SearchConfig>>({
    keyword: '',
    supplierProductStatus: 'SELL_ON',
})
const goodsData = ref<OnlyProductSkuType[]>([])
const chooseGoodsPopupShow = ref(false)
const shopid = useShopInfoStore().shopInfo.id
const formData = ref<OnlyPromotionType>({
    date: [],
    name: '',
    startTime: '',
    endTime: '',
    joinMember: [],
    payLimit: 'UNLIMITED',
    applyTypes: [],
    onlyProducts: [],
})
const pageConfig = reactive({ current: 1, pages: 1, size: 10 })
const goodsDataRef = ref<HTMLDivElement>()
const rules = reactive({
    name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
    date: [{ required: false, message: '请选择活动时段', trigger: ['blur', 'change'] }],
    joinMember: [{ required: false, message: '请选择参与会员', trigger: ['blur', 'change'] }],
})
const isDisable = computed(() => !!useRoute().query.isLookUp)
const memberTypeList = ref<MemberType[]>([])

/*
 *function
 */

/**
 * @LastEditors: lexy
 * @description: 获取参与会员类型
 * @returns {*}
 */
async function initMemberType() {
    const { code, data, msg } = await doGetMemberType()
    if (code !== 200) {
        ElMessage.error(msg || '获取参与会员类型失败')
        memberTypeList.value = []
    }
    memberTypeList.value = data || []
}

/**
 * @LastEditors: lexy
 * @description: 编辑查询活动下商品
 * @returns {*}
 */
async function initOnlyPromotionProduct(isLoad = false) {
    if (!isLoad) {
        // 刷新
        pageConfig.current = 1
        goodsData.value = (await getOnlyPromotionProduct()) || []
        if (goodsData.value.length) {
            getSpanId(goodsData.value)
        }
    } else if (isLoad && pageConfig.current < pageConfig.pages) {
        // 更新
        pageConfig.current++
        const goods = await getOnlyPromotionProduct()
        goodsData.value = goodsData.value.concat(goods || [])
        if (goodsData.value.length) getSpanId(goodsData.value)
    }
}

/**
 * @LastEditors: lexy
 * @description: 编辑获取会员专享商品信息
 * @returns {*}
 */
async function getOnlyPromotionProduct() {
    const { code, data, msg } = await doGetPromotionProductsList(shopid, onlyId, pageConfig)
    if (code !== 200) {
        ElMessage.error(msg || '获取会员专享商品信息失败')
        return []
    }
    if (data) {
        pageConfig.pages = data.pages
        const records = data?.records as OnlyProductSkuType[]
        const arr = integrationGoodsEdit(records, formData.value.shopId)
        return arr
    }
    return []
}

/**
 * @LastEditors: lexy
 * @description: 获取活动信息和商品信息回显
 * @returns {*}
 */
async function initOnlyPromotion() {
    if (onlyId) {
        const { code, data } = await doGetPromotionDetail(shopid, onlyId)
        if (code !== 200) {
            ElMessage.error('获取会员专享信息失败')
            return
        }
        const { id, onlyName, payLimit, joinMember, startTime, endTime, applyTypes, shopId, onlyStatus } = data as OnlyPromotionType
        formData.value.onlyId = id
        formData.value.name = onlyName
        formData.value.date = [startTime, endTime]
        formData.value.startTime = startTime
        formData.value.endTime = endTime
        formData.value.applyTypes = applyTypes || []
        formData.value.shopId = shopId
        formData.value.onlyStatus = onlyStatus
        formData.value.payLimit = payLimit

        formData.value.joinMember = []
        formData.value.limitMember = []
        if (joinMember && joinMember.length > 0) {
            for (let i = 0; i < joinMember.length; i++) {
                const member = memberTypeList.value.find((memberType) => memberType.id === joinMember[i].memberId)
                if (member) {
                    if (joinMember[i].include === true) {
                        formData.value.joinMember.push(member)
                    }
                    if (joinMember[i].include === false) {
                        formData.value.limitMember.push(member)
                    }
                }
            }
        }

        initOnlyPromotionProduct()
    }
}

/**
 * @LastEditors: lexy
 * @description: 活动时间改变
 * @param {*} val
 * @returns {*}
 */
const handleDateChange = (val: any) => {
    if (val?.length) {
        if (new Date(val[0]) >= new Date(val[1])) {
            ElMessage.error('开始时间必须小于结束时间')
            formData.value.date = []
            return
        }
        formData.value.startTime = val[0]
        formData.value.endTime = val[1]
    }
}

/**
 * @LastEditors: lexy
 * @description: 表单提交
 * @param {*} formEl
 * @returns {*}
 */
const handleSubmit = async (formEl?: FormInstance) => {
    try {
        if (isDisable.value) {
            // 状态只能查看
            router.back()
            return
        }
        if (!formEl) return
        await formEl.validate()
        if (!validate()) return
        loading.value = true
        const { endTime, name, startTime, payLimit, applyTypes, onlyStatus, joinMember, limitMember = [] } = formData.value
        const { id: shopId, name: shopName } = useShopInfoStore().shopInfo

        var joinMemberIds = joinMember.flatMap(({ id, name, paid, labelJson }) => ({ id, memberId: id, memberName: name, name, paid, labelJson, include: true }))
        var limitMemberIds = limitMember?.flatMap(({ id, name, paid, labelJson }) => ({ id, memberId: id, memberName: name, name, paid, labelJson, include: false }))

        const onlyProducts = integrationGoods(goodsData.value)

        const param: any = {
            name,
            shopId,
            shopName,
            joinMember: joinMemberIds.concat(limitMemberIds),
            onlyStatus,
            payLimit,
            startTime,
            endTime,
            applyTypes,
            onlyProducts,
        }
        const update = !!(onlyId && !isDisable.value)
        if (update) {
            // 编辑状态 携带 id
            param.onlyId = onlyId
        }
        console.log('handleSubmit formData.value =>', param)

        const { code, msg } = await doPostPromotion(param, update)
        if (msg?.includes('需要是一个将来的时间')) {
            ElMessage.error('活动时间段需要是一个将来的时间')
            loading.value = false
            return
        }
        if (code !== 200) {
            ElMessage.error(msg ? msg : `${update ? '编辑' : '保存'}失败`)
            loading.value = false
            return
        }
        loading.value = false
        ElMessage.success(`${update ? '编辑' : '保存'}成功`)
        reset()
        router.push('/marketing/forMembers')
    } catch (error) {
        loading.value = false
    }
}

function validate() {
    if (!goodsData.value.length) {
        ElMessage.info('请选择适用商品')
        return false
    }
    if (!goodsData.value.every((item) => item.sku.onlyPrice)) {
        ElMessage.info('请输入会员专享价')
        return false
    }
    if (!goodsData.value.every((item) => item.sku.onlyStock)) {
        ElMessage.info('请输入会员专享库存')
        return false
    }
    return true
}

/**
 * @LastEditors: lexy
 * @description: 商品触底请求下一页
 * @returns {*}
 */
useEventListener(goodsDataRef, 'scroll', (evt) => {
    if (goodsDataRef.value && onlyId) {
        const isLower = goodsDataRef.value.scrollTop + goodsDataRef.value.clientHeight + 1 >= goodsDataRef.value.scrollHeight
        if (isLower) {
            initOnlyPromotionProduct(true)
        }
    }
})
// 编辑逻辑 e
/**
 * @LastEditors: lexy
 * @description: 选择商品做数据处理
 * @returns {*}
 */
const handleConfirm = (e: ChoosedGoodCallBack) => {
    const newArr: any[] = []
    for (let index = 0; index < e.tempGoods.length; index++) {
        const { productName, albumPics, shopId, onlyLimit = 0, storageSkus = [] } = e.tempGoods[index]
        if (storageSkus?.length) {
            for (let j = 0; j < storageSkus.length; j++) {
                const { stockType, specs, productId, stock: skuStock, id: skuId, purchasePrice: skuPrice, commission: skuCommission } = storageSkus[j]

                if ((Number(skuStock) > 0 && stockType === 'LIMITED') || stockType === 'UNLIMITED') {
                    newArr.push({
                        sku: {
                            onlyPrice: 0.01,
                            onlyStock: 1,
                            onlyLimit: 0,
                            payLimit: 0,
                            actualPaidPrice: 0,
                            commission: 0,
                            productId,
                            skuId,
                            skuPrice,
                            skuStock,
                            skuCommission,
                            stockType,
                            skuName: specs.join(''),
                        },
                        onlyLimit,
                        productId,
                        productName,
                        productPic: albumPics,
                        shopId,
                        onlyId,
                        desc: '',
                    })
                }
            }
        }
    }
    if (newArr?.length === 0) {
        return ElMessage.error('请至少选择一个存在库存的商品')
    }
    console.log('handleConfirm goodsData =>', newArr)
    // // 列表渲染前合并单元格
    // 去重：只添加goodsData.value中不存在相同productId和skuId的项
    const existingSkus = new Set(goodsData.value.map((item) => `${item.productId}_${item.sku.skuId}`))
    const filteredNewArr = newArr.filter((item) => {
        const key = `${item.productId}_${item.sku.skuId}`
        if (existingSkus.has(key)) {
            return false
        }
        existingSkus.add(key)
        return true
    })
    goodsData.value = [...goodsData.value, ...filteredNewArr]
    getSpanId(goodsData.value)
    chooseGoodsPopupShow.value = false
    // choosedGoods.value = arr
}
const reset = () => {
    if (!ruleFormRef.value) return
    ruleFormRef.value.resetFields()
    formData.value = {
        date: '',
        onlyId: '',
        shopId: '',
        name: '',
        joinMember: [],
        payLimit: 'UNLIMITED',
        onlyStatus: 'NOT_STARTED',
        startTime: '',
        endTime: '',
        applyTypes: [],
        onlyProducts: [],
        limitMember: [],
    }
    loading.value = false
    goodsData.value = []
}
const handleDelGoods = async (goodsId: string) => {
    const isValidate = await ElMessageBox.confirm('确定移除该商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (isValidate) {
        const goodsIndex = goodsData.value.findIndex((item: OnlyProductSkuType) => item.sku.skuId === goodsId)
        if (goodsIndex !== -1) {
            goodsData.value.splice(goodsIndex, 1)
            getSpanId(goodsData.value)
        }
    }
}
// 时间处理 s
const disabledDate = (time: string) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes())
    const timeDate = new Date(time)
    return timeDate.getTime() < today.getTime()
}

// 时间处理 e
/**
 * @LastEditors: lexy
 * @description: 筛选商品
 * @returns {*}
 */
const handleChooseGoods = () => {
    chooseGoodsPopupShow.value = true
}
const rulePrice = (skuPrice: number) => divTenThousand(skuPrice).toNumber()
const ruleStock = (stockType: StockType, skuStock: string) => {
    return stockType === 'UNLIMITED' ? 100000 : parseInt(skuStock)
}
const dialogVisible = ref(false)
const currentBatchSetProductId = ref<string | number>('0')
/**
 * 批量设置
 */
const batchSet = ref({
    stock: 1,
    price: 0.01,
    commission: 0,
})
const handleClose = () => {
    batchSet.value = { stock: 1, price: 0.01, commission: 0 }
    dialogVisible.value = false
}
/**
 * 批量设置提交
 * 一口价：0<=N<=销售价
 * 佣金:0<=N<=一口价
 * 优惠库存：0<N---跟规格库存无关
 * 限购数量：0<=N<=优惠库存
 */
const submitBatch = () => {
    // 检查批量设置数据是否合法
    if (batchSet.value.stock < 0) {
        ElMessage.error('优惠库存不能小于0')
        return
    }
    if (batchSet.value.price < 0.01) {
        ElMessage.error('一口价不能小于0.01')
        return
    }
    if (batchSet.value.commission < 0) {
        ElMessage.error('佣金不能小于0')
        return
    }

    goodsData.value.forEach((item) => {
        if (item.productId === currentBatchSetProductId.value) {
            const { stockType, skuPrice, skuStock } = item.sku

            // 优惠库存: 0<N---跟规格库存无关
            const maxStock = ruleStock(stockType, skuStock)
            item.sku.onlyStock = batchSet.value.stock > maxStock ? maxStock : batchSet.value.stock

            // 一口价: 0<=N<=销售价
            const maxPrice = rulePrice(skuPrice)
            item.sku.onlyPrice = batchSet.value.price > maxPrice ? maxPrice : batchSet.value.price

            // 佣金: 0<=N<=一口价
            item.sku.commission = batchSet.value.commission > item.sku.onlyPrice ? item.sku.onlyPrice : batchSet.value.commission
        }
    })

    // 批量设置后，需要重新计算限购数量
    // 规则：要小于所有商品的优惠库存的最小值
    const minStock = Math.min(...goodsData.value.map((item) => item.onlyStock))
    goodsData.value.forEach((item) => {
        item.onlyLimit = item.onlyLimit > minStock ? minStock : item.onlyLimit
    })

    dialogVisible.value = false
}
/**
 * 点击批量设置
 * @param row
 */
const handleBacthSetClick = (row: OnlyProductSkuType) => {
    currentBatchSetProductId.value = row.productId
    batchSet.value.commission = row.sku.commission
    batchSet.value.price = row.sku.onlyPrice
    batchSet.value.stock = row.sku.onlyStock
    dialogVisible.value = true
}
/**
 * @LastEditors: lexy
 * @description: 限购设置改变
 * @param {*} val
 * @returns {*}
 */
const handlePayLimitChange = (val: string) => {
    // 不限购时 onlyLimit 为空
    if (val === 'UNLIMITED') {
        goodsData.value.forEach((item) => (item.onlyLimit = 0))
    }
}
// 最小库存
const minStock = computed(() => Math.min(...goodsData.value.map((item) => (isDisable.value ? Number(item.sku.skuStock) : Number(item.sku?.onlyStock)))))
/**
 * @LastEditors: lexy
 * @description: 限购数量改变
 * @param {*} val
 * @returns {*}
 */
const handleOnlyLimitChange = (row: OnlyProductSkuType, index: number) => {
    // 限购数量要小于所有商品的优惠库存的最小值
    goodsData.value[index].onlyLimit = row.onlyLimit > minStock.value ? minStock.value : row.onlyLimit
}

/*
 *lifeCircle
 */
onMounted(() => {
    initMemberType().then(() => {
        initOnlyPromotion()
    })
})

const getGoodMainPic = (picStrs: string) => {
    if (!picStrs) {
        return ''
    }
    return picStrs.split(',')
}
/**
 * @LastEditors: lexy
 * @description: 限制会员是否能被选中
 * @param id
 * @returns
 */
const isCheckedLimit = (id: string) => {
    if (formData.value.joinMember.length === 0) {
        return false
    }
    return formData.value.joinMember.some((item) => item.id === id)
}
/**
 * @LastEditors: lexy
 * @description: 参与会员改变
 * @param val
 * @returns
 */
const handleJoinMemberChange = (val: MemberType[]) => {
    if (!formData.value.limitMember || formData.value.limitMember.length === 0) {
        return
    }
    for (let index = 0; index < val.length; index++) {
        const member = val[index]
        formData.value.limitMember = formData.value.limitMember.filter((item) => item.id !== member.id)
    }
}
</script>

<template>
    <div>
        <div style="padding: 0 40px">
            <h1 class="title">基本信息</h1>
            <el-form ref="ruleFormRef" :model="formData" :rules="rules" label-width="auto">
                <el-form-item label="活动名称" prop="name">
                    <el-input v-model.trim="formData.name" :disabled="isDisable" :maxlength="10" :minlength="3" placeholder="请输入活动名称" style="width: 60%; margin-right: 15px" />
                    <span class="msg">活动名称不超过10个字</span>
                </el-form-item>
                <el-form-item label="活动时间" prop="date" required>
                    <el-date-picker
                        v-model="formData.date"
                        :disabled="isDisable"
                        :disabled-date="disabledDate"
                        format="YYYY-MM-DD HH:mm:ss"
                        time-format="HH:mm:ss"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        type="datetimerange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        @change="handleDateChange"
                    />
                </el-form-item>
                <el-form-item label="参与会员" prop="joinMember" required>
                    <el-checkbox-group v-model="formData.joinMember" :disabled="isDisable" @change="handleJoinMemberChange">
                        <el-checkbox v-for="item in memberTypeList" :key="item.id + item.name" :value="item">{{ item.name }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="限制会员" prop="limitMember">
                    <el-checkbox-group v-model="formData.limitMember" :disabled="isDisable">
                        <el-checkbox v-for="item in memberTypeList" :key="item.id + item.name" :value="item" :disabled="isCheckedLimit(item.id)">{{ item.name }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="限购设置">
                    <el-radio-group v-model="formData.payLimit" :disabled="isDisable" style="display: flex; flex-direction: column; align-items: flex-start" @change="handlePayLimitChange">
                        <el-radio value="UNLIMITED">不限购</el-radio>
                        <!-- <el-radio value="ACTIVITY_LIMITED">每人每种商品超出限购后<span style="color: #f12f22">无法</span>购买</el-radio> -->
                        <el-radio value="ACTIVITY_ORIGIN_LIMITED">每人每种商品超出限购后原价购买</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="适用商品" required>
                    <el-row style="width: 100%">
                        <el-link :disabled="isDisable" :underline="false" type="primary" @click="handleChooseGoods"> 选择商品 </el-link>
                    </el-row>
                    <div v-if="goodsData.length" ref="goodsDataRef" class="goods-list">
                        <el-table
                            :cell-style="{ height: '80px' }"
                            :data="goodsData"
                            :header-cell-style="{
                                fontSize: '14px',
                                color: '#606266',
                                background: '#f2f2f2',
                                height: '54px',
                                fontWeight: 'normal',
                            }"
                            :span-method="objectSpanMethod"
                            height="100%"
                            border
                        >
                            <el-table-column label="商品信息">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <div class="goods-list__info">
                                        <div style="width: 60px; height: 60px; margin: 10px">
                                            <el-image
                                                :preview-src-list="getGoodMainPic(row.productPic)"
                                                :preview-teleported="true"
                                                :src="getGoodMainPic(row.productPic)[0]"
                                                fit="cover"
                                                style="width: 60px; height: 60px"
                                            />
                                        </div>
                                        <div class="goods-list__goods-list__info-name">
                                            <el-button v-show="!isDisable" link :disabled="isDisable" type="primary" @click="handleBacthSetClick(row)"> 批量设置 </el-button>
                                            <div class="goods-list__goods-list__info-name--name">
                                                {{ row.productName }}
                                            </div>
                                            <!-- <div class="goods-list__goods-list__info-name--price">{{ row.salePrice }}</div> -->
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="专享说明" width="130px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <el-input
                                        v-model="row.desc"
                                        :disabled="isDisable"
                                        placeholder="请输入"
                                        :maxlength="6"
                                        style="width: 100%"
                                        @input="(val) => (row.desc = val.replace(/[^\u4e00-\u9fa5a-zA-Z\s]/g, ''))"
                                    />
                                    <div class="table-msg">最多输入6个字，支持中英文和空格</div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="限购数量" width="100px">
                                <template #default="{ row, $index }: { row: OnlyProductSkuType, $index: number }">
                                    <!-- 限购数量要小于所有商品的优惠库存的最小值 -->
                                    <el-input-number
                                        v-model="row.onlyLimit"
                                        :controls="false"
                                        :disabled="isDisable || formData.payLimit === 'UNLIMITED'"
                                        :max="minStock > 99 ? 99 : minStock"
                                        :min="1"
                                        :precision="0"
                                        style="width: 100%"
                                        @change="handleOnlyLimitChange(row, $index)"
                                    />
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="规格" width="70px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <div class="table-msg">
                                        {{ row.sku.skuName || '无' }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="一口价" width="120px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <decimal-input
                                        v-model="row.sku.onlyPrice"
                                        :decimal-places="2"
                                        :min="0"
                                        :max="divTenThousand(row.sku.skuPrice).toNumber()"
                                        style="width: 80%"
                                        :disabled="isDisable"
                                    />
                                    <div class="table-msg">销售价：￥{{ rulePrice(row.sku.skuPrice) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="佣金" width="100px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <decimal-input v-model="row.sku.commission" :decimal-places="2" :min="0" :max="row.sku.onlyPrice" style="width: 100%" :disabled="isDisable" />
                                    <div class="table-msg">需小于一口价</div>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="优惠库存" width="100px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <el-input-number v-if="!isDisable" v-model="row.sku.onlyStock" :controls="false" :disabled="isDisable" :min="0" :precision="0" style="width: 100%" />
                                    <template v-else>
                                        <el-input-number v-model="row.sku.skuStock" :controls="false" :disabled="isDisable" style="width: 100%" />
                                        <div class="table-msg">剩余优惠库存 {{ row.sku?.onlyStock || '0' }}</div>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="60px">
                                <template #default="{ row }: { row: OnlyProductSkuType }">
                                    <el-link :disabled="isDisable" :underline="false" type="primary" @click="handleDelGoods(row.sku.skuId)"> 删除 </el-link>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-form-item>
                <!-- <el-form-item label="可用抵扣">
            <el-checkbox v-model="formData.deductionType" :disabled="isDisable">
                积分
                <span class="msg">仅勾选的抵扣方式，可在下单时间时使用</span>
            </el-checkbox>
        </el-form-item> -->
            </el-form>
            <div class="nav-button" v-if="isDisable">
                <el-button plain round @click="$router.back()">返回</el-button>
            </div>
            <div class="nav-button" v-else>
                <el-button plain round @click="$router.back()">取消</el-button>
                <el-button :loading="loading" round type="primary" @click="handleSubmit(ruleFormRef)"> 确定 </el-button>
            </div>
        </div>
        <!-- 选择商品弹出 s-->
        <q-choose-goods-popup v-model="chooseGoodsPopupShow" :search-consignment-product="true" v-model:search-param="searchConfig" :point-goods-list="goodsData" @on-confirm="handleConfirm" />
        <!-- 选择商品弹出 e-->
    </div>
    <!-- 批量处理 s-->
    <el-dialog v-model="dialogVisible" title="批量设置" width="600" destroy-on-close center top="30vh" @close="handleClose">
        <div class="flex">
            一口价
            <decimal-input v-model="batchSet.price" :decimal-places="2" :min="0.01" style="width: 120px" />
            佣金
            <decimal-input v-model="batchSet.commission" :decimal-places="2" :min="0.01" style="width: 120px" />
            库存
            <el-input-number v-model="batchSet.stock" :precision="0" :controls="false" :min="0" style="width: 120px" />
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitBatch">确定</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 批量处理 e-->
</template>

<style lang="scss" scoped>
@include b(title) {
    font-size: 14px;
    color: #323233;
    font-weight: 700;
    margin-bottom: 20px;
}

@include b(msg) {
    font-size: 12px;
    color: #c4c4c4;
}

@include b(nav-button) {
    width: 1010px;
    position: fixed;
    bottom: 10px;
    padding: 15px 0px;
    display: flex;
    justify-content: center;
    box-shadow: 0 0px 10px 0px #d5d5d5;
    background-color: white;
    z-index: 999;
    margin: 0 auto;
    margin-left: -55px;
}

@include b(goods-list) {
    width: 100%;
    height: 500px;
    margin-bottom: 100px;
    overflow-x: scroll;
    @include e(info) {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
    }
    @include e(goods-list__info-name) {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: flex-start;
        padding: 0 16px;
        @include m(name) {
            width: 100%;
            font-size: 14px;
            @include utils-ellipsis(1);
        }
        @include m(price) {
            font-size: 14px;
            text-align: LEFT;
            color: #f12f22;
            &::before {
                content: '￥';
                font-size: 12px;
                text-align: LEFT;
                color: #f12f22;
            }
        }
    }
}

@include b(table-msg) {
    font-size: 12px;
    color: #838383;
}
.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
