<template>
    <search :show="false" @change-show="changeSearchShow" @search="handleSearch" @supplier-export="supplierExport" />
    <el-tabs v-model="activeTabName" @tab-change="handleTabChange">
        <el-tab-pane name=" ">
            <template #label>
                <span>{{ quickSearchTabName }}</span>
                <el-dropdown placement="bottom-end" trigger="click" @command="handleQuickSearchCommand">
                    <span class="el-dropdown-link" style="height: 40px" @click.stop="() => {}">
                        <el-icon class="el-icon--right">
                            <i-ep-arrow-down />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item v-for="NameItem in quickSearchTabNames" :key="NameItem" :command="NameItem">{{ NameItem }}</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </template>
        </el-tab-pane>
        <el-tab-pane v-for="item in statusSearchTabs" :key="item[0]" :label="item[1]" :name="item[0]" />
    </el-tabs>
    <template v-if="['WAITING_FOR_DELIVER', ' '].includes(activeTabName)">
        <el-button type="primary" round @click="handleDatchDeliverys"> 批量发货 </el-button>
        <el-button type="primary" round @click="openRemark()"> 批量备注 </el-button>
    </template>
    <split-table v-model:checkedItem="multiSelect" :selection="true" class="order-table" :style="{ height: tableHeight }" :data="orderDataList">
        <template #header="{ row }">
            <el-checkbox v-model="row.checked" @update:model-value="handleChangeRow($event, row?.no)"></el-checkbox>
            <div class="order-table__header">
                <span class="order-table__no">
                    <span>订单号：{{ row.no }}</span>
                    <span class="copy" @click="copyOrderNo(row.no)">复制</span>
                </span>
                <span class="order-table__freight">运费：{{ computedCalculateFreight(row?.orderItems) }}</span>
                <span v-if="getMainOrderStatusText(row) === '待发货' || getMainOrderStatusText(row) === '待入库' || getMainOrderStatusText(row) === '已完成'" class="order-table__pay">
                    已付款：<span style="font-size: 14px"><span style="font-size: 12px">￥</span>{{ divTenThousand(row.payAmount) }}</span></span
                >
                <span v-else class="order-table__pay">
                    应付款：<span style="font-size: 14px"><span style="font-size: 12px">￥</span>{{ divTenThousand(row.payAmount) }}</span>
                </span>
                <span class="order-table__mode">{{ payTypeMap[row?.extra?.pay?.payType] }}</span>
                <span v-if="row?.extra?.pay?.proof" class="order-table__proof" @click="goToShowProof(row)">付款凭证</span>
                <span class="order-table__pay-time">支付：{{ row?.timeNodes?.payTime }}</span>
                <span class="order-table__order-time">下单：{{ row.createTime }}</span>
                <el-tooltip v-if="row?.remark" :content="row.remark" trigger="hover" :teleported="false">
                    <q-icon name="icon-qizhi" size="24px" color="red" @click="openRemark(row?.no, row?.remark)" />
                </el-tooltip>
            </div>
        </template>
        <split-table-column label="商品" width="280px">
            <template #default="{ shopOrderItems }">
                <div class="order-table__commodity">
                    <el-image style="width: 63px; height: 63px" fits="cover" :src="shopOrderItems?.[0]?.image" />
                    <div style="flex: 1">
                        <span class="order-table__commodity--name" style="font-weight: bold">{{ shopOrderItems?.[0]?.productName }}</span>
                        <span class="order-table__commodity--name" style="margin-top: 10px">{{ shopOrderItems?.[0]?.specs.join(',') }}</span>
                    </div>
                    <div class="order-table__commodity--info">
                        <span>￥{{ divTenThousand(shopOrderItems?.[0]?.salePrice) }}</span>
                        <span>{{ shopOrderItems?.[0]?.num }} 件</span>
                    </div>
                </div>
            </template>
        </split-table-column>
        <split-table-column label="采购金额" width="150px" :is-mixed="true">
            <template #default="{ row }">
                <div class="order-table__amount">
                    <div class="order-table__amount--price">
                        ￥<span style="color: #f00; font-size: 14px">{{ divTenThousand(row.payAmount) }}</span>
                    </div>
                    <div class="order-table__amount--num" style="text-align: center">共 {{ row.orderItems.reduce((pre: number, item: any) => pre + item.num, 0) }} 件</div>
                </div>
            </template>
        </split-table-column>
        <split-table-column label="采购商" width="150px" :is-mixed="true">
            <template #default="{ row }">
                <div class="order-table__receiver">
                    <span>{{ row?.extra?.receiver?.name }}</span>
                    <span>{{ row?.extra?.receiver?.mobile }}</span>
                    <div><q-address :address="row?.extra?.receiver?.areaCode" />{{ row?.extra?.receiver?.address }}</div>
                </div>
            </template>
        </split-table-column>
        <split-table-column label="订单状态" width="150px" :is-mixed="true">
            <template #default="{ row }">
                <span :class="{ 'text-red': getMainOrderStatusText(row) === '待支付' }">
                    {{ getMainOrderStatusText(row) }}
                    <template v-if="getMainOrderStatusText(row) === '待支付'"> (<countdown :create-time="row?.createTime" :pay-timeout="row?.extra?.payTimeout" />) </template>
                </span>
            </template>
        </split-table-column>
        <split-table-column label="操作" width="150px" :is-mixed="true">
            <template #default="{ row }">
                <div class="order-table__actions">
                    <el-link v-for="btn in computedBtnList(row)" :key="btn.action" style="margin: 0 6px 6px" :type="btn.type" @click="handleDispatchEvent(btn.action, row)">
                        {{ btn.text }}
                    </el-link>
                </div>
            </template>
        </split-table-column>
    </split-table>
    <PageManage v-model="pagination.page" load-init :total="pagination.total" @reload="initOrderList" />
    <el-dialog v-model="showProof" title="付款凭证" width="500px">
        <img :src="currentProof" class="proof-img" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showProof = false">取消</el-button>
                <el-button type="primary" @click="showProof = false"> 确认 </el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="showAuditDialog" title="审核" width="500px" destroy-on-close>
        <audit v-model:order-info="auditOrderInfo" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showAuditDialog = false">取消</el-button>
                <el-button type="primary" @click="handleConfirmAuditOrder"> 确认 </el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="showDeliveryDialog" title="商品发货" width="700px" destroy-on-close :close-on-click-modal="false">
        <delivery ref="deliveryRef" :current-no="deliveryProps.currentNo" :list-order-items="deliveryProps.listOrderItems" :receiver="deliveryProps.receiver" :create-time="deliveryProps.createTime" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showDeliveryDialog = false">取消</el-button>
                <el-button :loading="deliveryConfirmLoading" type="primary" @click="handleConfirmDelivery"> 确认 </el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="showRemarkDialog" title="备注" width="500px" destroy-on-close>
        <remark v-model:remark="remarkData.remark" :order-nos="remarkData.orderNos" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showRemarkDialog = false">取消</el-button>
                <el-button type="primary" @click="handleConfirmRemark"> 确认 </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import search from './components/search.vue'
import SplitTable from './components/split-table/SplitTable'
import SplitTableColumn from './components/split-table/split-table-column.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import { changeShowQuickSearch } from './helper/diapatch'
import { queryOrderStatus } from './helper/constant'
import countdown from '@/views/order/components/count-down/index.vue'
import useShowProof from './hooks/useShowProof'
import audit from './components/audit.vue'
import usePurchaseOrderList from './hooks/usePurchaseOrderList'
import delivery from './components/delivery.vue'
import remark from './components/remark.vue'
import { ElMessage, ElTooltip } from 'element-plus'
import { doPostSupplierExport } from '@/apis/exportData'
/**
 * hooks
 */
const {
    handleTabChange,
    pagination,
    initOrderList,
    orderDataList,
    handleQuickSearchCommand,
    handleSearch,
    quickSearchTabName,
    quickSearchTabNames,
    activeTabName,
    getMainOrderStatusText,
    computedBtnList,
    handleDispatchEvent,
    copyOrderNo,
    computedCalculateFreight,
    payTypeMap,
    handleConfirmAudit,
    auditOrderInfo,
    showAuditDialog,
    showDeliveryDialog,
    deliveryProps,
    deliveryRef,
    handleConfirmDelivery,
    multiSelect,
    handleChangeRow,
    handleDatchDeliverys,
    remarkData,
    showRemarkDialog,
    openRemark,
    handleConfirmRemark,
    deliveryConfirmLoading,
} = usePurchaseOrderList()
const { divTenThousand } = useConvert()
const { showProof, goToShowProof, currentProof } = useShowProof()

// const tableHeight = ref('calc(100vh - 350px)')
const tableHeight = ref('calc(100vh - 300px)')
const changeSearchShow = (isShow: boolean) => changeShowQuickSearch(isShow, tableHeight)
const handleConfirmAuditOrder = () => handleConfirmAudit({ orderNo: auditOrderInfo.orderNo, success: auditOrderInfo.success })
const statusSearchTabs: any[][] = []

const initialTabs = () => {
    Object.keys(queryOrderStatus).forEach((key: string) => {
        statusSearchTabs.push([key, queryOrderStatus[key]])
    })
}
initialTabs()
initOrderList()
const supplierExport = async (val: any) => {
    if (multiSelect.value.length) {
        let exportOrderIds = ['']
        exportOrderIds = multiSelect.value.map((item) => item.mainNo)
        const { data, code, msg } = await doPostSupplierExport({ exportOrderIds })
        if (code !== 200) return ElMessage.error(msg || '导出数据失败')
    } else {
        let param = {
            no: val.no,
            supplierId: val.purchaser,
            startTime: val.date?.[0],
            endTime: val.date?.[1],
            status: activeTabName.value,
            exportOrderIds: '',
        }
        param.status = param.status.trim()
        const { data, code, msg } = await doPostSupplierExport(param)
        if (code !== 200) return ElMessage.error(msg || '导出数据失败')
    }
}
</script>

<style lang="scss" scoped>
@include b(order-table) {
    overflow-x: auto;
    height: calc(100vh - 520px);
    transition: height 0.5s;
    word-break: break-all;
    @include e(commodity) {
        width: 280px;
        display: flex;
        justify-content: space-between;
        // align-items: center;
        @include m(name) {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: box;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin: 0 10px;
        }
        @include m(info) {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
    }
    @include e(receiver) {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        line-height: 1.5;
    }
    @include e(actions) {
        display: flex;
        flex-wrap: wrap;
        .el-link + .el-link {
            margin-left: 8px;
        }
    }
    @include e(header) {
        font-size: 11px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
    }
}
.proof-img {
    width: 350px;
    height: 350px;
    object-fit: contain;
}
@include b(copy) {
    color: #1890ff;
    margin-left: 8px;
    cursor: pointer;
}
@include b(text-red) {
    color: #f00;
}
</style>
