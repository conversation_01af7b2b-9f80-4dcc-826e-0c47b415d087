/*
 * @description:
 * @Author: lexy
 * @Date: 2022-11-24 15:19:44
 * @LastEditors: lexy
 * @LastEditTime: 2024-05-09 17:16:50
 */
import { Stock } from '@/views/goods/types'
import { ElMessage } from 'element-plus'

import type { OnlyProductType, OnlyPromotionItemType } from '@/apis/marketing/model'
import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults'

import Decimal from 'decimal.js'

const { divTenThousand, mulTenThousand } = useConvert()

/**
 * @LastEditors: lexy
 * @description: 秒杀活动 item
 * @params productNum 活动商品数
 * @params peopleNum 参与人数
 * @params payOrder 支付订单数
 * @params amountReceivable  应收金额
 */
export interface ApiSecondSkillItem {
    id: string
    startTime: string
    endTime: string
    secKillName: string
    seckillStatus: SecondsKillJointType
    productNum: number
    peopleNum: number
    payOrder: number
    amountReceivable: string
    shopName?: string
}

enum Limit {
    UNLIMITED,
    PRODUCT_LIMITED,
    SKU_LIMITED,
}

/**
 * @LastEditors: lexy
 * @description: 活动状态
 * @param NOT_STARTED 未开始
 * @param PROCESSING 进行中
 * @param OVER 已结束
 * @param ILLEGAL_SELL_OFF 违规下架
 */
enum SECONDS_KILL_STATUS {
    NOT_STARTED = 'NOT_STARTED',
    PROCESSING = 'PROCESSING',
    OVER = 'OVER',
    ILLEGAL_SELL_OFF = 'ILLEGAL_SELL_OFF',
    SHOP_SELL_OFF = 'SHOP_SELL_OFF',
}

export type SecondsKillJointType = keyof typeof SECONDS_KILL_STATUS

/**
 * @LastEditors: lexy
 * @param highestPrice: 最高价
 * @param lowestPrice: 最低价
 * @param productId: 商品ID
 * @param productName: 商品名称
 * @param productPic: 商品图
 * @param shopId: 店铺ID
 * @param skus: SkuItem[]
 */
export interface ApiGoodsRetrieve {
    productId: string
    productName: string
    productPic: string
    shopId: string
    isCheck: boolean
    skus: SkuItem[]
}

export interface SkuItem {
    limitType?: keyof typeof Limit
    productId: string
    skuId: string
    skuName?: string[]
    skuPrice: string
    skuStock: string
    stockType: keyof typeof Stock
}

/**
 * @LastEditors: lexy
 * @description: 选择商品 sku 参数
 * @returns {*}
 */
export interface SkuQuery extends SkuItem {
    seckillPrice: number | string
    seckillStock: number | string
    seckillLimit: number | string
    actualPaidPrice: number | string
}

export interface GoodsListItem extends Omit<ApiGoodsRetrieve, 'skus' | 'isCheck'> {
    sku: SkuQuery
}

/**
 * @LastEditors: lexy
 * @description: 添加活动 sku 参数
 * @returns {*}
 */
export interface RequestSkuQuery extends Omit<SkuQuery, 'productId' | 'skuPrice' | 'skuName'> {
    actualPaidPrice: string
    skuName: string
}

export type ApiEditSecondSKill = Pick<OnlyPromotionItemType, 'id' | 'endTime' | 'startTime' | 'orderClosingTime' | 'applyTypes' | 'shopId' | 'onlyStatus' | 'joinMember' | 'payLimit' | 'onlyProducts'>

export const statusConfig = {
    NOT_STARTED: {
        title: '未开始',
        class: 'nots',
    },
    PROCESSING: {
        title: '进行中',
        class: 'ongoing',
    },
    OVER: {
        title: '已结束',
        class: 'hasEnded',
    },
    ILLEGAL_SELL_OFF: {
        title: '违规下架',
        class: 'off',
    },
    SHOP_SELL_OFF: {
        title: '已下架',
        class: 'off',
    },
}

/**
 * @LastEditors: lexy
 * @description: 整合商品供提交使用
 * @param {*}
 * @returns {*}
 */
export function integrationGoods(goodsData: OnlyProductType[]) {
    const map = new Map()
    const arr: OnlyProductType[] = []
    const productOnlyLimit = goodsData[0]?.onlyLimit || 0
    for (let index = 0; index < goodsData.length; index++) {
        const item = goodsData[index]
        const { productPic, productName, productId, desc } = item

        const { skuName, skuPrice, skuId, onlyPrice, onlyStock, skuStock, stockType, commission } = item.sku
        const querySku = {
            productName,
            productId,
            productPic,
            desc: !desc ? '低价好货' : desc,
            onlyProductSkus: [
                {
                    productId,
                    skuName: Array.isArray(skuName) ? skuName.join('') : skuName || '',
                    onlyPrice: mulTenThousand(onlyPrice).toNumber(),
                    skuId,
                    stockType,
                    skuStock,
                    actualPaidPrice: skuPrice,
                    onlyStock,
                    onlyLimit: productOnlyLimit,
                    commission: mulTenThousand(commission).toNumber(),
                },
            ],
        }
        if (!map.get(productId)) {
            map.set(productId, arr.length)
            arr.push(querySku)
        } else {
            const prIndex = map.get(productId)
            if (prIndex !== -1) {
                arr[prIndex].onlyProductSkus = querySku.onlyProductSkus
            } else {
                ElMessage.info('商品不存在')
            }
        }
    }
    return arr
}

export function integrationGoodsEdit(goods: OnlyProductType[], shopId: string) {
    const arr: GoodsListItem[] = []
    goods.forEach((item) => {
        const { productId, productName, productPic, desc } = item
        item.onlyProductSkus.forEach((sku) => {
            const { actualPaidPrice, onlyPrice, onlyStock, limitNum, skuId, skuName, overStock, stockType, limitType, commission } = sku
            const currentProduct = {
                productId,
                productName,
                productPic,
                shopId,
                desc,
                onlyLimit: limitNum,
                sku: {
                    limitType,
                    productId,
                    stockType,
                    onlyLimit: limitNum,
                    onlyPrice: divTenThousand(onlyPrice).toNumber(),
                    onlyStock: Number(overStock),
                    skuId,
                    skuName,
                    skuPrice: actualPaidPrice,
                    actualPaidPrice,
                    skuStock: Number(onlyStock) || 0,
                    commission: divTenThousand(commission).toNumber(),
                },
            }
            arr.push(currentProduct)
        })
    })
    return arr
}

interface SpanMethodProps {
    row: GoodsListItem
    column: TableColumnCtx<GoodsListItem>
    rowIndex: number
    columnIndex: number
}

let spanArr: number[] = []
let pos = 0
export const getSpanId = (data: GoodsListItem[]) => {
    spanArr = []
    pos = 0
    // data就是我们从后台拿到的数据
    for (let i = 0; i < data.length; i++) {
        if (i === 0) {
            //spanArr 用于存放没一行记录的合并数
            spanArr.push(1)
            //pos是spanArr的索引
            pos = 0
        } else if (data[i].productId === data[i - 1].productId) {
            spanArr[pos] += 1
            spanArr.push(0)
        } else {
            spanArr.push(1)
            pos = i
        }
    }
}
export const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
    if (columnIndex < 3) {
        const _row = spanArr[rowIndex]
        //如果行号大于0 合并
        const _col = _row > 0 ? 1 : 0
        // console.log(`rowspan:${_row} colspan:${_col}`);
        return {
            // [0,0] 表示这一行不显示，
            rowspan: _row,
            colspan: _col,
        }
    }
}
/*
 * @description:
 * @Author: lexy
 * @Date: 2022-11-23 10:16:06
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-25 10:21:06
 */
export const secondsKillStatus = {
    NOT_STARTED: '未开始',
    PROCESSING: '进行中',
    OVER: '已结束',
    SHOP_SELL_OFF: '已下架',
    ILLEGAL_SELL_OFF: '违规下架',
}
/**
 * @LastEditors: lexy
 * @description: 秒杀商品
 */
export const strategyPatternHandler = {
    NOT_STARTED: {
        color: '#333333',
        text: '即将开始',
        soldOut: false,
    },
    PROCESSING: {
        color: '#e31436',
        text: '立即抢购',
        soldOut: false,
    },
    OVER: {
        color: '#999999',
        text: '已结束',
        soldOut: false,
    },
    BUY_NOW: {
        color: '#e31436',
        text: '立即抢购',
        soldOut: false,
    },
    OUT_OF_STOCK: { color: '#999999', text: '已抢光', soldOut: true },
}

/**
 * @LastEditors: lexy
 * @description: 倒计时
 */
export const countdownStrategyPatternHandler = {
    NOT_STARTED: {
        text: '距开始',
    },
    PROCESSING: {
        text: '距结束',
    },
    OVER: {
        text: '距结束',
        soldOut: false,
    },
    ILLEGAL_SELL_OFF: {
        text: '距结束',
    },
}
export interface ApiSecondSKill {
    secKillStatus: SecondsKillJointType
    startTime: string
}
export interface ApiSecondSKillGoods {
    productId: string
    productName: string
    productPic: string
    productStock: string
    secKillId: string
    secKillPrice: string
    shopId: string
    stockStatus: SecondsKillGoodsJointType
    robbed: string
}
type SecondsKillGoodsJointType = keyof typeof SECONDS_KILL_GOODS_STATUS
/**
 * @LastEditors: lexy
 * @description: 活动商品状态
 * @param NOT_STARTED 未开始
 * @param PROCESSING 进行中
 * @param OVER 已结束
 * @param BUY_NOW 立即抢购
 * @param OUT_OF_STOCK 已抢光
 */
enum SECONDS_KILL_GOODS_STATUS {
    NOT_STARTED = 'NOT_STARTED',
    PROCESSING = 'PROCESSING',
    OVER = 'OVER',
    BUY_NOW = 'BUY_NOW',
    OUT_OF_STOCK = 'OUT_OF_STOCK',
}
type DecimalType = Decimal
// interface ReturnType {
//     pic: DecimalType
//     conversionPrice: (price: string | number | DecimalType) => DecimalType
// }
export function useConversionPrice(price: string | number | DecimalType = 0): DecimalType {
    if (!price) price = 0
    return new Decimal(price).div(10000)
}
type UnionDate = number | Date
export default class DateUtil {
    ms: Date

    constructor(ms = new Date()) {
        this.ms = ms
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年
     * @param {Date} ms
     */
    getY(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        return GMT.getFullYear()
    }
    /**
     * @LastEditors: lexy
     * @description: 获取月
     * @param {Date} ms
     */
    getM(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const m = GMT.getMonth() + 1
        return this.formatLength(m)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取日
     * @param {Date} ms
     */
    getD(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const d = GMT.getDate()
        return this.formatLength(d)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取时
     * @param {Date} ms
     */
    getH(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const H = GMT.getHours()
        return this.formatLength(H)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取分
     * @param {Date} ms
     */
    getMin(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const M = GMT.getMinutes()
        return this.formatLength(M)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取秒
     * @param {Date} ms
     */
    getS(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const S = GMT.getSeconds()
        return this.formatLength(S)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年/月/日
     * @param {Date} ms
     */
    getYMD(ms: Date | number = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const y = GMT.getFullYear()
        const m = GMT.getMonth() + 1
        const d = GMT.getDate()
        return [y, m, d].map(this.formatLength).join('/')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年-月-日
     * @param {Date} ms
     */
    getYMDs(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const y = GMT.getFullYear()
        const m = GMT.getMonth() + 1
        const d = GMT.getDate()
        return [y, m, d].map(this.formatLength).join('-')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年-月
     * @param {Date} ms
     */
    getYMs(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const y = GMT.getFullYear()
        const m = GMT.getMonth() + 1
        return [y, m].map(this.formatLength).join('-')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取月-日
     * @param {Date} ms
     */
    getMDs(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const m = GMT.getMonth() + 1
        const d = GMT.getDate()
        return [m, d].map(this.formatLength).join('-')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取时: 分: 秒
     * @param {Date} ms
     */
    getHMS(ms: UnionDate = this.ms) {
        const GMT = this.unitReturnDate(ms)
        const h = GMT.getHours()
        const m = GMT.getMinutes()
        const s = GMT.getSeconds()
        return [h, m, s].map(this.formatLength).join(':')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年/月/日 时: 分: 秒
     * @param {Date} ms
     */
    getYMDHMS(ms: UnionDate = this.ms) {
        ms = this.unitReturnDate(ms)
        return this.getYMD(ms) + ' ' + this.getHMS(ms)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取年-月-日 时: 分: 秒
     * @param {Date} ms
     */
    getYMDHMSs(ms: UnionDate = this.ms) {
        return this.getYMDs(ms) + ' ' + this.getHMS(ms)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取上个月 格式年-月-日 时: 分: 秒
     * @param {Date} ms
     * @param {number} day 天数
     */

    getLastMonth(ms: UnionDate = this.ms, day = 30) {
        let GMT = this.getTime(this.unitReturnDate(ms))
        GMT = GMT - 3600 * 1000 * 24 * day
        // return this.getYMDs(GMT) + ' ' + this.getHMS(GMT)
        return this.getYMDs(GMT)
    }
    /**
     * @LastEditors: lexy
     * @description: 获取上个季度 格式年-月-日 // 时: 分: 秒
     * @param {Date} ms
     * @param {number} day 天数
     */
    getLastThreeMonth(ms: Date = this.ms, day = 90) {
        let GMT = this.getTime(ms)
        GMT = GMT - 3600 * 1000 * 24 * day
        // return this.getYMDs(GMT) + ' ' + this.getHMS(GMT)
        return this.getYMDs(GMT)
    }
    /**
     * @LastEditors: lexy
     * @description: 年月日加天数
     * @param {Date} ms
     * @param {number} day 天数
     */
    getAddDays(ms: Date | number = this.ms, day = 0) {
        let GMT = this.getTime(this.unitReturnDate(ms))
        GMT = GMT + day * 24 * 60 * 60 * 1000
        const Y = this.getY(GMT)
        const M = this.getM(GMT)
        const D = this.getD(GMT)
        return [Y, M, D].map(this.formatLength).join('-')
    }
    getSubtracteDays(ms: Date | number = this.ms, day = 0) {
        let GMT = this.getTime(this.unitReturnDate(ms))
        GMT = GMT - day * 24 * 60 * 60 * 1000
        const Y = this.getY(GMT)
        const M = this.getM(GMT)
        const D = this.getD(GMT)
        return [Y, M, D].map(this.formatLength).join('-')
    }
    /**
     * @LastEditors: lexy
     * @description: 获取毫秒数
     * @param {Date} ms
     */
    getTime(ms: Date = this.ms) {
        return ms.getTime()
    }

    getObj(ms = this.ms) {
        const GMT = ms
        const Y = GMT.getFullYear()
        const M = GMT.getMonth() + 1
        const D = GMT.getDate()
        const h = GMT.getHours()
        const m = GMT.getMinutes()
        const s = GMT.getSeconds()
        return [Y, M, D, h, m, s].map(this.formatLength)
    }
    /**
     * @LastEditors: lexy
     * @description: 格式化补零
     * @param {Date} ms
     */
    formatLength(ms: number | string) {
        return String(ms)[1] ? String(ms) : '0' + ms
    }
    /**
     * @LastEditors: lexy
     * @description: 统一返回日期类型
     */
    unitReturnDate(ms: Date | number) {
        if (!(ms instanceof Date)) {
            return new Date(ms)
        }
        return ms
    }
    /**
     * @LastEditors: lexy
     * @description: 闰年判断
     * @param {string | number} year
     */
    isLeapYear(year: string | number) {
        const d = new Date(Number(year), 1, 29)
        return d.getDate() === 29
    }
    /**
     * @LastEditors: lexy
     * @description: 判断当月对应天数
     * @param {string} year
     * @param {string} month
     */
    MonthToDay(year: string | number, month: string | number) {
        const d = new Date(Number(year), Number(month), 1, 0, 0, 0)
        const lastDay = new Date(d.getTime() - 1000)
        return lastDay.getDate()
    }
}
/**
 * @LastEditors: lexy
 * @description: 59 分 59 秒 用于计算 秒杀结束时间
 */
export const FIXED_POINT_TIME = 60 * 60 * 1000 - 1000
/**
 * @LastEditors: lexy
 * @description: 时间格式化工具（倒计时转化）
 * @param {number} data
 * @returns {*}
 */
export function toHHmmss(data: number) {
    let time
    let hours = parseInt(((data % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)).toString())
    let minutes = parseInt(((data % (1000 * 60 * 60)) / (1000 * 60)).toString())
    let seconds = parseInt(((data % (1000 * 60)) / 1000).toString())
    time = (hours < 10 ? '0' + hours : hours) + ':' + (minutes < 10 ? '0' + minutes : minutes) + ':' + (seconds < 10 ? '0' + seconds : seconds)
    return time
}
/**
 * @LastEditors: lexy
 * @description: 商品sku规格table
 * @param initSalesVolume 初始销量
 */
export interface ApiGoodSkus {
    id: string
    image: string
    initSalesVolume: number
    limitNum: number
    limitType: keyof typeof Limit
    price: string
    productId: string
    salePrice: string
    shopId: string
    stock: string
    stockType: keyof typeof Stock
    totalStock: string
    weight: number
    specs: string[]
    salesVolume: string
    secKillPrice?: string // 秒杀价格
}
