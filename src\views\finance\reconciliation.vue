<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-15 09:49:18
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-30 20:10:51
-->
<script setup lang="ts">
import { onMounted, reactive, Ref, ref } from 'vue'
import { ElMessage } from 'element-plus'
import DateUtil from '@/utils/date'
import { useRouter } from 'vue-router'
import { doGetFinance } from '@/apis/finance/index'
import PageManage from '@/components/PageManage.vue'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import type { ApiFinanceItem } from '@/views/finance/types'
import { QuestionFilled } from '@element-plus/icons-vue'
import reconciliationDescription from './components/reconciliation-description.vue'
import { doPostExportStatementData } from '@/apis/overview'
import { useRangeLimitedDate } from '@/hooks'
/**
 * @LastEditors: lexy
 * @description: 交易类型
 * @params ORDER_PAID 订单支付(订单详情)
 * @params ORDER_AFS 售后退款(详情)
 * @params DISTRIBUTE 分销佣金
 */
enum TRADESTATUS {
    ORDER_PAID,
    ORDER_AFS,
    DISTRIBUTE,
}
/*
 *variable
 */
const showDescriptionDialog = ref(false)
const $router = useRouter()
const $shopStore = useShopInfoStore()
const { divTenThousand } = useConvert()
const activeTab = ref('')

const checkedData = ref<ApiFinanceItem[]>([])
const tableData = ref<ApiFinanceItem[]>([])
const dateTool = new DateUtil()
/** zrb:初始化为当前自然月日期段 */
const getMonthStartTimestamp = dateTool.getMonthStartTimestamp(new Date())

/** 搜索条件 */
const query = ref({
    transactionType: '',
    changeType: null, //   ""->全部;INCREASE->收入;REDUCE->支出
    startDate: dateTool.getYMDs(getMonthStartTimestamp),
    endDate: dateTool.getYMDs(+new Date()),
    current: 1,
    size: 10,
    shopId: $shopStore.getterShopInfo.id,
})

const total = ref(0)

/** 对账单概况 */
const account = ref({
    income: 0,
    incomeCount: 0,
    payout: 0,
    payoutCount: 0,
})

const createTime: Ref<string | [Date, Date]> = ref([dateTool.getMonthStartDate(getMonthStartTimestamp), new Date()])
let defaultCreateTime: [Date, Date] = [dateTool.getMonthStartDate(getMonthStartTimestamp), new Date()]
// const basicOptions = [
//     { label: '全部', value: '' },
//     { label: '平台服务费(采购)', value: 'PURCHASE_ORDER_SERVICE_CHARGE', type: 'out' },
//     { label: '采购交易', value: 'PURCHASE_ORDER', type: 'in' },
//     { label: '采购运费', value: 'PURCHASE_ORDER_FREIGHT', type: 'in' },
// ]
const basicOptions = [
    { label: '全部', value: null },
    { label: '代销交易', value: 'ORDER_PAID', type: 'in' },
    // { label: '平台优惠券', value: 'PLATFORM_DISCOUNT_COUPON', type: 'in' },
    { label: '会员包邮', value: 'MEMBER_LOGISTICS_DISCOUNT', type: 'in' },
    // { label: '会员折扣', value: 'MEMBER_DISCOUNT', type: 'in' },
    { label: '代销运费', value: 'ORDER_FREIGHT', type: 'in' },
    { label: '平台服务费(代销)', value: 'SYSTEM_SERVICE', type: 'out' },
    // { label: '分销佣金', value: 'DISTRIBUTE', type: 'out' },
    { label: '采购运费', value: 'PURCHASE_ORDER_FREIGHT', type: 'in' },
    { label: '采购交易', value: 'PURCHASE_ORDER', type: 'in' },
    { label: '平台服务费(采购)', value: 'PURCHASE_ORDER_SERVICE_CHARGE', type: 'out' },
    // { label: '退款', value: 'ORDER_AFS' },
]

const options = computed(() => {
    let returnOptions = []
    if (activeTab.value === 'INCREASE') {
        returnOptions = basicOptions.filter((item) => item.type === 'in')
        returnOptions.unshift({ label: '全部', value: '', type: 'in' })
    } else if (activeTab.value === 'REDUCE') {
        returnOptions = basicOptions.filter((item) => item.type === 'out')
        returnOptions.unshift({ label: '全部', value: '', type: 'out' })
    } else {
        returnOptions = basicOptions
    }
    return returnOptions
})

/*
 *lifeCircle
 */

onMounted(() => {
    init()
})

/*
 *function
 */

/**
 * 初始化
 */
function init() {
    query.value.current = 1
    // getFinanceStatement()
    // getFinanceList()
    initReconciliation()
}

/**
 * 获取对账单概况
 */
// const getFinanceStatement = () => {
//     doGetFinanceStatement(query.value).then((res) => {
//         account.value = res.data
//     })
// }

/**
 * 获取对账单列表
 */
// const getFinanceList = () => {
//     doGetFinanceList(query.value).then((res) => {
//         tableData.value = res.data.records
//         total.value = res.data.total
//     })
// }

/**
 * 切换顶部状态
 */
const clickTab = () => {
    Object.assign(query.value, {
        changeType: activeTab.value || null,
        startDate: dateTool.getYMDs(getMonthStartTimestamp),
        endDate: dateTool.getYMDs(+new Date()),
        transactionType: null,
        current: 1,
    })
    createTime.value = [dateTool.getMonthStartDate(getMonthStartTimestamp), new Date()]
    initReconciliation()
}

/**
 * 下拉框选择
 */
const selectStatus = () => {
    query.value.current = 1
    initReconciliation()
}

// 设置30天范围限制，同时禁用未来日期
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(90, true, true)

/**
 * 选择时间
 */
const chooseTime = (Date: [Date, Date]) => {
    query.value.startDate = Date ? dateTool.getYMDs(Date[0]) : ''
    query.value.endDate = Date ? dateTool.getYMDs(Date[1]) : ''
    init()
}
/**
 * 选择时间
 */
const Searchlist = () => {
    query.value.current = 1
    initReconciliation()
}

/**
 * @method handleSizeChange
 * @description 每页 条
 */
const handleSizeChange = (val: number) => {
    query.value.current = 1
    query.value.size = val
    initReconciliation()
}

/**
 * @method handleCurrentChange
 * @description 当前页
 */
const handleCurrentChange = (val: number) => {
    query.value.current = val
    initReconciliation()
}
/**
 * @LastEditors: lexy
 * @description: 状态转换
 */
const convertStatus = (status: keyof typeof TRADESTATUS) => {
    const currentOptions = basicOptions.filter((item) => item.value === status)
    if (currentOptions.length) {
        return currentOptions[0].label
    }
    return null
}
/**
 * 不同类型跳转详情 tradeNo
 */
const handleJump = (row: ApiFinanceItem) => {
    const { orderNo } = row
    const navMap = {
        ORDER_PAID: {
            path: '/order/details',
            params: {
                orderNo,
            },
        },
        ORDER_AFS: {
            path: '/afs/detail',
            params: {
                afsNo: row.tradeDetail.afsNo || '',
                packageId: row.tradeDetail.packageId || '',
                orderNo,
            },
        },
        DISTRIBUTE: {
            path: '/order/details',
            params: {
                orderNo,
            },
        },
    } as { [x: string]: { path: string; params: any } }
    const currentNavLink = navMap[row.tradeType]
    $router.push({
        path: currentNavLink.path,
        query: currentNavLink.params,
    })
}
async function initReconciliation() {
    const { current, size, changeType, startDate, endDate, transactionType } = query.value
    let tempObj = {
        page: {
            current,
            size,
        },
        changeType: changeType || null,
        startDate: startDate || null,
        endDate: endDate || null,
        transactionType: transactionType || null,
    }
    const { code, data } = await doGetFinance(tempObj)
    if (code === 200) {
        account.value = data.statistics
        tableData.value = data.records
        total.value = data.total
    } else {
        ElMessage.error('获取数据失败')
    }
}

const handleSelectionChange = (selectionData: ApiFinanceItem[]) => {
    checkedData.value = selectionData
}
const handleExport = async () => {
    let params: any = {}
    if (checkedData.value?.length) {
        params.exportIds = checkedData.value?.map((item) => item.id) || []
    } else {
        const { changeType, startDate, endDate } = query.value
        params = { changeType, startDate, endDate }
    }
    const { code, msg } = await doPostExportStatementData(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}
</script>

<template>
    <div class="finance">
        <div class="finance__header">
            <div class="finance__header--income">
                收入（{{ account.incomeCount }}笔）
                <br />
                <span style="color: #4bb1a6; font-size: 18px">+ {{ account.income && divTenThousand(account.income) }}元</span>
            </div>
            <div class="finance__header--expenses">
                支出（{{ account.payoutCount }}笔）
                <br />
                <span style="color: #ff0000; font-size: 18px">-{{ account.payout && divTenThousand(account.payout) }}元</span>
            </div>
        </div>
        <div class="finance__tabs">
            <el-tabs v-model="activeTab" @tab-change="clickTab">
                <el-tab-pane label="全部" name=""></el-tab-pane>
                <el-tab-pane label="收入" name="INCREASE"></el-tab-pane>
                <el-tab-pane label="支出" name="REDUCE"></el-tab-pane>
            </el-tabs>
            <div class="finance__tabs--export">
                <el-button size="small" type="primary" @click="handleExport">导出</el-button>
                <el-icon class="export-icon" @click="showDescriptionDialog = true"><question-filled /></el-icon>
            </div>
        </div>
        <div style="display: flex; align-content: center; justify-content: space-between; margin-bottom: 10px">
            <div>
                <el-date-picker
                    v-model="createTime"
                    style="margin-right: 15px; width: 230px"
                    type="daterange"
                    range-separator="-"
                    :default-value="defaultCreateTime"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled-date="disabledDate"
                    @calendar-change="handleCalendarChange"
                    @visible-change="handleVisibleChange"
                    @change="chooseTime"
                ></el-date-picker>
            </div>
            <el-select v-model="query.transactionType" placeholder="请选择" style="width: 210px" @change="selectStatus">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
        </div>
        <el-table
            empty-text="暂无数据~"
            :data="tableData"
            style="width: 100%"
            :header-cell-style="{
                'background-color': '#F6F8FA',
                'font-weight': 'normal',
                color: '#515151',
            }"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" fixed="left" />
            <el-table-column label="订单号" width="210px">
                <template #default="{ row }">
                    <div>{{ row.orderNo }}</div>
                </template>
            </el-table-column>
            <el-table-column label="交易流水号" width="210px">
                <template #default="{ row }">
                    <div>{{ row.tradeNo }}</div>
                </template>
            </el-table-column>
            <!-- <el-table-column label="对方信息" width="120px">
                <template #default="{ row }">
                    <div style="display: flex; align-items: center">
                        <img :src="row.userAvatar" style="width: 50px; height: 50px; margin-right: 7px" />
                        <div>{{ row.userNickname }}</div>
                    </div>
                </template>
            </el-table-column> -->
            <!-- <el-table-column label="店铺名称" width="150px">
                <template #default="{ row }">
                    <div>{{ row.shopName }}</div>
                </template>
            </el-table-column> -->
            <el-table-column label="交易类型" width="160px">
                <template #default="{ row }">
                    <template v-if="row">
                        <div>{{ convertStatus(row.tradeType) }}</div>
                    </template>
                </template>
            </el-table-column>
            <el-table-column label="收支金额（元）" width="160px">
                <template #default="{ row }">
                    <div>{{ row.changeType === 'INCREASE' ? '+' : '-' }}{{ row.amount && divTenThousand(row.amount) }}</div>
                </template>
            </el-table-column>
            <!-- <el-table-column label="商品来源" width="110px">
                <template #default="{ row }">
                    <div>{{ row.changeType === 'INCREASE' ? '+' : '-' }}{{ row.amount && divTenThousand(row.amount) }}</div>
                </template>
            </el-table-column> -->
            <el-table-column label="交易时间">
                <template #default="{ row }">
                    <div>{{ row.tradeTime }}</div>
                </template>
            </el-table-column>
            <!-- 操作按钮暂定隐藏  操作逻辑后面有变动 -->
            <!-- <el-table-column label="操作" width="60px" fixed="right">
                <template #default="{ row }">
                    <div style="color: #2e99f3; cursor: pointer" @click="handleJump(row)">详情</div>
                </template>
            </el-table-column> -->
        </el-table>
        <page-manage :page-size="query.size" :page-num="query.current" :total="total" @handle-size-change="handleSizeChange" @handle-current-change="handleCurrentChange" />
    </div>
    <el-dialog v-model="showDescriptionDialog" title="供应商对账说明" :width="800">
        <reconciliation-description />
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(finance) {
    padding-left: 27px;
    padding-right: 27px;

    @include e(header) {
        display: flex;
        align-items: center;
        background: #f9f9f9;
        height: 77px;
        margin: auto;
        font-size: 14px;
        margin-bottom: 29px;

        @include m(income) {
            flex: 1;
            margin-left: 38px;
        }

        @include m(expenses) {
            flex: 1;
        }
    }

    @include e(form) {
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-content: center;
    }
    @include e(tabs) {
        position: relative;
        @include m(export) {
            position: absolute;
            top: 10px;
            right: 0;
            @include flex();
            @include b(export-icon) {
                font-size: 22px;
                margin-left: 10px;
            }
        }
    }
}
</style>
