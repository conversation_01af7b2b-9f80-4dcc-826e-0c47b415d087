<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-12 22:18:03
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-08 16:13:47
-->
<template>
    <el-row justify="space-between">
        <el-col :span="19" style="text-align: left">
            <el-breadcrumb separator="/" class="m__breadcrumb">
                <el-breadcrumb-item
                    v-for="(item, i) of breadcrumb"
                    :key="i"
                    :class="['m__breadcrumb--item', { 'm__breadcrumb--last': i !== breadcrumb.length - 1 }]"
                    :to="i !== breadcrumb.length - 1 && i !== 0 ? { path: item.path } : null"
                >
                    {{ item.title }}
                </el-breadcrumb-item>
            </el-breadcrumb>
        </el-col>
        <el-col :span="5" style="text-align: right">
            <MMessage />
        </el-col>
    </el-row>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import MMessage from './message/MMessage.vue'
const route = useRoute()
const breadcrumb = computed(() => {
    return route.matched
        .filter((route) => !!route.meta.title)
        .map((route) => {
            return {
                title: route.meta.title,
                path: route.path,
            }
        })
})
// const props = defineProps({})
</script>
