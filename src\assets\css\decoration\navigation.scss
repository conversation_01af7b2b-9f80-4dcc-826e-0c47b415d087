// 店铺导航

@import "../mixins/mixins";
@import "../mixins/utils.scss";

@include b(storeNavigation-item) {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  // overflow-x: auto;
  padding: 10px 0;

  & > li {
    flex: none;
    width: 25%;
    text-align: center;
    margin-bottom: 5px;
    margin-top: 5px;
  }

  @include e(img) {
    width: 50px;
    height: 50px;
    display: inline-block;
  }
}

@include b(storeNavigation-item-form) {
  position: relative;
  border: 1px solid #eeeeee;
  padding: 10px;
  margin-bottom: 10px;

  @include b(avatar) {
    width: 48px;
    height: 48px;
    display: block;
  }

  @include b(avatar-uploader) {
    @include e(icon) {
      font-size: 28px;
      color: #8c939d;
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }

    @include b(el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #409eff;
      }
    }
  }
}
