<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-09-20 16:38:36
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-13 18:37:04
-->
<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
/*
 *variable
 */
const activeName = ref('business')
const asyncComponents = {
    business: defineAsyncComponent(() => import('./components/business.vue')),
    changeShop: defineAsyncComponent(() => import('./components/changeShop.vue')),
}
/*
 *lifeCircle
 */
/*
 *function
 */
const handleClick = () => {}
</script>

<template>
    <el-tabs v-model="activeName" @tab-change="handleClick">
        <el-tab-pane label="账户管理" name="business" />
        <el-tab-pane label="切换供应商" name="changeShop" />
        <component :is="asyncComponents[activeName]"></component>
    </el-tabs>
</template>

<style scoped lang="scss"></style>
