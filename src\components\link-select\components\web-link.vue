<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-04 19:04:16
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-09 19:10:47
-->
<script setup lang="ts">
import { PropType, ref, watch, onMounted } from 'vue'
import { useVModel } from '@vueuse/core'
import type { LinkSelectItem } from '../linkSelectItem'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const webview = ref('')
watch(webview, (newVal) => {
    const webview = newVal.replace(/https:\/\//, '')
    const currentItem = {
        id: 999,
        type: 6,
        name: '自定义链接',
        url: `https://${webview}`,
        append: '',
    }
    Object.assign(linkSelectItem.value, currentItem)
})
/*
 *lifeCircle
 */
onMounted(() => {
    if (linkSelectItem.value.type === 6) {
        webview.value = linkSelectItem.value.url
    }
})
/*
 *function
 */
</script>

<template>
    <div>
        <el-input v-model="webview" placeholder="请输入内容" class="input-with-select" maxlength="40">
            <template #prepend>https://</template>
        </el-input>
        <div class="link-tips">
            注意事项 : 选择此项需要https协议的链接，其它网页需登录
            <a href="https://mp.weixin.qq.com/" target="_tartget" rel="noopener noreferrer">
                小程序管理后台
                <span></span> </a
            >配置业务域名。
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
