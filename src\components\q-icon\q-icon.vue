<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-14 18:25:47
 * @LastEditors: lexy
 * @LastEditTime: 2022-06-15 11:05:49
-->
<script setup lang="ts">
import { defineEmits } from 'vue'
/*
 *variable
 */
/**
 * @LastEditors: lexy
 * @description: micon 属性
 * @property {String} name font-class
 * @property {String} size 尺寸默认为16px 需传默认单位
 * @property {String} color icon颜色
 */
const $props = defineProps({
    name: {
        type: String,
        default: '',
    },
    size: {
        type: String,
        default: '16px',
    },
    color: {
        type: String,
        default: '',
    },
})
const $emit = defineEmits(['click'])
/*
 *lifeCircle
 */
/*
 *function
 */
const clickHandler = () => {
    $emit('click')
}
</script>

<template>
    <span :class="'iconfont ' + [$props.name]" :style="'font-size:' + $props.size + '; color:' + $props.color" @click="clickHandler"></span>
</template>
