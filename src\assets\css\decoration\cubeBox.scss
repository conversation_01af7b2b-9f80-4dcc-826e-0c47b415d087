/*预览显示*/

.rc-design-component-default-preview {
    &__text {
        line-height: 136px;
        font-size: 14px;
        background-color: #ebf8fd;
        text-align: center;
        color: #88c4dc;
    }
}


/*控件*/

.zent-design-editor__control-group {
    &-container {
        @include flex(flex-start,flex-start);
    }
    &-help-desc {
        margin-top: 10px;
        color: #999;
        font-size: 12px;
    }
    &-label {
        width: 40px;
        font-size: 12px;
        -ms-flex-negative: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        text-align: right;
        margin-right: 10px;
    }
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    .rc-design-select-templates {
        width: 100px;
        height: 100px;
        display: inline-block;
        border: 1px solid #e5e5e5;
        margin: 0 10px 15px 0;
        padding-top: 5px;
        background-color: #fff;
        text-align: center;
        box-sizing: content-box;
        cursor: pointer;
        &__image-block {
            width: 100px;
            height: 64px;
            & > img{
                width: 100%;
                height: 100%;
            }
        }
        &__title {
            font-size: 12px;
            margin-top: 10px;
        }
        &.active {
            border-color: #38f;
        }
    }
    &-control {
        webkit-box-flex: 1;
        -ms-flex-positive: 1;
        -webkit-flex-grow: 1;
        -moz-box-flex: 1;
        flex-grow: 1;
        .rc-design-component-cube {
            position: relative;
            width: 325px;
            &.clearfix {
                zoom: 1;
                &:after {
                    content: "";
                    display: table;
                    clear: both;
                }
            }
            .cube-row {
                float: left;
                list-style: none;
                padding: 0;
                margin: 0;
                .cube-item {
                    background: #f8f8f8;
                    border-left: 1px solid #e5e5e5;
                    border-bottom: 1px solid #e5e5e5;
                    cursor: pointer;
                    text-align: center;
                    .plus-icon {
                        font-size: 20px;
                        color: #bbb;
                    }
                    &:first-child {
                        border-top: 1px solid #e5e5e5;
                    }
                    &.item-selected {
                        background: #e8f7fd;
                        border-color: #e5e5e5;
                    }
                }
                &:last-of-type {
                    .cube-item {
                        border-right: 1px solid #e5e5e5;
                    }
                }
            }
            .cube-selected {
                position: absolute;
                background-color: #e8f7fd;
                border: 1px solid #bdf;
                text-align: center;
                color: #88c4dc;
                cursor: pointer;
                &-text {
                    width: 100%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    -webkit-transform: translateX(-50%) translateY(-50%);
                    -moz-transform: translateX(-50%) translateY(-50%);
                    -ms-transform: translateX(-50%) translateY(-50%);
                    transform: translateX(-50%) translateY(-50%);
                    font-size: 12px;
                }
                &.cube-selected-click {
                    border: 1px solid #38f;
                    z-index: 2;
                    cursor: auto;
                }
            }
        }
    }
}

.zent-slider {
    margin-left: 20px;
}

.rc-design-editor-card-item {
    position: relative;
    background-color: #fff;
    margin: 15px 0;
    padding: 15px;
    border: 1px dashed #e5e5e5;
}

.rc-design-component-editor_subentry-item {
    position: relative;
}

.rc-design-common-choice-image-component {
    display: inline-block;
    .image-editor {
        float: left;
    }
}

.has-not-choose-image {
    position: relative;
    width: 80px;
    height: 80px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    border: 1px dashed #ddd;
    color: #38f;
    cursor: pointer;
    overflow: hidden;
    font-size: 12px;
    .plus-icon {
        font-size: 20px;
        line-height: 48px;
    }
}

.rc-design-component-editor_subentry-item .subentry-item-editor-form-content {
    float: left;
    width: 350px;
    margin-left: 10px;
}

.rc-design-component-editor_subentry-item .image-editor {
    float: left;
}

.clearfix {
    zoom: 1;
}

.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

.rc-choose-link-menu-trigger {
    font-size: 12px;
}

.flex {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.rc-choose-link-menu {
    color: #38f;
    font-size: 12px;
    cursor: pointer;
}

.rc-design-common-choice-image-component .has-choosed-image .thumb-image {
    min-height: 80px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: bottom;
    max-height: 100%;
    max-width: 100%;
    height: auto;
    width: auto;
}

.rc-design-common-choice-image-component .modify-image {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.rc-design-component-cube .cube-selected img {
    height: inherit;
    vertical-align: baseline;
    object-fit: cover;
}

.rc-design-editor-card-item-delete {
    position: absolute;
    cursor: pointer;
    font-size: 20px;
    right: -10px;
    top: -10px;
    color: #bbb;
    background: #fff;
    border-radius: 50%;
    z-index: 10;
}


/*预览*/

.rc-design-vue-preview {
    .cap-cube-wrap {
        width: 100%;
        overflow: hidden;
    }
}
.cap-cube{
    position: relative;
}
.cap-cube__item {
    position: absolute;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50%;
    overflow: hidden;
}

.cap-cube__table-image--invisible {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.iconshangpinxiangqing-baozhuang-2 {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 40px;
    z-index: 999;
    color: #fff;
    transform: translate(-50%, -50%);
}