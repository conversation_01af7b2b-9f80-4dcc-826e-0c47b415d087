/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-16 14:14:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-04-06 21:15:23
 */
export interface ShopInfoStore {
    id: string
    logo: string
    name: string
    newTips: string
    status: string
    token?: string
    userId: string
    shopId: string
    shopType?: string
    mobile?: string
}
export default {
    shopInfo: {
        id: '',
        logo: '',
        name: '',
        newTips: '',
        status: '',
        token: '',
        userId: '',
        nickname: '',
        refresh_token: '',
        shopId: '',
        shopType: '',
        mobile: '',
    },
}
