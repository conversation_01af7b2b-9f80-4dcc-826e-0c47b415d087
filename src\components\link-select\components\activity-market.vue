<!--
 * @description: 链接选择器-活动营销
 * @Author: lexy
 * @Date: 2022-08-04 18:03:05
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-24 11:25:48
-->
<script setup lang="ts">
import { PropType, ref, onMounted, defineExpose } from 'vue'
import { useVModel } from '@vueuse/core'
import { typeNameMap } from '../linkSelectItem'
import type { LinkSelectItem } from '../linkSelectItem'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
    visible: {
        type: Boolean,
        default: false,
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const selectId = ref('')
const tableData: LinkSelectItem[] = [
    // {
    //     id: 3,
    //     type: 4,
    //     name: '领券中心',
    //     url: '/pages/coupon/couponCenter/couponCenter',
    //     append: '',
    // },
    // {
    //     id: 4,
    //     type: 4,
    //     name: '我的优惠券',
    //     url: '/pages/coupon/coupon',
    //     append: '',
    // },
    // {
    //     id: 11,
    //     type: 4,
    //     name: '会员中心',
    //     url: '/pages/member/member',
    //     append: '',
    // },
    // {
    //     id: 12,
    //     type: 4,
    //     name: '直播营销',
    //     url: '/pages/live/live',
    //     append: 'live',
    // },
    // {
    //     id: 13,
    //     type: 4,
    //     name: '社群接龙',
    //     url: '/pages/chainDetail/chainDetail',
    //     append: 'chain',
    // },
    // {
    //     id: 14,
    //     type: 4,
    //     name: '回收返券',
    //     url: '/pages/recycling/recycling',
    //     append: '',
    // },
    // {
    //     id: 15,
    //     type: 0,
    //     name: '分销中心',
    //     url: '/pages/distributionCenter/distributionCenter',
    //     append: '',
    // },
]
watch(
    linkSelectItem,
    (newVal) => {
        selectId.value = newVal.id
    },
    {
        immediate: true,
    },
)
defineExpose({
    selectId,
})
/*
 *lifeCircle
 */
/*
 *function
 */
const handleSelect = () => {
    const currentItem = tableData.find((item) => item.id === selectId.value)
    Object.assign(linkSelectItem.value, currentItem)
}
</script>

<template>
    <el-table :data="tableData" height="369">
        <el-table-column label="页面名称" prop="name"></el-table-column>
        <el-table-column label="操作" width="100px">
            <template #default="scope">
                <el-radio v-model="selectId" :value="scope.row.id" @change="handleSelect">
                    <span></span>
                </el-radio>
            </template>
        </el-table-column>
    </el-table>
</template>

<style lang="scss" scoped></style>
