/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-13 00:25:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 17:42:48
 */
import { REGEX } from '@/constant'
/**
 * @LastEditors: lexy
 * @description: 日期
 */
const REGEX_DATE = (str: string) => REGEX.DATE.test(str)
/**
 * @LastEditors: lexy
 * @description: 时间
 */
const REGEX_TIME = (str: string) => REGEX.TIME.test(str)
/**
 * @LastEditors: lexy
 * @description: 日期 + 时间
 */
const REGEX_TIME_DATE = (str: string) => REGEX.TIME_DATE.test(str)
/**
 * @LastEditors: lexy
 * @description: 图片
 */
const REGEX_HTTP_URL = (str: string) => REGEX.HTTP_URL.test(str)
/**
 * @LastEditors: lexy
 * @description: 数字
 */
const REXGEX_NUMBERS = (str: string) => REGEX.NUMBERS.test(str)
/**
 * @LastEditors: lexy
 * @description: 文本
 */
const REGEX_BLANK = (str: string) => REGEX.BLANK.test(str)
/**
 * @LastEditors: lexy
 * @description: 手机号
 */
const REGEX_MOBILE = (str: string) => REGEX.MOBILE.test(str)
/**
 * @LastEditors: lexy
 * @description: 公民身份证
 */
const REGEX_CITIZEN_ID = (str: string) => REGEX.CITIZEN_ID.test(str)
/**
 * @LastEditors: lexy
 * @description: 邮箱
 */
const REGEX_EMAIL = (str: string) => REGEX.EMAIL.test(str)
/**
 * @LastEditors: lexy
 * @description: 密码校验，6-20位密码，至少使用字母、数字、符号中的2种组合
 */
const REGEX_PASSWORD = (str: string) => REGEX.PASSWORD.test(str)
export { REGEX_DATE, REGEX_TIME, REGEX_TIME_DATE, REGEX_HTTP_URL, REXGEX_NUMBERS, REGEX_BLANK, REGEX_MOBILE, REGEX_CITIZEN_ID, REGEX_EMAIL, REGEX_PASSWORD }
