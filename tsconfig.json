{
    "compilerOptions": {
        "types": ["node", "vite/client", "vitest/globals"],
        "target": "esnext",
        "useDefineForClassFields": true,
        "suppressImplicitAnyIndexErrors": true, //新增这一行
        "module": "esnext",
        "moduleResolution": "node",
        "strict": true,
        "jsx": "preserve",
        "sourceMap": true,
        "resolveJsonModule": true,
        "esModuleInterop": true,
        "strictNullChecks": true,
        "noImplicitAny": true,
        "noImplicitThis": true,
        "lib": ["esnext", "dom"],
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/*"],
            "@components/*": ["src/components/*"],
            "@views/*": ["src/views/*"],
            "@apis/*": ["src/apis/*"],
            "@utils/*": ["src/utils/*"],
            "@hooks/*": ["src/hooks/*"],
            "#/*": ["./*"]
        },
        "typeRoots": ["./node_modules/@types/", "types"]
    },
    "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "../gruul-mall-view-shop/src/utils/downloadOSS.ts", "../gruul-mall-view-shop/src/utils/downloadOSS.ts"],
    "exclude": ["node_modules"]
}
