<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-14 15:03:15
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 09:12:08
-->
<template>
    <el-container class="customer-service">
        <el-aside width="200px">
            <aside-index :message-users="messageUsersPage.records" @change="onChange" @keyword-change="onKeywordsChange" @search-focus="onSearchFocus" />
        </el-aside>
        <el-main>
            <main-index
                :shop-info="shopInfo"
                :user="currentSelectUser"
                :messages="adminMessagesPage.records"
                :search-focus="searchFocus"
                @message-submit="messageSubmit"
                @load-more="contentLoadMore"
            />
        </el-main>
    </el-container>
</template>

<script setup lang="ts">
import AsideIndex from './components/aside/Index.vue'
import MainIndex from './components/main/Index.vue'
import { shopInfo, messageUsersPage, currentSelectUser, searchFocus, onChange, onKeywordsChange, onSearchFocus, adminMessagesPage, messageSubmit, initCustomerService, contentLoadMore } from './index'
import { onMounted } from 'vue'
onMounted(initCustomerService)
</script>

<style scoped lang="scss">
.customer-service.el-container {
    border: 1px solid var(--el-border-color);
    border-radius: $rows-border-radius-lg;
    width: 100%;
    min-height: 700px;
    overflow: hidden;
}
.el-aside {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-right: 1px solid var(--el-border-color);
    min-height: 600px;
    background-color: white;
}
.el-main {
    width: 600px;
    padding: 0;
}
</style>
