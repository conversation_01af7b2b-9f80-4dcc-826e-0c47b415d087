/*
 * @Author: lexy
 * @Date: 2024-05-10 11:25:42
 * @LastEditors: lexy
 * @LastEditTime: 2024-05-17 15:13:46
 * @FilePath: \webSupplier\src\components\layout\layout.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export enum MenuType {
    CATALOG = 'CATALOG',
    MENU = 'MENU',
}

export interface Menu {
    id: string
    name: string
    icon: string
    type: MenuType
    path: string
    children?: Array<Menu>
    component?: string
}
