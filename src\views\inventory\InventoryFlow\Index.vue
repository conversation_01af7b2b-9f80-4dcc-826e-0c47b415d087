<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-15 09:49:18
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-03 15:31:43
-->
<script setup lang="ts">
import { reactive, Ref, ref } from 'vue'
import search, { SearchType } from './Search.vue'
import { ElMessage } from 'element-plus'
import PageManage from '@/components/pageManage/PageManage.vue'
import { doGetStorageFlow } from '@/apis/inventory'
import { QuestionFilled } from '@element-plus/icons-vue'
import useClipboard from 'vue-clipboard3'

/*
 *variable
 */
const { toClipboard } = useClipboard()
const tableList = reactive({
    page: { size: 10, current: 1 },
    list: [],
    total: 0,
})
const currentTab = ref<'' | 'WAIT_COMPLETION' | 'COMPLETION' | 'CANCEL'>('')
const searchParams = ref({
    productName: '',
    productId: '',
    Id: '',
    stockChangeType: '',
    sellType: '',
    orderNo: '',
    date: '',
    startTime: '',
    endTime: '',
    isOutbound: '' as string | boolean,
})
const stockChangeTypelist = {
    PUBLISHED_INBOUND: '发布入库',
    EDITED_INBOUND: '编辑入库',
    OVERAGE_INBOUND: '盘盈入库',
    RETURNED_INBOUND: '退货入库',
    ORDER_CANCELLED_INBOUND: '订单取消入库',
    ALLOCATION_INBOUND: '调拨入库',
    OTHER_INBOUND: '其它入库',
    SOLD_OUTBOUND: '销售出库',
    EDITED_OUTBOUND: '编辑出库',
    SHORTAGE_OUTBOUND: '盘亏出库',
    ALLOCATION_OUTBOUND: '调拨出库',
    OTHER_OUTBOUND: '其它出库',
}
const sellTypeList = {
    PURCHASE: '采购商品',
    CONSIGNMENT: '代销商品',
}
const illustrateType = ref(false)
/*
 *lifeCircle
 */

/*
 *function
 */
const initList = async () => {
    const { data } = await doGetStorageFlow({
        ...searchParams.value,
        ...tableList.page,
    })
    tableList.total = +data.total
    tableList.list = data.records
}
//tab点击事件
const handleTabClick = (status: '' | true | false) => {
    searchParams.value.isOutbound = status
    initList()
}
const getSearch = (e: SearchType) => {
    tableList.page.current = 1
    if (searchParams.value.date.length > 0) {
        searchParams.value.startTime = searchParams.value.date[0]
        searchParams.value.endTime = searchParams.value.date[1]
    }
    searchParams.value = { ...searchParams.value, ...e }
    initList()
}
const copyOrderNo = async (data: string) => {
    try {
        await toClipboard(data)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}
</script>

<template>
    <search @on-search-params="getSearch" />
    <div style="display: flex; align-items: center; justify-content: space-between; padding-right: 20px">
        <el-tabs v-model="currentTab" @tab-change="handleTabClick">
            <el-tab-pane label="全部" name=""></el-tab-pane>
            <el-tab-pane label="入库明细" name="false"></el-tab-pane>
            <el-tab-pane label="出库明细" name="true"></el-tab-pane>
        </el-tabs>

        <el-icon :size="20" style="cursor: pointer" @click="illustrateType = true"><QuestionFilled /></el-icon>
    </div>
    <el-table
        empty-text="暂无数据~"
        :data="tableList.list"
        style="width: 100%"
        :header-cell-style="{
            'background-color': '#F6F8FA',
            'font-weight': 'normal',
            color: '#515151',
        }"
    >
        <el-table-column label="流水号" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.id }}</div>
                <div>
                    <el-tooltip class="box-item" :content="row.skuId" placement="top">
                        <span style="margin-right: 30px; color: #0f40f5; cursor: pointer" @click="copyOrderNo(row.skuId)"> SKUID</span>
                    </el-tooltip>
                    <el-tooltip class="box-item" :content="row.productId" placement="top">
                        <span style="color: #0f40f5; cursor: pointer" @click="copyOrderNo(row.productId)"> SPUID</span>
                    </el-tooltip>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="商品" width="300">
            <template #default="{ row }">
                <div class="ellipsis">{{ row.productName }}</div>
                <div>{{ row.specName?.join(',') }}</div>
            </template>
        </el-table-column>
        <!-- <el-table-column label="商品规格" width="150">
            <template #default="{ row }">
                <div>{{ row.specName?.join(',') }}</div>
            </template>
        </el-table-column> -->
        <el-table-column label="出入库数" align="center">
            <template #default="{ row }">
                <div>{{ row.stockChangeNum }}</div>
            </template>
        </el-table-column>
        <el-table-column label="类型" align="center">
            <template #default="{ row }">
                <div>{{ stockChangeTypelist[row.stockChangeType] }}</div>
            </template>
        </el-table-column>
        <el-table-column label="销售方式" align="center">
            <template #default="{ row }">
                <div>{{ sellTypeList[row.sellType] }}</div>
            </template>
        </el-table-column>
        <el-table-column label="关联订单号" width="190">
            <template #default="{ row }">
                <div>{{ row.orderNo }}</div>
            </template>
        </el-table-column>
        <el-table-column label="变更时间" width="160" align="center">
            <template #default="{ row }">
                <div>{{ row.updateTime }}</div>
            </template>
        </el-table-column>
    </el-table>
    <PageManage v-model="tableList.page" load-init :total="tableList.total" @reload="initList" />
    <el-dialog v-model="illustrateType" title="库存明细说明" width="60%" center>
        <el-image src="https://bgniao-small-file-1253272780.cos.ap-chengdu.myqcloud.com/b2b2c-develop/%E4%BE%9B%E5%BA%94%E5%95%86%E7%AB%AF.png"> </el-image>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(ellipsis) {
    @include utils-ellipsis(2);
    font-weight: 600;
}
</style>
