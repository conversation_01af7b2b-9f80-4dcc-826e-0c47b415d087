/*
 * @description:
 * @Author: lexy
 * @Date: 2023-07-27 18:41:47
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-02 16:08:48
 */
import { get, post, put, del, patch } from '../http'

/**
 * @LastEditors: lexy
 * @description: 获取仓储管理订单列表
 * @param {any} params
 */
export const doGetStorageList = (params: any) => {
    return get({
        url: 'gruul-mall-storage/storage/management/order/page',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 仓储管理订单创建
 * @param {any} data
 */
export const doPostStorageOrderCreate = (data: any) => {
    return post({
        url: 'gruul-mall-storage/storage/management/order/create',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 仓储管理订单修改
 * @param {any} data
 */
export const doPostStorageOrderedit = (data: any) => {
    return post({
        url: 'gruul-mall-storage/storage/management/order/edit',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 仓储管理订单取消
 * @param {any} id
 */
export const doPutStorageOrdercancel = (id: string) => {
    return put({
        url: `gruul-mall-storage/storage/management/order/cancel/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 仓储管理订单完成
 * @param {any} id
 */
export const doPutStorageOrdercomplete = (id: string) => {
    return put({
        url: `gruul-mall-storage/storage/management/order/complete/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取仓储管理订单详情
 * @param {any} id
 */
export const doGetStorageOrderDetail = (id: string) => {
    return get({
        url: `gruul-mall-storage/storage/management/order/get/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取库存明细
 * @param {any} id
 */
export const doGetStorageFlow = (params) => {
    return get({
        url: `gruul-mall-storage/storage/detail/page`,
        params,
    })
}
