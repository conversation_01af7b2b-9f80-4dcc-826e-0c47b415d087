<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Ref } from 'vue'
import AddCategory from './AddCategory.vue'
import { doDelShopRelationCategory, doGetCurrentShopRelationCategory, doPostSaveShopRelationCategory } from '@/apis/store/index'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { getsettings } from '@/apis'

const multipleSelection = ref<any[]>([])
const tableData: Ref<any[]> = ref([])
const showAddDialog = ref(false)
const checkedSubCategoryIds = computed(() => tableData.value.map((item) => item.currentCategoryId))
const selectionSubList: Ref<any[]> = ref([])
const typeOfCommissionOptions = reactive({
    extractionType: '',
    drawPercentage: '',
})

const selectionChange = (selectionList: any[]) => {
    selectionSubList.value = selectionList
}
const handleRemoveClick = (id: string) => {
    ElMessageBox.confirm('请确认是否移出？', '请确认')
        .then(async () => {
            const { success, msg } = await doDelShopRelationCategory([id])
            if (success) {
                ElMessage.success({ message: msg || '移出成功' })
                getCurrentShopRelationCategory()
            } else {
                ElMessage.error({ message: msg || '移出失败' })
            }
        })
        .catch(() => {})
}

const getCurrentShopRelationCategory = async () => {
    getsettings({}).then((res) => {
        if (res?.data?.extractionType) {
            typeOfCommissionOptions.extractionType = res?.data?.extractionType
            typeOfCommissionOptions.drawPercentage = res?.data?.drawPercentage
        }
    })
    const shopStore = useShopInfoStore()
    const res = await doGetCurrentShopRelationCategory({ shopId: shopStore.shopInfo.id })
    tableData.value = res?.data
}
const handleSave = async (requestData: any[] = []) => {
    const { success, msg } = await doPostSaveShopRelationCategory(requestData.map((item) => item.currentCategoryId))
    if (success) {
        ElMessage.success({ message: msg || '保存成功' })
        getCurrentShopRelationCategory()
    } else {
        ElMessage.error({ message: msg })
    }
}
const confirmAddCategory = () => {
    const requestData = [...tableData.value, ...selectionSubList.value]
    showAddDialog.value = false
    if (requestData.length) handleSave(requestData)
}
/**
 * 勾选类目回调事件
 * @param val
 */
const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val
}
/**
 * 批量移除类目
 */
const handleMultiRemoveClick = () => {
    ElMessageBox.confirm('确认移出选择的类目?', '🔔提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        if (multipleSelection.value?.length) {
            const { success, msg } = await doDelShopRelationCategory([...multipleSelection.value.map(({ id }) => id)])
            if (success) {
                ElMessage.success({ message: msg || '移出成功' })
                getCurrentShopRelationCategory()
            } else {
                ElMessage.error({ message: msg || '移出失败' })
            }
        }
    })
}

onMounted(() => getCurrentShopRelationCategory())

/**
 * @: 渲染类目
 */
const renderRatio = (row: any) => {
    const ratio = row.customDeductionRatio ? row.customDeductionRatio : row.supplierDeductionRatio
    return ratio ? ratio + '%' : '-'
}
</script>
<template>
    <div class="category">
        <div class="category__title">平台服务费</div>
        <div class="category__content">
            <span> 提佣类型：</span>
            <span v-if="typeOfCommissionOptions.extractionType === 'CATEGORY_EXTRACTION'">类目提佣</span>
            <span v-else>订单金额提佣，按订单实付金额的{{ typeOfCommissionOptions.drawPercentage }}% ，进行提佣结算</span>
        </div>
        <div class="category__title">签约类目</div>
        <div class="category__content">
            <div class="category__content--btns">
                <el-button type="default" :disabled="!multipleSelection?.length" @click="handleMultiRemoveClick">移出类目</el-button>
                <el-button type="primary" @click="showAddDialog = true">添加类目</el-button>
            </div>
            <el-table :data="tableData" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="一级类目" prop="parentName" />
                <el-table-column label="二级类目" prop="currentCategoryName" />
                <el-table-column label="类目扣率">
                    <template #default="{ row }">{{ renderRatio(row) }} </template>
                </el-table-column>
                <el-table-column label="操作" width="100px">
                    <template #default="{ row }">
                        <el-link :underline="false" type="danger" size="small" @click="handleRemoveClick(row.id)">移出</el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- <div class="category__bottom">
            <el-button type="primary" round class="shoplist__save" @click="handleSave">保存</el-button>
        </div> -->
    </div>
    <el-dialog v-model="showAddDialog" title="添加类目" destroy-on-close>
        <AddCategory :checked-list="checkedSubCategoryIds" @selection-change="selectionChange" />
        <template #footer>
            <el-button @click="showAddDialog = false">取消</el-button>
            <el-button type="primary" @click="confirmAddCategory">确定</el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(category) {
    @include e(title) {
        line-height: 1.5;
        font-size: 14px;
        margin-top: 15px;
    }
    @include e(content) {
        padding-left: 30px;
        @include m(btns) {
            text-align: right;
            width: 100%;
        }
    }
    @include e(bottom) {
        padding: 30px 100px;
    }
}
</style>
