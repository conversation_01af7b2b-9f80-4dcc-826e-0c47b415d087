<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-18 10:09:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-04 13:34:03
-->
<template>
    <div class="pagination">
        <el-pagination
            :page-sizes="$props.pageSizes"
            :page-size="$props.pageSize"
            :current-page="$props.pageNum"
            :total="$props.total"
            layout="total, prev, pager, next, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>
    </div>
</template>
<script setup lang="ts">
import { PropType } from 'vue'
const $props = defineProps({
    pageSize: {
        type: Number,
        default: 20,
    },
    pageNum: {
        type: Number,
        default: 1,
    },
    pageSizes: {
        type: Array as PropType<number[]>,
        default() {
            return [10, 20, 50, 100]
        },
    },
    total: {
        type: Number,
        default: 0,
    },
})
const $emit = defineEmits(['handleSizeChange', 'handleCurrentChange'])
// const parentConfig = inject("pageManageConfig");
const handleSizeChange = (val: number) => {
    $emit('handleSizeChange', val)
}
const handleCurrentChange = (val: number) => {
    $emit('handleCurrentChange', val)
}
</script>

<style lang="scss" scoped>
.pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

v-deep .el-pagination {
    padding: 0px;
    position: relative;
    right: -15px;
}
</style>
