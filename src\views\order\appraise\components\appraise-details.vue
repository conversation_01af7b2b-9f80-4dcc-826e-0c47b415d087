<template>
    <div class="details">
        <el-carousel v-if="detailsInfo?.medias?.length" height="450px" :autoplay="false">
            <el-carousel-item v-for="(item, index) in detailsInfo?.medias" :key="index">
                <video v-if="isVideo(item)" :src="item" style="max-width: 100%; max-height: 100%; display: block; margin: 0 auto" controls autoplay />
                <el-image v-else :src="item" style="width: 100%; height: 450px" fit="contain" />
            </el-carousel-item>
        </el-carousel>
        <!-- <div class="commodity">
            <el-image :src="detailsInfo?.image" style="width: 80px; height: 80px; flex-shrink: 0" />
            <div class="commodity__info">
                <p class="commodity__info--name">{{ detailsInfo?.name }}</p>
                <p class="commodity__info--spec">{{ detailsInfo?.specs?.join(',') }}</p>
                <p class="commodity__info--type">{{ SellTypeEnum[detailsInfo?.sellType] }}</p>
            </div>
        </div>
        <div class="details__line">
            <span class="details__line--label">评分</span>
            <span class="details__line--content">
                <el-rate :model-value="detailsInfo?.rate" disabled text-color="#ff9900" />
            </span>
        </div>
        <div class="details__line">
            <span class="details__line--label">评价</span>
            <span class="details__line--content">{{ detailsInfo?.comments }}</span>
        </div>
        <div v-if="detailsInfo?.shopReply" class="details__line">
            <span class="details__line--label">回复</span>
            <span class="details__line--content">{{ detailsInfo?.shopReply }}</span>
        </div> -->
    </div>
</template>

<script lang="ts" setup>
defineProps({
    detailsInfo: {
        type: Object,
        default: () => ({}),
    },
})
const isVideo = (url: string) => {
    const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mkv', 'mov', 'flv', 'wmv']
    const extension = url.split('.').pop()?.toLowerCase() || ''
    return videoExtensions.includes(extension)
}
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
</script>

<style lang="scss" scoped>
@include b(details) {
    @include e(line) {
        display: flex;
        align-items: center;
        line-height: 1.5;
        @include m(label) {
            font-weight: 600;
            margin-right: 8px;
        }
    }
}
@include b(commodity) {
    @include flex(flex-start);
    @include e(info) {
        flex: 1;
        margin-left: 8px;
        @include m(name) {
            font-weight: bold;
            font-size: 14px;
            @include utils-ellipsis(2);
        }
        @include m(spec) {
            font-size: 12px;
            margin-top: 3px;
        }
        @include m(type) {
            margin-top: 5px;
            font-size: 12px;
        }
    }
}
</style>
