.page--top {
  margin-top: 20px;
}

.user__list {
  border-collapse: collapse;

  thead tr td {
    // border-top: 1px solid #eee;
    padding: 10px 5px;
    background-color: #f6f8fa;

    text-align: center;

    &:first-child {
      width: 1%;
    }

    &:nth-child(2) {
      width: 50px;
      text-align: left !important;
    }

    &:last-child {
      width: 90px;
    }
  }

  tbody tr:nth-child(2) td {
    background-color: #ecf6ff;
    height: 40px;
    padding: 5px 5px;
    // border-right: 1px solid #ecf6ff;
    // border-left: 1px solid #ecf6ff;
    font-size: 12px !important;

    .checkItem {
      margin-left: 15px;
    }
  }

  tbody tr:nth-child(3) td {
    // border-bottom: 1px solid #ecf6ff;
    padding: 15px 9px;
    text-align: center;
    height: 75px;
    border-right: 1px solid #ecf6ff;

    &:first-child {
      // border-left: 1px solid #ecf6ff;
    }

    &:nth-child(2) {
      height: 100px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  &__good {
    width: 280px;
    display: flex;
    text-align: justify;
    padding-right: 20px;

    img {
      width: 70px;
      height: 70px;
    }

    &--msg {
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;

      &--price {
        color: #ff7417;
        margin-top: 10px;
      }
    }
  }

  .stockWarn {
    @include flex(center, center);
  }

  .upDown {
    display: flex;
    align-items: center;
    justify-content: center;

    &--goodUp {
      display: flex;
      width: 50px;
      height: 30px;
      justify-content: center;
      align-items: center;
      border-radius: 50px;
      color: white;
    }

    &--goodDown {
      margin-left: 10px;
      color: #2d8cf0;
      cursor: pointer;
    }
  }
}

.mouseEnter {
  // background-color: red;
  border: 1px solid #ecf6ff;
}

.mouseEnter:hover {
  // background-color: green;
  border: 1px solid #d7e0e8;
}

.pop--button {
  display: flex;
  justify-content: flex-end;
  margin-right: 10px;
}

.goodList {
  width: 280px;
  display: flex;
  text-align: justify;
  padding-right: 20px;

  img {
    width: 70px;
    height: 70px;
  }

  &__msg {
    margin-left: 10px;
    display: flex;
    flex-wrap: wrap;
    align-content: space-between;
    padding: 8px 0px;

    &--apply {
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.upDown {
  display: flex;
  align-items: center;
  justify-content: center;

  &__goodUp {
    display: flex;
    width: 50px;
    height: 20px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    color: white;
    margin-right: 10px;
  }

  &__goodDown {
    margin-left: 10px;
    color: #2d8cf0;
    cursor: pointer;
  }
}

.commandClass {
  height: 150px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}

.center {
  display: flex;
  justify-content: center;
}

.digTitle {
  font-size: 17px;
  font-weight: bold;
}
