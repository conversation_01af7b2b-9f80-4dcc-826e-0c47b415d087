/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 17:09:02
 */
/**
 * vue-router index 路由跳转
 * <AUTHOR>
 */
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import Main from '../components/layout/Main.vue'
import { goods, order, overview, mall, finance, marketing, business } from './MenusRoute'
import watermark from '@/utils/watermark'

const routes: Array<RouteRecordRaw> = [
    {
        name: 'sign',
        path: '/sign',
        meta: {
            title: '登录',
            isShow: 1,
        },
        component: () => import('@views/sign/Index.vue'),
    },
    {
        name: 'changePass',
        path: '/changePass',
        meta: {
            title: '找回密码',
        },
        component: () => import('@views/changePass/Index.vue'),
    },
    {
        path: '/',
        name: 'Main',
        component: Main,
        children: [
            overview,
            goods,
            order,
            marketing,
            // inventory,
            finance,
            mall,
        ],
    },
    {
        path: '/business',
        component: Main,
        children: [business],
    },
    {
        path: '/:pathMatch(.*)*',
        component: Main,
        meta: {
            title: '......',
        },
        children: [
            {
                path: '',
                name: 'hello',
                component: () => import('@views/^_^.vue'),
                meta: {
                    title: '......',
                },
            },
        ],
    },
]

const router = createRouter({
    history: createWebHashHistory(),
    routes: routes,
})
router.beforeEach((to) => {
    if (to.path === '/') {
        router.push('/overview').catch(() => {})
        return false
    }
    const { token } = useShopInfoStore().getterShopInfo
    const whiteList = ['/sign', '/changePass']
    if (!token && !whiteList.includes(to.path)) {
        router
            .push({
                path: '/sign',
            })
            .catch((fail) => {
                console.log('跳转失败', fail)
            })
        return false
    }
    return true
})

/**
 * zrb:添加路由后置处理，添加页面水印
 *
 */
router.afterEach((to, from, failure) => {
    if (!failure && to.name !== 'sign') {
        const shopInfo = useShopInfoStore().getterShopInfo
        const mobile = shopInfo?.mobile
        const userId = shopInfo?.userId
        watermark.load({ watermark_txt: mobile || userId || '宠有灵犀供应商' })
    } else {
        watermark.remove()
    }
})

export default router
