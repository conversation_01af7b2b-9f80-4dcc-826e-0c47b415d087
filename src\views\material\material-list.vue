<script lang="ts" setup>
import search from './components/search.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import useMaterialListHooks from './hooks/useMaterialListHooks'
import translationMaterial from './components/translationMaterial.vue'
import useMaterialCategoryList from './hooks/useMaterialCategoryList'
import uploading from './components/uploading.vue'
import { uploadFileApi } from '../../apis/upload/index'
import { doPutMaterialTo } from '@/apis/material'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'

const {
    checkedSelect,
    tableList,
    tableHeight,
    handlePreview,
    paginationOptions,
    initialList,
    changeTableHeight,
    handleSearch,
    lookInfoVal,
    lookInfos,
    lookInfoFn,
    disable,
    uploadingVal,
    uploadingFn,
    lookInfoValFn,
    handleSizeChange,
    handleCurrentChange,
    delBatchFn,
    handleAddCategoryTran,
    showDia,
    checkedSelectIds,
    ruleFormRef,
    searchCondition,
    pImgs,
} = useMaterialListHooks()
initialList()
const { currentFormModel, handleCloseDialog } = useMaterialCategoryList()
const upload = ref()
const loading = ref(false)
const uploadingConfirm = async () => {
    try {
        loading.value = true
        const formData = new FormData()
        if (upload.value) {
            formData.append('categoryId', upload.value.categoryFormModel.parentId)
            upload.value.fileLists.forEach((item: any) => {
                formData.append('files[]', item)
            })
            // if (!upload.value.categoryFormModel.parentId) return ElMessage.error('请选择上级分类')
            // else
            if (upload.value.fileLists.length === 0) return
            const res = await uploadFileApi('/gruul-mall-search/search/material/upload', formData)
            if (res.data.code === 200) {
                upload.value.fileLists = []
                uploadingVal.value = false
                initialList()
                ElMessage.success('上传成功')
            } else ElMessage.error(res.data.msg || '上传失败')
        }
    } finally {
        loading.value = false
    }
}
const emit = defineEmits(['categoryListFn'])
const uploadFn = () => {
    uploadingVal.value = false
    upload.value.categoryFormModel.parentId = ' ' //zrb:不清空分类选项--如果其他地方失效或不使用watch监听，可注释本行解决部分场景下弹窗分类选择重置为全部问题
    emit('categoryListFn')
}
const moveFn = async () => {
    // if (currentFormModel.parentId === '') return ElMessage.error('请选择上级分类')
    const { data, code, msg } = await doPutMaterialTo(currentFormModel.parentId, checkedSelectIds.value)
    if (code === 200) {
        initialList()
        showDia.value = false
        ElMessage.success('操作成功')
        checkedSelect.value = []
        return
    } else ElMessage.error(msg || '移动素材失败，请重试')
}
const rules: FormRules = {
    name: [
        { required: true, message: '请输入素材名称', trigger: 'blur' },
        { min: 0, max: 20, message: '素材名称 0 - 20 之间', trigger: 'blur' },
    ],
}
const props = defineProps({
    classificationId: {
        type: String,
        default: () => '',
    },
})
watch(
    () => props.classificationId,
    () => {
        searchCondition.categoryId = props.classificationId
        currentFormModel.parentId = searchCondition.categoryId //zrb:当前选中分类为父级分类
        initialList()
    },
)
/**
 * zrb:监听弹窗，重置为左侧分类选择节点，解决弹窗中选择其他分类后再次打开仍显示上一次的分类或全部问题
 *
 */
watch(
    () => showDia.value,
    (newVal) => {
        if (newVal) currentFormModel.parentId = searchCondition.categoryId //zrb:重置为当前选中分类
    },
)
watch(
    () => uploadingVal.value,
    (newVal) => {
        if (newVal) currentFormModel.parentId = searchCondition.categoryId //zrb:重置为当前选中分类
    },
)
</script>
<template>
    <div>
        <search :classification-id="props.classificationId" @change-show="changeTableHeight" @search="handleSearch" />
        <div class="btn">
            <el-button type="primary" @click="handleAddCategoryTran">移动至</el-button>
            <el-button type="primary" @click="uploadingFn">上传</el-button>
            <el-button type="danger" @click="delBatchFn">批量删除</el-button>
        </div>
        <q-table v-model:checked-item="checkedSelect" :data="tableList" :style="{ height: tableHeight }" style="overflow-y: scroll" :selection="true" class="table">
            <q-table-column label="" align="center" width="190px">
                <template #default="{ row }">
                    <div class="material-name" style="cursor: pointer" @click="handlePreview(row, 'look')">
                        <video v-if="row?.url?.split('.').pop() === 'mp4' || row?.url?.split('.').pop() === 'MP4'" :src="row?.url" :alt="row?.name" muted style="width: 70px; height: 70px" />
                        <div v-else :style="{ background: `url(${row?.url}) center no-repeat` }" class="rowImg"></div>
                        <!-- <img v-else :src="row?.url" :alt="row?.name" style="width: auto; height: 70px" /> -->
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="素材名称" align="center">
                <template #default="{ row }">
                    <div class="material-name">
                        <span class="name">{{ row?.name }}</span>
                    </div>
                </template>
            </q-table-column>
            <q-table-column label="格式" align="center" prop="format" width="80px" />
            <q-table-column label="尺寸" align="center" prop="size" />
            <q-table-column label="操作" align="center" width="180px">
                <template #default="{ row }">
                    <el-button text class="margin" type="primary" @click="handlePreview(row, 'look')">查看</el-button>
                    <el-button text class="margin" type="primary" @click="handlePreview(row, '')">重命名</el-button>
                    <el-button text class="margin" type="danger" @click="handlePreview(row, 'del')">删除</el-button>
                </template>
            </q-table-column>
        </q-table>
        <!-- 移动素材 -->
        <el-dialog v-model="showDia" title="移动素材" :width="650" :destroy-on-close="true" @close="handleCloseDialog">
            <translationMaterial v-model:form-model="currentFormModel" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDia = false">取消</el-button>
                    <el-button type="primary" @click="moveFn"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 上传素材 -->
        <el-dialog v-model="uploadingVal" title="上传素材" :width="900" :destroy-on-close="true" @close="uploadFn">
            <uploading ref="upload" v-model:form-model="currentFormModel" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="uploadFn">取消</el-button>
                    <el-button type="primary" :loading="loading" @click="uploadingConfirm"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 查看 && 重命名 -->
        <el-dialog v-model="lookInfoVal" :close-on-click-modal="!disable ? true : false" center :width="800" :destroy-on-close="true" style="background-color: #f8f8f8" @closed="lookInfoFn">
            <div :style="!disable ? 'height: 400px; display: flex; justify-content: center' : 'height: 100px'">
                <div v-if="!disable" style="width: 750px; height: 400px; overflow: hidden; text-align: center">
                    <video v-if="(lookInfos.format === 'mp4' || lookInfos.format === 'MP4') && !disable" :src="lookInfos?.url" loop autoplay muted controls style="width: auto; height: 400px" />
                    <div v-else-if="(lookInfos.format !== 'mp4' || lookInfos.format !== 'MP4') && !disable" :style="{ background: `url(${pImgs}) no-repeat center` }" class="pImg"></div>
                    <!-- <img
                        v-else-if="(lookInfos.format !== 'mp4' || lookInfos.format !== 'MP4') && !disable"
                        :src="lookInfos?.url"
                        :alt="lookInfos?.name"
                        style="width: auto; height: auto"
                    /> -->
                </div>
                <el-form v-if="disable" ref="ruleFormRef" :model="lookInfos" :rules="rules" label-position="left" label-width="100px">
                    <el-form-item label="素材名称：" :prop="disable ? 'name' : ''">
                        <el-input v-model="lookInfos.name" :disabled="disable ? false : true" style="width: 320px" @keypress.enter="lookInfoValFn(lookInfos)" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span v-if="disable" class="dialog-footer">
                    <el-button @click="lookInfoFn">取消</el-button>
                    <el-button type="primary" @click="lookInfoValFn(lookInfos)"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- <PageManage v-model="paginationOptions.page" load-init :total="paginationOptions.total" @reload="initialList" /> -->
        <page-manage
            :page-size="paginationOptions.page.size"
            :page-num="paginationOptions.page.current"
            :total="paginationOptions.total"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </div>
</template>

<style lang="scss" scoped>
@include b(btn) {
    text-align: right;
    margin: 5px 0;
}
@include b(margin) {
    margin: 0;
    padding: 0 7px;
}
@include b(name) {
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}
@include b(pImg) {
    width: 750px;
    height: 400px;
    background-size: contain !important ;
}
@include b(rowImg) {
    width: 140px;
    height: 70px;
    background-size: contain !important ;
}
</style>
