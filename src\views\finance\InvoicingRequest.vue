<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-08-14 19:05:14
 * @LastEditors: lexy
 * @LastEditTime: 2024-05-16 14:40:31
-->
<script setup lang="ts">
import { reactive, Ref, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import search from './components/Search.vue'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { doGetinvoiceRequestList, doGetinvoiceDetail, doPostrefuseInvoiceRequest, doPostinvoiceAttachment, doPostInvoiceRequest } from '@/apis/invoice'
import { INVOICE_OWNER_TYPE, INVOICE_TYPE } from '@/apis/invoice/model'
import PageManage from '@/components/pageManage/PageManage.vue'
import InvoicingDialogMain from './components/InvoicingDialogMain.vue'
import detailDialogMain from './components/detailDialogMain.vue'
/*
 *variable
 */
const { divTenThousand } = useConvert()
const $shopInfoStore = useShopInfoStore()
const $route = useRoute()
const $router = useRouter()

const tableList = reactive({
    page: { size: 10, current: 1 },
    list: [],
    total: 0,
})
const searchParams = ref({
    id: '',
    header: '',
    taxIdentNo: '',
    orderNo: '',
    invoiceHeaderType: '',
    date: '',
    applicationStartTime: '',
    applicationEndTime: '',
    invoiceStatus: '',
    invoiceOwnerType: 'SUPPLIER', // 供应商端
})

// 发票申请表单
const showCreateDialog = ref(false)
const createFormRef = ref<FormInstance>()
const createForm = ref({
    orderNo: '',
    invoiceHeaderId: 0, // 改为数字类型
    invoiceType: 'VAT_GENERAL' as keyof typeof INVOICE_TYPE,
    billingRemarks: '',
})
const invoiceHeaderType = {
    PERSONAL: '个人',
    ENTERPRISE: '企业',
}
const invoiceStatus = {
    REQUEST_IN_PROCESS: '开票中',
    SUCCESSFULLY_INVOICED: '开票成功',
    FAILED_INVOICE_REQUEST: '开票失败',
    CLIENT_CANCEL_REQUEST: '用户撤销',
}
const refuseDialog = ref(false) //拒绝弹窗
const form = ref({ RejectReason: '' }) //拒绝原因
const Dialogid = ref() //弹窗所需发票id
const InvoicingDialog = ref(false) //开票弹窗
const InvoicingDialogTypeType = ref(true) //(true开票| false重新上传发票）
const PdfList = ref<string[]>([]) //开票所需pdf
const detailDialog = ref(false) //详情弹窗
const invoiceDetail = ref() //发票详情
/*
 *lifeCircle
 */
onMounted(() => {
    // 检查是否有订单号查询参数，如果有则显示创建发票申请对话框
    const orderNo = $route.query.orderNo as string
    if (orderNo) {
        createForm.value.orderNo = orderNo
        showCreateDialog.value = true
    }
})

/*
 *function
 */
async function initList() {
    const { code, data, msg } = await doGetinvoiceRequestList({
        ...searchParams.value,
        ...tableList.page,
    })
    if (code !== 200) {
        ElMessage.error(msg || '获取申请列表失败')
        return
    }
    tableList.total = data.total
    tableList.list = data.records
}
const getSearch = (e) => {
    tableList.page.current = 1
    if (e.date.length > 0) {
        searchParams.value.applicationStartTime = e.date[0]
        searchParams.value.applicationEndTime = e.date[1]
    }
    searchParams.value = { ...searchParams.value, ...e }
    initList()
}
//tab点击事件
const handleTabClick = (status: 'FAILED_INVOICE_REQUEST' | 'SUCCESSFULLY_INVOICED' | 'REQUEST_IN_PROCESS' | '') => {
    searchParams.value.invoiceStatus = status
    initList()
}
const handledetail = async (id: string) => {
    const { data, code, msg } = await doGetinvoiceDetail(id)
    if (code !== 200) {
        ElMessage.error(msg || '获取发票设置失败')
        return
    }
    invoiceDetail.value = data
    detailDialog.value = true
    Dialogid.value = id
}
/**
 * @LastEditors: lexy
 * @description: 打开拒绝弹窗
 * @returns
 */
const openRefuseDialog = async (id: string) => {
    Dialogid.value = id
    refuseDialog.value = true
}
/**
 * @LastEditors: lexy
 * @description: 拒绝弹窗确认
 * @returns
 */
const formRef = ref<FormInstance>()
const handleRefuse = async () => {
    try {
        const validate = await formRef.value?.validate()
        if (!validate) return
        const { data, code, msg } = await doPostrefuseInvoiceRequest({
            id: Dialogid.value,
            denialReason: form.value.RejectReason,
        })
        if (code !== 200) {
            ElMessage.error(msg || '拒绝失败')
            return
        }
        refuseDialog.value = false
        initList()
    } catch (error) {
        console.error('error', error)
    }
}
/**
 * @LastEditors: lexy
 * @description: 拒绝弹窗关闭
 * @returns
 */
const refuseDialogClose = () => {
    Dialogid.value = ''
    form.value.RejectReason = ''
}
/**
 * @LastEditors: lexy
 * @description: 详情弹窗打开
 * @returns
 */
const handleConfirm = (id: string, header: string, invoiceAmount: string, taxIdentNo: string, attachments: string[]) => {
    Dialogid.value = id
    InvoicingDialogTypeType.value = true
    invoiceDetail.value = { header, invoiceAmount, taxIdentNo, attachments }
    InvoicingDialog.value = true
}
/**
 * @LastEditors: lexy
 * @description: 重新上传发票打开弹窗
 * @returns
 */
const againAgree = () => {
    InvoicingDialogTypeType.value = false
    detailDialog.value = false
    InvoicingDialog.value = true
}
/**
 * @LastEditors: lexy
 * @description: 上传pdf
 * @returns
 */
const PdfListChange = (e) => {
    PdfList.value = e
}
/**
 * @LastEditors: lexy
 * @description: 同意开票
 * @returns
 */
const handleAgree = async () => {
    if (!PdfList.value.length) return ElMessage.error('请上传发票')

    const { data, code, msg } = await doPostinvoiceAttachment({
        invoiceRequestId: Dialogid.value,
        shopId: $shopInfoStore.getterShopInfo.id,
        invoiceAttachmentUrl: PdfList.value,
    })
    if (code !== 200) {
        ElMessage.error(msg || '开票失败')
        return
    }
    InvoicingDialog.value = false
    initList()
}
/**
 * @LastEditors: lexy
 * @description: 同意开票弹窗关闭
 * @returns
 */
const InvoicingDialogClose = () => {
    Dialogid.value = ''
    PdfList.value = []
    invoiceDetail.value = {}
}

/**
 * @description: 创建发票申请
 * @returns
 */
const handleCreateInvoiceRequest = async () => {
    try {
        const validate = await createFormRef.value?.validate()
        if (!validate) return

        const requestData = {
            invoiceOwnerType: 'SUPPLIER', // 供应商端
            orderNos: [createForm.value.orderNo], // 订单号数组
            invoiceType: createForm.value.invoiceType, // 发票类型
            billingRemarks: createForm.value.billingRemarks || '', // 开票备注
            invoiceHeaderId: createForm.value.invoiceHeaderId || 0, // 发票抬头ID
        }

        const { code, msg } = await doPostInvoiceRequest(requestData)

        if (code !== 200) {
            ElMessage.error(msg || '创建发票申请失败')
            return
        }

        ElMessage.success('发票申请创建成功')
        showCreateDialog.value = false

        // 清空表单
        createForm.value = {
            orderNo: '',
            invoiceHeaderId: 0,
            invoiceType: 'VAT_GENERAL',
            billingRemarks: '',
        }

        // 刷新列表
        initList()

        // 清除URL查询参数
        $router.replace({ name: 'InvoicingRequestChildren' })
    } catch (error) {
        console.error('创建发票申请失败:', error)
        ElMessage.error('创建发票申请失败，请稍后重试')
    }
}

/**
 * @description: 关闭创建发票申请对话框
 * @returns
 */
const handleCloseCreateDialog = () => {
    showCreateDialog.value = false
    // 清除URL查询参数
    $router.replace({ name: 'InvoicingRequestChildren' })
}

const rules: any = {
    RejectReason: [{ required: true, message: '拒绝原因为必填项', trigger: 'blur' }],
}

const createRules: any = {
    orderNo: [{ required: true, message: '订单号为必填项', trigger: 'blur' }],
    invoiceHeaderId: [
        { required: true, message: '发票抬头ID为必填项', trigger: 'blur' },
        { type: 'number', min: 1, message: '发票抬头ID必须大于0', trigger: 'blur' },
    ],
    invoiceType: [{ required: true, message: '发票类型为必填项', trigger: 'blur' }],
}
</script>

<template>
    <search @on-search-params="getSearch" />
    <el-tabs v-model="searchParams.invoiceStatus" @tab-change="handleTabClick">
        <el-tab-pane label="全部" name=""></el-tab-pane>
        <el-tab-pane label="开票中" name="REQUEST_IN_PROCESS"></el-tab-pane>
        <el-tab-pane label="开票成功" name="SUCCESSFULLY_INVOICED"></el-tab-pane>
        <el-tab-pane label="开票失败" name="FAILED_INVOICE_REQUEST"></el-tab-pane>
    </el-tabs>
    <el-table
        empty-text="暂无数据~"
        :data="tableList.list"
        style="width: 100%"
        :header-cell-style="{
            'background-color': '#F6F8FA',
            'font-weight': 'normal',
            color: '#515151',
        }"
    >
        <el-table-column label="申请单号" width="200" align="center">
            <template #default="{ row }">{{ row.id }} </template>
        </el-table-column>
        <el-table-column label="抬头" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.header }}</div>
            </template>
        </el-table-column>
        <el-table-column label="税号" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.taxIdentNo }}</div>
            </template>
        </el-table-column>
        <el-table-column label="开票金额" width="150" align="center">
            <template #default="{ row }">
                <div style="color: #fd0505">{{ divTenThousand(row.invoiceAmount) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="邮箱地址" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.email }}</div>
            </template>
        </el-table-column>
        <el-table-column label="类型" width="100" align="center">
            <template #default="{ row }">
                <div>{{ invoiceHeaderType[row.invoiceHeaderType] }}</div>
            </template>
        </el-table-column>
        <el-table-column label="开票状态" width="100" align="center">
            <template #default="{ row }">
                <div>{{ invoiceStatus[row.invoiceStatus] }}</div>
            </template>
        </el-table-column>
        <el-table-column label="关联订单号" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.orderNo }}</div>
            </template>
        </el-table-column>
        <el-table-column label="申请时间" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.createTime }}</div>
            </template>
        </el-table-column>
        <el-table-column label="更新时间" width="200" align="center">
            <template #default="{ row }">
                <div>{{ row.updateTime }}</div>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
                <el-link type="primary" @click="handledetail(row.id)">详情</el-link>
                <span v-if="row.invoiceStatus === 'REQUEST_IN_PROCESS'">
                    <el-link type="primary" style="margin-left: 10px" @click="handleConfirm(row.id, row.header, row.invoiceAmount, row.taxIdentNo, row.attachments)">开票</el-link>
                    <el-link type="danger" style="margin-left: 10px" @click="openRefuseDialog(row.id)">拒绝</el-link>
                </span>
            </template>
        </el-table-column>
    </el-table>
    <PageManage v-model="tableList.page" load-init :total="tableList.total" @reload="initList()" />

    <!-- 拒绝弹窗 -->
    <el-dialog v-model="refuseDialog" title="拒绝" width="500px" center @close="refuseDialogClose">
        <el-form ref="formRef" :model="form" class="m-t-16" :rules="rules">
            <el-form-item label="拒绝原因" prop="RejectReason" class="m-b-10">
                <el-input v-model="form.RejectReason" maxlength="15" placeholder="请输入拒绝原因" show-word-limit />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="refuseDialog = false">取消</el-button>
            <el-button type="primary" @click="handleRefuse()"> 确认 </el-button>
        </template>
    </el-dialog>

    <!-- 开票弹窗 -->
    <el-dialog v-model="InvoicingDialog" :title="InvoicingDialogTypeType ? '开票' : '重新上传发票'" width="800px" center destroy-on-close @close="InvoicingDialogClose">
        <InvoicingDialogMain :invoice-detail="invoiceDetail" @handle-pdf-list-change="PdfListChange" />
        <template #footer>
            <el-button @click="InvoicingDialog = false">取消</el-button>
            <el-button type="primary" @click="handleAgree"> 确认 </el-button>
        </template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialog" title="详情" width="1000px" center>
        <detailDialogMain :invoice-detail="invoiceDetail" />
        <template #footer>
            <el-button v-if="invoiceDetail.invoiceStatus === 'SUCCESSFULLY_INVOICED'" type="primary" @click="againAgree"> 重新上传发票 </el-button>
        </template>
    </el-dialog>

    <!-- 创建发票申请弹窗 -->
    <el-dialog v-model="showCreateDialog" title="创建发票申请" width="600px" center @close="handleCloseCreateDialog">
        <el-form ref="createFormRef" :model="createForm" :rules="createRules" label-width="120px">
            <el-form-item label="订单号" prop="orderNo">
                <el-input v-model="createForm.orderNo" placeholder="请输入订单号" readonly />
            </el-form-item>
            <el-form-item label="发票类型" prop="invoiceType">
                <el-select v-model="createForm.invoiceType" placeholder="请选择发票类型" style="width: 100%">
                    <el-option label="增值税电子普通发票" value="VAT_GENERAL" />
                    <el-option label="增值税电子专用发票" value="VAT_SPECIAL" />
                </el-select>
            </el-form-item>
            <el-form-item label="发票抬头ID" prop="invoiceHeaderId">
                <el-input-number v-model="createForm.invoiceHeaderId" placeholder="请输入发票抬头ID" style="width: 100%" :min="0" />
            </el-form-item>
            <el-form-item label="开票备注">
                <el-input v-model="createForm.billingRemarks" type="textarea" :rows="3" placeholder="请输入开票备注" maxlength="200" show-word-limit />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="handleCloseCreateDialog">取消</el-button>
            <el-button type="primary" @click="handleCreateInvoiceRequest">确认创建</el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(m-b-10) {
    margin-bottom: 10px;
}
@include b(m-t-16) {
    margin-top: 16px;
}
</style>
