/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-16 14:13:58
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 09:10:18
 */
import { defineStore } from 'pinia'
import { ShopInfoStore } from './state'
import Storage from '@/utils/Storage'
import shopInfo from './state'
const localStorage = new Storage()
export const useShopInfoStore = defineStore('shopStore', {
    state: () => shopInfo,
    actions: {
        SET_SHOP_INFO(payload: ShopInfoStore) {
            this.shopInfo = Object.assign(this.shopInfo, payload)
            localStorage.setItem('shopStore', this.shopInfo, 60 * 60 * 24)
        },
        DEL_SHOP_INFO() {
            this.shopInfo = {
                id: '',
                logo: '',
                name: '',
                newTips: '',
                status: '',
                token: '',
                userId: '',
                nickname: '',
                refresh_token: '',
                shopId: '',
                shopType: '',
                mobile: '',
            }
            localStorage.removeItem('shopStore')
        },
        SET_SHOP_TOKEN(payload: { access_token: string; refresh_token: string }) {
            this.shopInfo.token = payload.access_token
            this.shopInfo.refresh_token = payload.refresh_token
            localStorage.setItem('shopStore', this.shopInfo, 60 * 60 * 24)
            return payload.access_token
        },
        SET_SHOP_ADMIN_DATA(payload) {
            this.shopInfo = Object.assign(this.shopInfo, payload)
            localStorage.setItem('shopStore', this.shopInfo, 60 * 60 * 24)
        },
    },
    getters: {
        getterShopInfo: (state) => {
            return getShopInfoFn(state)
        },
        token: (state) => state.shopInfo.token,
        refresh_token: (state) => state.shopInfo.refresh_token,
    },
})
function getShopInfoFn(state) {
    if (judgeNotEmpty(state.shopInfo)) {
        const localStorage = new Storage()
        const shopInfo = localStorage.getItem('shopStore')
        if (judgeNotEmpty(shopInfo)) {
            return { id: '', token: '' }
        } else {
            state.shopInfo = shopInfo
            return shopInfo
        }
    } else {
        return state.shopInfo
    }
}
/**
 * @LastEditors: lexy
 * @description: 判断shopInfo是否为空
 * @param {*} obj
 */
function judgeNotEmpty(shopInfo) {
    if (JSON.stringify(shopInfo) === '{}' || JSON.stringify(shopInfo) === 'null') return true
    // if (Object.keys(shopInfo).length === 0) return true
    if (!shopInfo.id) return true
    return false
}
