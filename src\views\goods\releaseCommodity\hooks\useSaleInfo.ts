import { FormInject } from '../types'
const useSaleInfo = () => {
    const $parent = inject('form') as FormInject
    const submitForm = $parent.submitForm
    const diffPrice = (pre: number, next: number) => {
        const diff = pre - next
        return diff > 0 ? diff : 0
    }
    // 单规格零售价校验
    const realPriceValidatePass = (rule: any, value: any, callback: any) => {
        if (value <= 0) {
            callback(new Error('输入数值请大于0'))
        } else if (Number(submitForm.value.skus[0].price) < Number(value)) {
            callback(new Error('零售价应小于等于划线价'))
        } else if (Number(submitForm.value.skus[0].commission) > Number(value)) {
            callback(new Error('零售价应大于佣金'))
        } else {
            callback()
        }
    }
    // 单规格划线价校验
    const minimumPurchaseValidatePass = (rule: any, value: any, callback: any) => {
        if (Number(value) <= 0 && submitForm.value.sellType !== 'CONSIGNMENT') {
            callback(new Error('输入数值请大于0'))
        } else {
            callback()
        }
    }
    // 单规格划线价校验
    const commissionValidatePass = (rule: any, value: any, callback: any) => {
        if (Number(value) > Number(submitForm.value.skus[0].retailPrice)) {
            callback(new Error('佣金应小于零售价！'))
        } else {
            callback()
        }
    }
    // 单规格起批数校验
    const validatePass = (rule: any, value: any, callback: any) => {
        if (Number(value) <= 0) {
            callback(new Error('输入数值请大于0'))
        } else if (Number(submitForm.value.skus[0].retailPrice) > Number(value)) {
            callback(new Error('划线价应大于等于零售价'))
        } else {
            callback()
        }
    }

    return { submitForm, diffPrice, validatePass, commissionValidatePass, minimumPurchaseValidatePass, realPriceValidatePass }
}

export default useSaleInfo
