<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-02 10:50:34
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-09 09:51:29
-->
<template>
    <div>
        <template v-if="!submitForm.id || $route.query.isCopy">
            <release-title title="批量设置" />
            <div class="table-container">
                <el-table :data="[{}]" table-layout="auto" width="2000" :cell-style="{ minWidth: '130px' }">
                    <el-table-column label="sku图" align="center">
                        <template #default>
                            <!-- <q-upload
                            :width="50"
                            :height="50"
                            append-to-body
                            :format="{ types: ['image/png', 'image/jpg', 'image/gif', 'image/jpeg', 'image/avif', 'image/webp'] } as any"
                            @update:src="changeBatchFormSku($event, 'image')"
                        /> -->
                            <div style="width: 100px; height: 52px; cursor: auto" class="selectMaterialStyle">
                                <div class="selectMaterialStyle" style="margin: 0 auto"
                                    @click="buttonlFn('image', true, -1)">
                                    <span class="selectMaterialStyle__span">+</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="划线价" align="center">
                        <decimal-input v-model="price" :decimal-places="2" style="width: 110px"
                            @change="changeBatchFormSku($event, 'price')">
                            <template #prepend>￥</template>
                        </decimal-input>
                    </el-table-column>
                    <el-table-column label="佣金" align="center">
                        <decimal-input v-model="commission" :decimal-places="2" style="width: 110px"
                            @change="changeBatchFormSku($event, 'commission')">
                            <template #prepend>￥</template>
                        </decimal-input>
                    </el-table-column>
                    <el-table-column label="零售价" align="center">
                        <decimal-input v-model="retailPrice" :decimal-places="2" style="width: 110px"
                            @change="changeBatchFormSku($event, 'retailPrice')">
                            <template #prepend>￥</template>
                        </decimal-input>
                    </el-table-column>
                    <el-table-column label="供货价" align="center">
                        <decimal-input v-model="salePrice" :decimal-places="2" style="width: 110px" :disabled="true"
                            @change="changeBatchFormSku($event, 'salePrice')">
                            <template #prepend>￥</template>
                        </decimal-input>
                    </el-table-column>
                    <el-table-column label="重量" align="center">
                        <template #default="{ row }">
                            <div class="weight">
                                <decimal-input v-model="row.weight" :decimal-places="3" style="width: 70px"
                                    class="com__input--width-weight" @change="changeBatchFormSku($event, 'weight')">
                                    <!-- <template #append>kg</template> -->
                                </decimal-input>
                                <el-select v-model="row.weightType" placeholder="单位" style="width: 74px"
                                    @change="changeBatchFormSku($event, 'weightType')">
                                    <el-option v-for="item in weightTypeOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="sku销量" align="center">
                    <template #default>
                        <el-input-number :precision="0" :controls="false" style="width: 110px" @change="changeBatchFormSku($event, 'initSalesVolume')" />
                    </template>
                </el-table-column> -->
                    <el-table-column label="限购数量" align="center">
                        <template #default>
                            <el-input-number :precision="0" :controls="false" style="width: 70px"
                                @change="changeBatchFormSku($event, 'limitNum')" />
                        </template>
                    </el-table-column>
                    <el-table-column label="库存" align="center">
                        <template #default>
                            <el-input-number :precision="0" :controls="false" style="width: 70px"
                                @change="changeBatchFormSku($event, 'initStock')" />
                        </template>
                    </el-table-column>
                    <el-table-column label="起批数" align="center">
                        <template #default>
                            <el-input-number :precision="0" :controls="false" style="width: 70px"
                                :disabled="submitForm.sellType === 'CONSIGNMENT'"
                                @change="changeBatchFormSku($event, 'minimumPurchase')" />
                        </template>
                    </el-table-column>
                </el-table>
                <el-alert v-if="validateInfo" :title="validateInfo" type="error" :closable="false" />
            </div>
        </template>
        <release-title title="商品限购设置">
            <span
                style="color: rgba(108, 108, 108, 1); font-weight: 400; font-size: 12px; margin-left: 50px">如果选择商品限购，则下属全部规格都将计入限购数量，无论是否限制单独的规格限购
            </span>
        </release-title>
        <!-- 仅对不限购的sku进行控制 -->
        <div style="display: flex; align-items: center; margin: 20px 0 0 20px">
            <span>限购</span>
            <el-input v-model="goodLimitNum" placeholder="请输入限购数量" class="input-with-select"
                style="max-width: 300px; margin-left: 20px" type="number"
                :disabled="goodLimitType === 'UNLIMITED' || openEidt" @input="
                    (val) => {
                        const num = Math.min(Math.max(Math.floor(Number(val) || 1), 1), 99)
                        goodLimitNum = Number(num)
                    }
                ">
                <template #prepend>
                    <el-select v-model="goodLimitType" placeholder="限购类型" :disabled="openEidt"
                        @change="changeGoodLimitType">
                        <el-option label="不限购" value="UNLIMITED"></el-option>
                        <el-option label="商品限购" value="PRODUCT_LIMITED"></el-option>
                    </el-select>
                </template>
            </el-input>
        </div>
        <release-title title="规格明细" />
        <div class="table-container">
            <el-table :data="getSpecList">
                <el-table-column v-for="(headerStr, index) in computedSpecHeader.slice(0, 1)" :key="index"
                    :label="headerStr" width="150" fixed="left" align="center">
                    <template #default="{ row, $index }">
                        <!-- <q-upload
                            v-model:src="row.image"
                            :width="50"
                            :height="50"
                            append-to-body
                            :format="{ types: ['image/png', 'image/jpg', 'image/gif', 'image/jpeg', 'image/avif', 'image/webp'] } as any"
                        /> -->
                        <div v-if="!row.image" class="selectMaterialStyle" style="margin: 0 auto"
                            @click="buttonlFn('image', false, $index)">
                            <span class="selectMaterialStyle__span">+</span>
                        </div>
                        <q-btn-image v-else alt="" class="selectMaterialStyle" style="margin: 0 auto" :src="row.image"
                            :preview-src-list="[row.image]" :fit="'contain'"
                            :disabled="submitForm.status === 'UNDER_REVIEW'">
                            <template #preview="{ openViewer }">
                                <el-icon color="#fff" @click="openViewer">
                                    <FullScreen />
                                </el-icon>
                            </template>
                            <template #first>
                                <el-icon color="#fff" @click.stop="buttonlFn('image', false, $index)">
                                    <Edit />
                                </el-icon>
                            </template>
                        </q-btn-image>
                    </template>
                </el-table-column>
                <el-table-column v-for="(classAr, idx) in $prop.classArr" :key="idx" align="center" width="180"
                    :label="classAr.name">
                    <template #default="{ row }">
                        {{ Array.isArray(row.specs) ? row.specs?.[idx] : row.specs.name }}
                    </template>
                </el-table-column>
                <el-table-column v-for="(headerStr, index) in computedSpecHeader.slice(1)" :key="index" align="center"
                    width="180" :label="(headerStr as keyof typeof ExtraLabelKeyEnum)">
                    <template #default="{ row, $index: i }">
                        <template v-if="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'limitType'">
                            <el-select v-model="row.limitType" :disabled="openEidt"
                                @change="changeSingleSku($event, 'limitType', i)">
                                <el-option label="不限购" value="UNLIMITED"></el-option>
                                <!-- <el-option label="商品限购" value="PRODUCT_LIMITED"></el-option> -->
                                <el-option label="规格限购" value="SKU_LIMITED"></el-option>
                            </el-select>
                        </template>
                        <template
                            v-else-if="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'stockType'">
                            <el-select v-model="row.stockType" :disabled="openEidt"
                                @change="changeSingleSku($event, 'stockType', i)">
                                <el-option label="有限库存" value="LIMITED"></el-option>
                                <el-option label="无限库存" value="UNLIMITED"></el-option>
                            </el-select>
                        </template>
                        <template
                            v-else-if="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'limitNum'">
                            <el-input-number v-model="row.limitNum" :precision="0" :controls="false"
                                :min="row.limitType !== 'UNLIMITED' ? 1 : 0"
                                :max="goodLimitType !== 'UNLIMITED' && goodLimitNum < 99 ? goodLimitNum : 99"
                                :disabled="row.limitType === 'UNLIMITED' || openEidt"
                                @change="changeSingleSku($event, 'limitNum', i)" />
                        </template>
                        <template
                            v-else-if="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'initStock'">
                            <el-input-number v-model="row.initStock" :precision="0" :controls="false"
                                :disabled="row.stockType === 'UNLIMITED' || openEidt"
                                @change="changeSingleSku($event, 'initStock', i)" />
                        </template>
                        <template
                            v-else-if="['commission', 'retailPrice', 'price', 'salePrice', 'weight'].includes(ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum])">
                            <el-space fill :size="4">
                                <decimal-input
                                    v-model="row[ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum]]"
                                    :decimal-places="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'weight' ? 3 : 2"
                                    style="width: 90px"
                                    :disabled="ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum] === 'salePrice' || submitForm.status === 'UNDER_REVIEW'"
                                    @change="changeSingleSku($event, ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum], i)" />
                                <el-text
                                    v-if="skuValidateInfo && skuValidateInfo.row === i && skuValidateInfo.key === ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum]"
                                    class="mx-1" size="small" type="danger">
                                    {{ skuValidateInfo.content }}
                                </el-text>
                            </el-space>
                        </template>
                        <template
                            v-else-if="['weightType'].includes(ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum])">
                            <el-select v-model="row[ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum]]"
                                placeholder="单位" style="width: 74px"
                                @change="changeSingleSku($event, ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum], i)">
                                <el-option v-for="item in weightTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                        <template v-else>
                            <el-input-number
                                v-model="row[ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum]]"
                                :precision="0"
                                :controls="false"
                                :disabled="['minimumPurchase'].includes(ExtraLabelKeyEnum[headerStr as keyof typeof ExtraLabelKeyEnum])"
                            />
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <!-- 选择素材 e -->
    <selectMaterial :dialog-visible="dialogVisible" :upload-files="1" @select-material-fn="selectMaterialFn"
        @cropped-file-change="croppedFileChange" @checked-file-lists="checkedFileLists" />
    <!-- 选择素材 d -->
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import type { PropType } from 'vue'
import { NormType, NormListType } from './index'
import QUpload from '@/components/q-upload/q-upload.vue'
import QBtnImage from '@/components/q-btn-image/q-btn-image.vue'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
import ReleaseTitle from '../ReleaseTitle.vue'
import { ExtraLabelKeyEnum } from './index'
import { FormInject } from '../../types'
import useSaleInfo from '../../hooks/useSaleInfo'
// 选择素材 e
import selectMaterial from '@/views/material/selectMaterial.vue'
import { Edit, FullScreen } from '@element-plus/icons-vue'
const { mulHundred, divHundred } = useConvert()

const $route = useRoute()
const dialogVisible = ref(false)
const selectMaterialFn = (val: boolean) => {
    dialogVisible.value = val
}
const materialConfig = reactive({
    parameterId: '',
    isBatch: false,
    listIndex: -1,
})

const buttonlFn = (val: string, isBatch = false, listIndex: number) => {
    dialogVisible.value = true
    materialConfig.parameterId = val
    materialConfig.isBatch = isBatch
    materialConfig.listIndex = listIndex
}

const price = ref<number>()
const commission = ref<number>()
const retailPrice = ref<number>()
const salePrice = computed(() => `${divHundred(diffPrice(Number(mulHundred(retailPrice.value)), Number(mulHundred(commission.value))))}`)
const openEidt = computed(() => submitForm.value.status !== 'UNDER_REVIEW' && !(!$route.query.id || $route.query.isCopy))
// 批量设置校验提示信息内容
const validateInfo = ref<string>()
// 规格明细校验提示信息内容
const skuValidateInfo = ref<{ content: string; key: string; row: number } | null>(null)

const { diffPrice } = useSaleInfo()

// 重量单位
const weightTypeOptions = [
    {
        label: '千克',
        value: 'KG',
    },
    {
        label: '克',
        value: 'G',
    },
]

// @cropped-file-change="" 裁剪后返回的单个素材
// @checked-file-lists=""  选中素材返回的素材合集
const croppedFileChange = (val: string) => {
    if (materialConfig.isBatch === false && materialConfig.listIndex > -1) {
        let newSpecList = [...getSpecList.value]
        newSpecList[materialConfig.listIndex]['image'] = val || ''
        $emit('changeNormList', newSpecList)
    } else {
        changeBatchFormSku(val, materialConfig.parameterId)
    }
    resetMaterialConfig()
}
const checkedFileLists = (val: string[]) => {
    if (materialConfig.isBatch === false && materialConfig.listIndex > -1) {
        let newSpecList = [...getSpecList.value]
        newSpecList[materialConfig.listIndex]['image'] = val?.shift() || ''
        $emit('changeNormList', newSpecList)
    } else {
        changeBatchFormSku(val?.shift() || '', materialConfig.parameterId)
    }
    resetMaterialConfig()
}
const resetMaterialConfig = () => {
    materialConfig.isBatch = false
    materialConfig.parameterId = ''
    materialConfig.listIndex = -1
}
// 选择素材 d

const $parent = inject('form') as FormInject
const submitForm = $parent.submitForm
const $prop = defineProps({
    list: {
        type: Array as PropType<NormListType[]>,
        default() {
            return []
        },
    },
    classArr: {
        type: Array as PropType<NormType[]>,
        default() {
            return []
        },
    },
    memoSpecList: {
        type: Array as PropType<NormListType[]>,
        default() {
            return []
        },
    },
})
const $Route = useRoute()
const $emit = defineEmits(['changeNormList'])

// 用于控制整个品的限购类型、数量
const goodLimitNum = ref<number>(0)
const goodLimitType = ref<string>('UNLIMITED')

const computedSpecHeader = computed(() => {
    if (submitForm.value.productType === 'REAL_PRODUCT') {
        return ['sku图', '划线价', '佣金', '零售价', '供货价', '库存', '起批数', '限购类型', '限购数量', '重量', '单位' /*, 'sku销量'*/]
    } else if (!$Route.query.id || $Route.query.isCopy) {
        return ['sku图', '划线价', '佣金', '零售价', '供货价', '库存类型', '库存', '起批数', '限购类型', '限购数量', '重量', '单位' /*, 'sku销量'*/]
    } else {
        return ['sku图', '划线价', '佣金', '零售价', '供货价', '起批数', '限购类型', '限购数量', '重量', '单位' /*, 'sku销量'*/]
    }
})

// 控制传入的规格列表
const getSpecList = computed(() => {
    const memoSpecList = $prop.memoSpecList
    let list = $prop.list
    if (memoSpecList.length > 0) {
        list = list.map((item) => {
            const tempArr = memoSpecList.filter((ite) => (equar(item.specs, ite.specs) ? true : false))
            if (tempArr.length > 0) item = tempArr[0]
            return item
        })
    }
    // 初始化商品限购数量和类型
    return list.map(({ commission, retailPrice, limitType, limitNum, ...item }) => {
        if (limitType === 'PRODUCT_LIMITED') {
            goodLimitNum.value = limitNum
            goodLimitType.value = limitType
        }
        return {
            ...item,
            commission,
            retailPrice,
            limitNum: limitType === 'PRODUCT_LIMITED' ? 0 : limitNum,
            limitType: limitType === 'PRODUCT_LIMITED' ? 'UNLIMITED' : limitType,
            salePrice: `${divHundred(diffPrice(Number(mulHundred(Number(retailPrice))), Number(mulHundred(commission))))}`,
        }
    })
})

/**
 * @LastEditors: lexy
 * @description: 父级获取限购类型和数量
 */
const getGoodLimit = () => {
    return { type: goodLimitType.value, num: goodLimitNum.value }
}

const changeBatchFormSku = (val: any, key: string) => {
    if (key === 'price' || key === 'retailPrice' || key === 'commission') {
        if (key === 'price' || key === 'retailPrice') {
            if (Number(val) <= 0) {
                return (validateInfo.value = '输入数值请大于0')
            }
        }
        if (Number(price.value) < Number(retailPrice.value)) {
            return (validateInfo.value = key === 'price' ? '划线价应大于等于零售价!' : '零售价应小于等于划线价！')
        }
        if (Number(commission.value) > Number(retailPrice.value)) {
            return (validateInfo.value = key === 'retailPrice' ? '零售价应大于佣金！' : '佣金应小于零售价！')
        }
    }
    validateInfo.value = ''
    $emit(
        'changeNormList',
        getSpecList.value.map((item) => {
            if (key === 'limitNum') {
                if (item.limitType !== 'UNLIMITED') item[key] = val
                else item.limitNum = 0
            } else if (key === 'initStock') {
                if (item.stockType !== 'UNLIMITED') item.initStock = val
            } else {
                item[key] = val
            }
            return item
        }),
    )
}

// 修改商品限购类型
const changeGoodLimitType = () => {
    if (goodLimitType.value === 'UNLIMITED') {
        goodLimitNum.value = 0
    }
}

/**
 * 修改规格明细
 * @param val   修改值
 * @param key   修改键值
 * @param index 修改行
 */
const changeSingleSku = (val: any, key: string, index: number) => {
    if (key === 'price' || key === 'retailPrice' || key === 'commission') {
        const { price, retailPrice, commission } = getSpecList.value[index]
        if (key === 'price' || key === 'retailPrice') {
            if (Number(val) <= 0) {
                return (skuValidateInfo.value = { content: '输入数值请大于0', key, row: index })
            }
        }
        if (Number(price) < Number(retailPrice)) {
            return (skuValidateInfo.value = { content: key === 'price' ? '划线价应大于等于零售价!' : '零售价应小于等于划线价！', key, row: index })
        }
        if (Number(commission) > Number(retailPrice)) {
            return (skuValidateInfo.value = { content: key === 'retailPrice' ? '零售价应大于佣金！' : '佣金应小于零售价！', key, row: index })
        }
    }
    skuValidateInfo.value = null

    var newNormList = [...getSpecList.value]
    newNormList[index] = { ...newNormList[index], [key]: val }
    if (key === 'limitType') {
        newNormList[index].limitNum = val === 'UNLIMITED' ? 0 : 1
    }
    $emit('changeNormList', newNormList)
}

/**
 * @LastEditors: lexy
 * @description: 对比
 * @param {string | string[]} a
 * @param {string | string[]} b
 */
function equar(a: string[] | string, b: string[] | string) {
    if (a.length !== b.length) {
        return false
    } else {
        // 循环遍历数组的值进行比较
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) {
                return false
            }
        }
        return true
    }
}

defineExpose({ getGoodLimit })
</script>

<style scoped>
.avatar-uploader .avatar {
    width: 150px;
    height: 50px;
    display: block;
}
</style>

<style lang="scss" scoped>
:deep(.el-input) {

    .el-input-group__prepend,
    .el-input-group__append {
        padding: 0 9px;
    }
}

.input-group {
    display: flex;
    align-items: center;

    &__prefix {
        background-color: #f7f7f7;
        height: 32px;
        line-height: 32px;
        padding: 0 9px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: inline-block;
    }
}

.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.input-with-select {
    :deep(.el-input-group__prepend) {
        padding: 0;
        width: 40%;
        background-color: var(--el-fill-color-blank);
    }
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 50px;
    height: 50px;
    text-align: center;
    border: 1px dashed #ccc;
    border-radius: 10px;
}

.table-container {
    padding-bottom: 30px;
}

@include b(selectMaterialStyle) {
    width: 54px;
    height: 54px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    @include e(span) {
        color: #999;
        font-size: 20px;
    }
}

.weight {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.com__input--width-weight {
    margin-right: 4px;
}
</style>
