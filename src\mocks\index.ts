/*
 * @description:
 * @Author: lexy
 * @Date: 2023-03-30 13:44:08
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-30 16:47:51
 */
import { setupWorker, rest } from 'msw'
import { groupServer } from './group'
export const mockServer = function mockServer() {
    if (process.env.NODE_ENV !== 'production' && import.meta.env.VITE_RESUME_MOCK === 'true') {
        return setupWorker(...groupServer)
    } else {
        return null
    }
}
