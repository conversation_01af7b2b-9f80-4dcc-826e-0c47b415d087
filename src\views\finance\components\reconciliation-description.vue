<template>
    <div class="description">
        <div class="description__content">供应商对账是指本供应商 与 各交易对象之间的资金(不含提现业务)往来，订单【已完成】后生成对应的对账数据。</div>
        <el-table :data="configData" border>
            <el-table-column prop="transactionType" label="交易类型" width="140" />
            <el-table-column prop="flowType" label="收入/支出" />
            <el-table-column prop="transactionObject" label="交易对象" />
            <el-table-column prop="fieldDescription" label="字段说明" width="450" />
        </el-table>
    </div>
</template>

<script lang="ts" setup>
const configData = [
    {
        transactionType: '代销交易',
        flowType: '收入',
        transactionObject: '店铺',
        fieldDescription: '代销商品交易成功后店铺支付给供应商的货款',
    },
    {
        transactionType: '代销运费',
        flowType: '收入',
        transactionObject: '用户',
        fieldDescription: '用户购买代销商品支付的运费',
    },
    {
        transactionType: '采购交易',
        flowType: '收入',
        transactionObject: '店铺',
        fieldDescription: '店铺购买采购商品支付的货款，仅线上支付订单',
    },
    {
        transactionType: '采购运费',
        flowType: '收入',
        transactionObject: '店铺',
        fieldDescription: '店铺采购商品支付的运费',
    },
    {
        transactionType: '会员包邮',
        flowType: '收入',
        transactionObject: '平台',
        fieldDescription: '因会员享有该权益而获得的平台补贴',
    },
    {
        transactionType: '平台服务费(代销)',
        flowType: '支出',
        transactionObject: '平台',
        fieldDescription: '根据平台类目扣率或订单金额进行提佣，支付给平台的服务费',
    },
    {
        transactionType: '平台服务费(采购)',
        flowType: '支出',
        transactionObject: '平台',
        fieldDescription: '根据平台类目扣率或订单金额进行提佣，支付给平台的服务费，仅线上支付订单',
    },
]
</script>

<style lang="scss" scoped>
@include b(description) {
    @include e(content) {
        margin-bottom: 15px;
    }
}
</style>
