<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-08 22:28:56
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-08 18:15:34
-->
<script setup lang="ts">
import { PropType } from 'vue'
import type { ShopOrderItem } from '@/views/order/types/order'
/*
 *variable
 */
const $props = defineProps({
    prop: {
        type: String,
        default: '',
    },
    align: {
        type: String,
        default: 'center',
    },
    row: {
        type: Object as PropType<any>,
        default() {
            return {}
        },
    },
    packageId: {
        type: String,
        default() {
            return undefined
        },
    },
    shopOrderItems: {
        type: Object as PropType<Array<ShopOrderItem>>,
        default() {
            return []
        },
    },
})
/*
 *lifeCircle
 */
/*
 *function
 */
const transformAlign = (val: string) => {
    switch (val) {
        case 'left':
            return 'flex-start'
        case 'right':
            return 'flex-end'
        default:
            return 'center'
    }
}
</script>

<template>
    <div class="item__content" :style="{ justifyContent: transformAlign($props.align) }">
        <slot :row="$props.row" :shop-order-items="$props.shopOrderItems"></slot>
    </div>
</template>

<style lang="scss" scoped></style>
