@import '../global.scss';
$--colors: (
        "primary": (
                "base": $rows-color-primary,
        ),
        "success": (
                "base": #4fe373,
        ),
        "warning": (
                "base": #f2711c,
        ),
        "danger": (
                "base": #db2828,
        ),
        "error": (
                "base": #db2828,
        ),
        "info": (
                "base": $rows-color-primary,
        ),
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward "element-plus/theme-chalk/src/common/var.scss" with
  (
    // do not use same name, it will override.
    $colors: $--colors,
    $button-padding-horizontal: ("default": 50px)
  );

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;
