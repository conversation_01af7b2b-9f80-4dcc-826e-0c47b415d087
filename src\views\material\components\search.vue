<script lang="ts" setup>
import MCard from '@/components/MCard.vue'
import { doGetMaterialCategoryList, doPostMaterialSuggest } from '@/apis/material'
import { ElMessage } from 'element-plus'
import { cloneDeep, isArray } from 'lodash-es'
const props = defineProps({
    // eslint-disable-next-line vue/require-default-prop
    classificationId: {
        type: String || undefined,
        defalut: () => '',
    },
    showCategory: {
        type: Boolean,
        default: false,
    },
})
const showSearchCondition = ref(props.showCategory ? true : false)
const searchType = reactive({
    categoryId: ' ',
    format: '',
    name: '',
    imgSize: '',
})
// searchType.format = suggestList.value?.format || ''
const $emit = defineEmits(['search', 'changeShow', 'reset', 'categoryBolFn'])
watch(
    () => showSearchCondition.value,
    (val) => {
        $emit('changeShow', val)
    },
)
function handleSearch() {
    if (!props.showCategory) searchType.categoryId = props.classificationId || ''
    else if (isArray(searchType.categoryId)) searchType.categoryId = searchType.categoryId?.pop()
    $emit('search', toRaw(searchType))
}

const handleReset = () => {
    Object.keys(searchType).forEach((key) => (searchType[key] = ''))
    searchType.categoryId = ' '
    handleSearch()
    $emit('reset')
}
const handleResetSearchType = () => {
    searchType.format = ''
    searchType.imgSize = ''
    searchType.name = ''
}
const suggestList = reactive<{ category: any[]; format: string[]; size: string[] }>({
    category: [],
    format: [],
    size: [],
})
const doPostMaterialSuggestInt = async (boolVal = true) => {
    const allClassifications = [{ hasChildren: false, name: '全部', id: ' ', parentId: ' ' }]
    let itemVal: any = []
    let iteVal: any = []
    let itVal: any = []
    const result = await doGetMaterialCategoryList('')
    if (result.code === 200) suggestList.category = [...allClassifications, ...result.data]
    else ElMessage.error(result.msg || '获取素材推荐检索建议分类失败')
    if (boolVal) $emit('categoryBolFn')
    cloneDeep(suggestList.category).filter((item) => {
        if (item.selectHistory) {
            itemVal = item
            return itemVal
        } else if (item.hasChildren) {
            item.children.filter((ite: { selectHistory: any; hasChildren: any; children: any[] }) => {
                if (ite.selectHistory) {
                    iteVal = ite
                    return iteVal
                } else if (ite.hasChildren) {
                    ite.children.filter((it: { selectHistory: any }) => {
                        if (it.selectHistory) {
                            itVal = it
                            return itVal
                        }
                    })
                }
            })
        }
    })
    if (itVal && itVal.id) {
        searchType.categoryId = searchType.categoryId || itVal.id
    } else if (iteVal && iteVal.id) searchType.categoryId = searchType.categoryId || iteVal.id
    else if (itemVal && itemVal.id) searchType.categoryId = searchType.categoryId || itemVal.id
    else searchType.categoryId = ' '
}
const materialSuggest = async (categoryId?: string) => {
    const { code, data, msg } = await doPostMaterialSuggest(categoryId)
    if (code === 200) {
        suggestList.format = data.format
        suggestList.size = data.size
    } else ElMessage.error(msg || '获取素材推荐检索建议失败')
}
if (props.showCategory) doPostMaterialSuggestInt()
watch(
    () => props.classificationId,
    () => {
        materialSuggest(props.classificationId)
    },
)
watch(
    () => searchType.categoryId,
    (value) => {
        materialSuggest(value)
    },
    { deep: true, immediate: true },
)
const defaultProps = {
    expandTrigger: 'hover',
    checkStrictly: true,
    emitPath: false,
    children: 'children',
    label: 'name',
    value: 'id',
}
defineExpose({ handleReset, handleResetSearchType, searchType, doPostMaterialSuggestInt })
</script>
<template>
    <div class="search">
        <m-card v-model="showSearchCondition">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col v-if="props.showCategory" :span="7">
                        <el-form-item label="分类">
                            <el-cascader v-model="searchType.categoryId" :options="suggestList.category" :show-all-levels="false" :props="defaultProps" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="props.showCategory ? 5 : 7">
                        <el-form-item label="格式">
                            <el-select v-model="searchType.format" placeholder="请选择格式">
                                <el-option v-for="(item, index) in suggestList?.format" :key="index" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="props.showCategory ? 5 : 7">
                        <el-form-item label="尺寸">
                            <el-select v-model="searchType.imgSize" placeholder="请选择尺寸" :filterable="true">
                                <el-option v-for="(item, index) in suggestList?.size" :key="index" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="props.showCategory ? 7 : 9">
                        <el-form-item label="素材名称">
                            <el-input v-model="searchType.name" placeholder="请输入名称" @keypress.enter="handleSearch"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item right style="margin-bottom: 0; padding-bottom: 20px">
                    <el-button class="from_btn" type="primary" round @click="handleSearch">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>
