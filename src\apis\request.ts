/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-01 18:27:27
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-04 13:53:41
 */
/**
 * axios api网络请求 设置拦截
 * <AUTHOR>
 */
import axios, { AxiosResponse } from 'axios'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import { AxiosCanceler } from '@/utils/http/axiosCancel'
import router from '../router/index'
import { signByUser } from '@/apis/sign'
import { CRUD_ERROR_CODE, TOKEN_OVERDUE } from './http.type'
import { ElMessage } from 'element-plus'
import { R } from './http.type'

const TOKEN_TYPE = 'Bearer '
axios.defaults.headers.post['Content-Type'] = 'application/json'
axios.defaults.headers.put['Content-Type'] = 'application/json'
const baseURL = import.meta.env.MODE === 'dev' ? import.meta.env.VITE_APP_BASE_API : import.meta.env.VITE_BASE_URL
const request = axios.create({
    baseURL,
    timeout: Number(import.meta.env.VITE_REQUEST_TIME_OUT),
    withCredentials: false,
    headers: {
        'Client-Type': import.meta.env.VITE_CLIENT_TYPE,
        'Device-Id': 1,
    },
})

// 取消请求的方法
const axiosCance = new AxiosCanceler()

//是否是单体应用
const isSingle = import.meta.env.VITE_IS_SINGLE && import.meta.env.VITE_IS_SINGLE.toLowerCase() === 'true'
//单体应用矫正正则
const singleUrlCorrectRegex = /\/?.*?\//
//矫正url函数
const urlCorrect = (currentUrl: undefined | string) => {
    return !currentUrl ? currentUrl : isSingle ? currentUrl.replace(singleUrlCorrectRegex, '/') : currentUrl
}

//请求拦截器
request.interceptors.request.use(
    (config) => {
        const { token, id } = useShopInfoStore().getterShopInfo
        if (!isRefreshing && token) {
            config.headers.Authorization = TOKEN_TYPE + token
        }
        if (id) {
            config.headers['Shop-Id'] = id
        }

        config.url = urlCorrect(config.url)
        axiosCance.addPending(config)
        return config
    },
    (error) => {
        return Promise.reject(error)
    },
)
let isRefreshing = false
let requests: any[] = []
//响应拦截器
request.interceptors.response.use(
    async (response: AxiosResponse<R<any>>) => {
        const result = response.data
        if (result.data?.total) {
            result.data.total = Number(result.data.total)
            result.data.size = Number(result.data.size)
            result.data.pages = Number(result.data.pages)
            result.data.current = Number(result.data.current)
        }
        if (response.status !== 200 || !result) {
            return Promise.reject({
                msg: '服务器异常',
            })
        }
        const code = result.code
        if (code === 4) {
            if (!isRefreshing) {
                isRefreshing = true
                refreshingFn()
            }
            return new Promise((reslove) => {
                requests.push((token: string) => {
                    response.headers.Authorization = TOKEN_TYPE + token
                    reslove(request(response.config))
                })
            })
        }
        if (TOKEN_OVERDUE.includes(code)) {
            axiosCance.removeAllPending()
            redirectWithLogin()
        } else if (CRUD_ERROR_CODE.includes(code)) {
            ElMessage.error(response.data.msg)
        }
        axiosCance.removePending(response.config)
        return Promise.resolve(response)
    },
    (error) => {
        return Promise.reject(error)
    },
)

const redirectWithLogin = () => {
    useShopInfoStore().DEL_SHOP_INFO()
    const { query, fullPath } = router.currentRoute.value
    let redirect = query?.redirect
    router
        .push({
            path: '/sign',
            query: { redirect: redirect ? redirect : fullPath.indexOf('/sign') ? '/' : fullPath },
        })
        .catch((fail) => {})
}

const refreshingFn = async () => {
    const refresh_token = useShopInfoStore().refresh_token
    try {
        const { data, code } = await signByUser({ grant_type: 'refresh_token', refresh_token })
        const newToken = useShopInfoStore().SET_SHOP_TOKEN({
            refresh_token: data.refresh_token,
            access_token: data.access_token,
        })
        requests.forEach((cd: (t: string) => any) => cd(newToken))
        requests = []
        isRefreshing = false
    } catch (error) {
        console.log('error', error)
    } finally {
        isRefreshing = false
    }
}
export default request
