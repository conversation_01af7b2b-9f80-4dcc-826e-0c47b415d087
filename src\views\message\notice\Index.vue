<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-11 20:15:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-13 13:47:36
-->
<template style="padding: 20px">
    <el-table :data="msgPage.records" :show-header="false" row-class-name="notice-table-row">
        <el-table-column>
            <template #default="scope">
                <el-row style="padding: 10px">
                    <el-col :span="18">
                        <!-- <el-link :href="scope.row.url" @click="handleClick"> -->
                        <el-link @click="handleClick(scope.row)">
                            <el-badge value="new" :hidden="scope.row.read">
                                {{ scope.row.title + '' }}
                            </el-badge>
                        </el-link>
                    </el-col>
                    <el-col :span="6">
                        {{ scope.row.createTime }}
                    </el-col>
                </el-row>
            </template>
        </el-table-column>
    </el-table>
    <PageManage v-model="msgPage.page" :total="msgPage.total" load-init @reload="getMessagePage" />
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { messagePage } from '../../../apis/message'
import PageManage from '../../../components/pageManage/PageManage.vue'
import { useRouter } from 'vue-router'
interface MsgPageRecords {
    channel: string
    content: string
    createBy: string
    createTime: string
    id: string
    msgType: string
    read: boolean
    sendType: string
    title: string
    type: string
    updateBy: string
    updateTime: string
    url: string
}
const msgPage = reactive({
    records: [] as MsgPageRecords[],
    total: 0,
    page: {},
})
const $router = useRouter()
const getMessagePage = () => {
    messagePage({ ...msgPage.page }).then((response) => {
        const page = response.data
        msgPage.total = page.total
        msgPage.records = page.records
    })
}
const handleClick = (row: MsgPageRecords) => {
    $router.push({
        name: 'messageNoticeDetail',
        query: {
            id: row.id,
        },
    })
}
</script>
<style>
.notice-table-row {
    padding: 20px 20px;
}
</style>
<style scoped>
.column-margin {
    margin-left: 20px;
    margin-right: 20px;
}

.notice-new-badge {
}
</style>
<style lang="scss" scoped>
::v-deep .el-badge__content,
::v-deep .is-fixed {
    transform: translate(40px, -9px);
    border: transparent;
}
</style>
