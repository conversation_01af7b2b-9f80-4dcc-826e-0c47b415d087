<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-09 14:03:02
 * @LastEditors: lexy
 * @LastEditTime: 2022-08-10 11:24:48
-->
<script setup lang="ts">
import { PropType } from 'vue'
import { regionData } from 'element-china-area-data'
import { AddressFn } from '@/components/q-address'
/*
 *variable
 */
const $props = defineProps({
    address: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
})
/*
 *lifeCircle
 */

/*
 *function
 */
</script>

<template>
    <span>{{ AddressFn(regionData, $props.address) }}</span>
</template>

<style scoped lang="scss"></style>
