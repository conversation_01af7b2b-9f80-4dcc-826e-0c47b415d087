<script lang="ts" setup>
import Category from './category.vue'
import MaterialList from './material-list.vue'

const classificationId = ref('')
const classificationIdVal = (val: string) => {
    classificationId.value = val
}
const freshKey = ref(0)
const categoryListFn = () => {
    // freshKey.value = Date.now() //zrb:不触发分类更新，会导致左侧选择节点消失
    if (categoryRef.value) categoryRef.value.getMaterialCategoryList() //zrb:触发数据更新
}
const categoryRef = ref<any>(null)
</script>
<template>
    <div class="material">
        <Category ref="categoryRef" :key="freshKey" class="material__category" @classification-id-val="classificationIdVal" />
        <MaterialList :classification-id="classificationId" @category-list-fn="categoryListFn" />
    </div>
</template>

<style lang="scss" scoped>
@include b(material) {
    display: flex;
    @include e(category) {
        flex-shrink: 0;
        width: 200px;
        height: 800px;
        overflow: auto;
    }
}
</style>
