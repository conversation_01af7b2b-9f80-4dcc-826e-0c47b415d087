/**
 * 物流告警通知管理 API 接口
 */

import { get, post, put } from '../http'
import type { BatchProcessParams } from './types'

/**
 * 获取物流通知列表
 * @param params 查询参数
 */
export const getLogisticsNoticeList = (params?: any) => {
    return get({
        url: 'gruul-mall-order/order/notice',
        params,
    })
}

/**
 * 获取物流通知详情
 * @param id 记录ID
 */
export const getLogisticsNoticeDetail = (id: number) => {
    return get({
        url: `gruul-mall-order/order/notice/${id}`,
    })
}

/**
 * 处理物流通知
 * @param id 记录ID
 * @param data 处理数据
 */
export const processLogisticsNotice = (id: number, data: any) => {
    return put({
        url: `gruul-mall-order/order/notice/process/${id}`,
        data,
    })
}

/**
 * 批量催发货
 * @param id 物流通知记录ID
 */
export const shipmentBatch = (id: number) => {
    return post({
        url: 'gruul-mall-order/order/notice/shipmentBatch',
        data: id,
    })
}

/**
 * 批量处理物流通知
 * @param data 批量处理参数
 */
export const batchProcessLogisticsNotice = (data: BatchProcessParams) => {
    return post({
        url: 'gruul-mall-order/order/notice/dealBatch',
        data,
    })
}

/**
 * 导出物流通知数据
 * @param params 查询参数
 */
export const exportLogisticsNoticeData = (params?: any) => {
    return get({
        url: 'gruul-mall-order/order/notice/export',
        params,
    })
}

/**
 * 重新发送通知
 * @param id 记录ID
 */
export const resendNotice = (id: number) => {
    return post({
        url: `gruul-mall-order/order/notice/resend/${id}`,
    })
}

/**
 * 批量重新发送通知
 * @param ids 记录ID数组
 */
export const batchResendNotice = (ids: number[]) => {
    return post({
        url: 'gruul-mall-order/order/notice/batch-resend',
        data: { ids },
    })
}

/**
 * 获取统计信息
 * @param params 查询参数
 */
export const getLogisticsStatistics = (params?: any) => {
    return get({
        url: 'gruul-mall-order/order/notice/statistics',
        params,
    })
}
