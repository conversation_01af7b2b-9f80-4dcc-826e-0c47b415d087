/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-21 18:08:52
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 09:18:32
 */
import { get, put, post } from '../http'
/**
 * @LastEditors: lexy
 * @description: 订单备注
 * @param {string} ids  总订单号 或者 店铺订单号 平台端 为总订单号列表 商家端 为店铺订单号列表
 * @param {string} status
 */
export const doPutOrderRemark = (nos: string[], remark: string) => {
    return put({
        url: `gruul-mall-order/order/remark/batch`,
        data: {
            nos,
            remark,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 售后订单备注
 * @param {string} ids
 * @param {string} status
 */
export const doPutAfsOrderRemark = (nos: string[], remark: string) => {
    return put({
        url: `gruul-mall-afs/afs/order/remark/batch`,
        data: {
            nos,
            remark,
        },
    })
}


/**
 * @LastEditors: lexy
 * @description: 获取说明文章
 * @param {string} articleType
 */
export const doGetArticle = (articleType: string) => {
    return get({
        url: `system/magic/shop/article/get?article_type=${articleType}`,
        baseURL: import.meta.env.VITE_ARTICLE_BASE_URL,
    })
}
