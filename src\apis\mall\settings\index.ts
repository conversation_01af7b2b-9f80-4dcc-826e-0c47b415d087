/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-26 16:14:01
 * @LastEditors: lexy
 * @LastEditTime: 2022-05-26 17:44:25
 */
import { get, post, put, del, patch } from '../../http'

/**
 * 商家设置信息获取
 * @param data
 */
export const getsettings = (data: any) => {
    return get({
        url: '/gruul-mall-shop/shop/info',
        params: data,
    })
}
/**
 * 店铺交易信息获取
 * @param data
 */
export const gettrade = (data: any) => {
    return get({
        url: '/gruul-mall-order/order/config/form',
        params: data,
    })
}
/**
 * 商家设置信息修改
 * @param data
 */
export const postsettings = (data: any) => {
    return post({
        url: '/gruul-mall-shop/shop/info/update',
        data,
    })
}
/**
 * 商铺交易信息编辑
 * @param data
 */
export const posttrade = (data: any) => {
    return post({
        url: '/gruul-mall-order/order/config/form',
        data,
    })
}
