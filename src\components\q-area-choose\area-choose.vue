<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-09-09 10:23:10
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-23 16:36:22
-->
<script setup lang="ts">
import { ref, PropType, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import { cloneDeep } from 'lodash-es'
import { regionData } from 'element-china-area-data'
interface RegionList {
    label: string
    value: string
    isCheck?: boolean
    isDisable?: boolean
    isIndeterminate?: boolean
    children: RegionList[]
}
interface ChooseAreaItem {
    upperCode: string
    upperName: string
    length: number
    lowerName: string[]
    lowerCode: string[]
}
/*
 *variable
 */
const $props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    allChooseArr: {
        type: Array as PropType<ChooseAreaItem[]>,
        default() {
            return []
        },
    },
    currentChoose: {
        type: Array as PropType<ChooseAreaItem[]>,
        default() {
            return []
        },
    },
})
const $emit = defineEmits(['update:show', 'change'])
const dialogType = useVModel($props, 'show', $emit)
const regionSource = ref(regionData)
// 筛选数组
const filterCheckArr = ref([])
// 全选标识
const allChooseType = ref(false)
// 全选disable标识
const allDisType = ref(false)
// 全选indeterminate标识
const allIndeterminateType = ref(false)

watch(dialogType, (newVal) => {
    if (newVal) {
        initRegion($props.allChooseArr, $props.currentChoose)
        checkAllStatus(regionSource.value)
    }
})

/**
 * @LastEditors: lexy
 * @description: 省选中
 * @param {RegionList} e
 */
const handleChangeProvince = (province: RegionList) => {
    if (province.isDisable) return

    province.isIndeterminate = false
    province.children.forEach((city) => {
        if (!city.isDisable) {
            city.isCheck = province.isCheck
        }
    })
    checkAllStatus(regionSource.value)
}
/**
 * @LastEditors: lexy
 * @description: 市选中
 * @param {RegionList} province
 * @param {RegionList} city
 */
const handleChangeCity = (province: RegionList, city: RegionList) => {
    if (city.isDisable) return

    const checkedCities = province.children.filter((c) => c.isCheck && !c.isDisable)
    province.isCheck = checkedCities.length === province.children.filter((c) => !c.isDisable).length
    province.isIndeterminate = checkedCities.length > 0 && !province.isCheck
    checkAllStatus(regionSource.value)
}
/**
 * @LastEditors: lexy
 * @description: 关闭弹窗重置checklist
 */
const handleHideDialog = () => {
    // regionSource.value = initRegion(JSON.parse(JSON.stringify(regionData)))
}
/**
 * @LastEditors: lexy
 * @description: 确认
 */
/**
 * {
 *  parentCode:'',
 *  parentName:'',
 *  childrenCode:[]
 * }
 */
const handleSure = () => {
    $emit('change', getCheckItem(regionSource.value, filterCheckArr.value))
    dialogType.value = false
}

const handleChangeAll = (e: boolean) => {
    regionSource.value = regionSource.value.map((province: RegionList) => ({
        ...province,
        isCheck: e && !province.isDisable,
        isIndeterminate: false,
        children: province.children.map((city) => ({
            ...city,
            isCheck: e && !city.isDisable,
        })),
    }))
    checkAllStatus(regionSource.value)
}

/**
 * @LastEditors: lexy
 * @description: 获取已选中省市区
 * @param {RegionList} data
 */
function getCheckItem(regionData: RegionList[], filterData: ChooseAreaItem[]) {
    let tempArr = []
    for (let i = 0; i < regionData.length; i++) {
        // 未选中
        if (!regionData[i].isCheck && !regionData[i].isIndeterminate) continue
        let tempObj = {
            upperCode: regionData[i].value,
            upperName: regionData[i].label,
            length: regionData[i].children.length,
            lowerName: [] as string[],
            lowerCode: [] as string[],
        }
        for (let j = 0; j < regionData[i].children.length; j++) {
            if (regionData[i].children[j].isCheck && !regionData[i].children[j].isDisable) {
                tempObj.lowerCode.push(regionData[i].children[j].value)
                tempObj.lowerName.push(regionData[i].children[j].label)
            }
        }
        if (tempObj.lowerCode.length > 0) {
            tempArr.push(tempObj)
        }
    }
    console.log('tempArr', tempArr)
    console.log('filterData', filterData)
    // 筛选重复项
    if (filterData.length) {
        for (let z = 0; z < tempArr.length; z++) {
            for (let k = 0; k < filterData.length; k++) {
                // 存在相同区域
                if (tempArr[z].upperCode === filterData[k].upperCode) {
                    tempArr[z].lowerCode = isInclude(tempArr[z].lowerCode, filterData[k].lowerCode)
                    tempArr[z].lowerName = isInclude(tempArr[z].lowerName, filterData[k].lowerName)
                }
            }
        }
    }
    return tempArr
}
/**
 * @LastEditors: lexy
 * @description: 检测选中区域是否存在filterData中
 * @param chooseArr 当前选中数组
 * @param filterArr 筛选数组
 */
function isInclude(chooseArr: string[], filterArr: string[]): string[] {
    if (!filterArr.length) return chooseArr
    let tempArr: string[] = []
    for (let i = 0; i < chooseArr.length; i++) {
        if (!filterArr.includes(chooseArr[i])) {
            tempArr.push(chooseArr[i])
        }
    }
    return tempArr
}
function initRegion(allChooseArr?: ChooseAreaItem[], currentChoose?: ChooseAreaItem[]) {
    const data = cloneDeep(regionSource.value)
    let temp = []

    const currentChooseSet = new Set<string>()
    const allChooseSet = new Set<string>()

    // Safely populate the sets
    if (Array.isArray(currentChoose)) {
        currentChoose.forEach((item) => {
            if (Array.isArray(item.lowerCode)) {
                item.lowerCode.forEach((code) => currentChooseSet.add(code))
            }
        })
    }

    if (Array.isArray(allChooseArr)) {
        allChooseArr.forEach((item) => {
            if (Array.isArray(item.lowerCode)) {
                item.lowerCode.forEach((code) => allChooseSet.add(code))
            }
        })
    }

    for (let i = 0; i < data.length; i++) {
        const province = data[i]
        const isProvinceInAllChoose = Array.isArray(allChooseArr) && allChooseArr.some((item) => item.upperCode === province.value)
        const isProvinceInCurrentChoose = Array.isArray(currentChoose) && currentChoose.some((item) => item.upperCode === province.value)

        province.isCheck = isProvinceInAllChoose
        province.isDisable = isProvinceInAllChoose && !isProvinceInCurrentChoose

        let allCitiesSelected = true
        let someCitiesSelected = false

        if (Array.isArray(province.children)) {
            for (let j = 0; j < province.children.length; j++) {
                const city = province.children[j]
                const isCityInAllChoose = allChooseSet.has(city.value)
                const isCityInCurrentChoose = currentChooseSet.has(city.value)

                city.isCheck = isCityInAllChoose
                city.isDisable = isCityInAllChoose && !isCityInCurrentChoose

                if (isCityInAllChoose) {
                    someCitiesSelected = true
                } else {
                    allCitiesSelected = false
                }
            }
        }

        province.isIndeterminate = someCitiesSelected && !allCitiesSelected

        temp.push(province)
    }

    regionSource.value = temp
}
/**
 * @LastEditors: lexy
 * @description: check当前全选状态
 * @returns {*}
 */
function checkAllStatus(temp: RegionList[]) {
    const checkedProvinceLength = temp.filter((item) => item.isCheck).length
    const indeterProvinceLength = temp.filter((item) => item.isIndeterminate).length
    const totalProvinces = temp.length

    allChooseType.value = checkedProvinceLength === totalProvinces
    allIndeterminateType.value = indeterProvinceLength > 0 || (checkedProvinceLength > 0 && checkedProvinceLength !== totalProvinces)
    allDisType.value = temp.every((item) => item.isDisable)
}

function isAllDisable(province: RegionList) {
    return province.isCheck && province.children.every((item) => item.isDisable)
}
</script>

<template>
    <el-dialog v-model="dialogType" @close="handleHideDialog">
        <el-checkbox
            v-for="(province, index) in regionSource"
            :key="index"
            v-model="province.isCheck"
            :indeterminate="province.isIndeterminate"
            :disabled="isAllDisable(province)"
            style="width: 140px"
            @change="handleChangeProvince(province)"
        >
            {{ province.label }}
            <el-popover placement="right" :width="160" trigger="hover">
                <template #reference>
                    <el-icon><i-ep-arrowRight /></el-icon>
                </template>
                <template #default>
                    <div style="gap: 50px; height: 100px; overflow-y: auto">
                        <el-checkbox
                            v-for="(city, idx) in province.children"
                            :key="idx"
                            v-model="city.isCheck"
                            :label="city.label"
                            :disabled="city.isDisable"
                            @change="handleChangeCity(province, city)"
                        >
                            {{ city.label }}
                        </el-checkbox>
                    </div>
                </template>
            </el-popover>
        </el-checkbox>
        <el-checkbox v-model="allChooseType" :disabled="allDisType" :indeterminate="allIndeterminateType" @change="handleChangeAll">全选</el-checkbox>
        <template #footer>
            <span>
                <el-button type="" @click="dialogType = false">取消</el-button>
                <el-button type="primary" @click="handleSure">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped></style>
