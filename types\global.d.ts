/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-05 23:58:25
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-30 16:46:12
 */
export {}
declare global {
    interface ImportMetaEnv {
        VITE_BASE_MAIN_PATH: string
        VITE_BASE_IMAGE_URL: string
        VITE_BASE_URL: string
        VITE_LOCAL_STORAGE_KEY_PREFIX: string
        VITE_REQUEST_TIME_OUT: string
        VITE_STOMP_CONNECT_URI: string
        VITE_IS_SINGLE: string
        VITE_CLIENT_TYPE: string
        VITE_SERVER_PORT: string
        VITE_RESUME_MOCK: string
    }
    type FN = (...arg: any[]) => void
    type Fn = () => void
}
