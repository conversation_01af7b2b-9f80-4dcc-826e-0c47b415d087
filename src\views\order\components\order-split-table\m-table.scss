@import '@/assets/css/mixins/mixins';

.o_table {
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 13px;
    background: transparent;

    .o_table--head {
        position: sticky;
        top: -11px;
        z-index: 999;
    }
    .hide {
        display: none;
    }

    &--container {
        background: #f5f7fa;
        padding: 16px 12px;
        position: relative;
        border-radius: 12px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

        &.single {
            background: none;
            box-shadow: none;

            .o_table--head {
                th {
                    &:first-child {
                        border-radius: 12px 0 0px 0px;
                    }

                    &:last-child {
                        border-radius: 0 12px 0px 0px;
                    }
                }

                &::after {
                    display: none;
                }
            }
        }
    }
    &--shrink {
        flex: 1;
    }
    &--center {
        display: flex;
        align-items: center;
    }
    .close {
        background: #f5f5f5 !important;
    }

    .hover--class {
        &:hover {
            .body--header {
                background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
                transition: all 0.3s ease;
            }

            .body--content {
                td {
                    background: #f8fafc;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                }
            }
        }
    }

    .ordinary--class {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);

            .body--header {
                background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);

                &::before {
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                }
            }

            .body--content {
                td {
                    background: #fefefe;
                }
            }
        }
    }

    .need--border {
        $bc: #bcdfff;

        .body--content {
            td {
                border-right: 1px solid $bc;

                &:last-child {
                    border-color: $bc;
                }
            }
        }
    }

    &--empty {
        .empty__td {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            font-size: 16px;
            color: #94a3b8;
            position: relative;

            &::before {
                content: '📦';
                display: block;
                font-size: 32px;
                margin-bottom: 8px;
                opacity: 0.6;
            }
        }
    }

    &--head {
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 16px 12px;
            text-align: center;
            color: #ffffff;
            font-weight: 600;
            border: none;
            height: 56px;
            vertical-align: middle;
            font-size: 14px;
            position: relative;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);

            &:first-child {
                border-radius: 12px 0 0 0;
            }

            &:last-child {
                border-radius: 0 12px 0 0;
            }

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.1);
                opacity: 0;
                transition: opacity 0.3s ease;
                border-radius: inherit;
            }

            &:hover::before {
                opacity: 1;
            }
        }

        &:after {
            content: '';
            display: block;
            height: 8px;
            background: transparent;
        }

        &.padding {
            &:after {
                content: '';
                display: block;
                height: 8px;
                background: transparent;
            }
        }
    }

    $b-c: #d8eaf9;

    &--body {
        .body--header {
            @include flex(flex-start);
            padding: 16px 20px;
            border: none;
            font-size: 14px;
            border-radius: 12px 12px 0px 0px;
            min-height: 64px;
            vertical-align: middle;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border-bottom: 3px solid #e2e8f0;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 4px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 2px 0 0 2px;
            }
        }

        &.default {
            .body--content {
                // border-radius: 10px 10px 0px 0px;

                .o_table--item {
                    &:first-child {
                        border-radius: 0px 0 0px 0px;
                    }

                    &:last-child {
                        border-radius: 0px 0px 0px 0px;
                    }
                }
            }

            .o_table--item {
                border-top: 1px solid #d8eaf9;
                vertical-align: middle;
                border-right: 1px solid #d8eaf9;
            }
        }

        .body--content {
            td {
                .item__content {
                    @include flex(center);
                }

                .selection__checkbox {
                    display: inline-block;
                    width: 100%;
                    height: 100%;

                    &.selection {
                        @include flex(flex-start);
                    }
                }

                padding: 16px 20px;
                border-top: 0px;
                border-bottom: 1px solid #e2e8f0;
                border-right: 0px;
                font-size: 13px;
                color: #475569;
                background: #ffffff;
                vertical-align: middle;
                transition: all 0.3s ease;

                &:first-child {
                    border-left: none;
                }

                &:last-child {
                    border-right: none;
                    border-radius: 0 0 12px 0;
                }

                &:hover {
                    background: #f8fafc;
                    transform: translateY(-1px);
                }
            }

            &.is--multiple {
                td {
                    &:first-child {
                        border-right: 1px solid $b-c !important;
                    }
                }
            }
        }

        &:after {
            content: '';
            display: block;
            height: 16px;
            background: transparent;
            width: 100%;
        }

        &:last-child {
            .body--content {
                td {
                    &:first-child {
                        border-radius: 0 0 0 12px;
                    }

                    &:last-child {
                        border-radius: 0 0 12px 0;
                    }
                }
            }
        }
    }

    .el-checkbox {
        margin-right: 12px !important;

        .el-checkbox__input {
            .el-checkbox__inner {
                border-radius: 4px;
                border-color: #d1d5db;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #3b82f6;
                }
            }

            &.is-checked {
                .el-checkbox__inner {
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    border-color: #1d4ed8;
                }
            }
        }
    }
}
