<script lang="ts" setup>
import useMaterialCategoryList from './hooks/useMaterialCategoryList'
import handleCategory from './components/handle-category.vue'

const { materialCategoryList, getMaterialCategoryList, handleAddCategory, handleCategoryRef, currentFormModel, showDialog, handleCloseDialog, handleConfirm, delMaterialInfo } =
    useMaterialCategoryList()
getMaterialCategoryList()

const emit = defineEmits(['classificationIdVal'])
const classificationId = ref('')
const nodeClickFn = (item: { id: string }) => {
    classificationId.value = item.id
}
watch(
    () => classificationId.value,
    (val) => {
        emit('classificationIdVal', val)
    },
)
const defaultProps = {
    children: 'children',
    label: 'name',
}
defineExpose({ getMaterialCategoryList }) //zrb:导出更新方法，外部调用数据更新
</script>
<template>
    <div class="category">
        <el-button class="category__button" type="primary" @click="handleAddCategory">添加分类</el-button>
        <el-tree
            :data="materialCategoryList"
            :highlight-current="true"
            :check-on-click-node="true"
            :expand-on-click-node="false"
            :props="defaultProps"
            :current-node-key="classificationId"
            node-key="id"
            @node-click="nodeClickFn"
        >
            <template #default="{ node }">
                <span class="treeText"> {{ node.label }}</span>
                <el-dropdown v-if="node.label !== '全部'" style="position: absolute; right: -10px">
                    <span style="font-weight: 600; display: block; height: 20px"> ... </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="handleAddCategory(node.data)">重命名</el-dropdown-item>
                            <el-dropdown-item style="color: red" @click="delMaterialInfo(node.data.id)">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </template>
        </el-tree>
        <el-dialog v-model="showDialog" :title="currentFormModel?.id ? '编辑分类' : '添加分类'" :width="650" destroy-on-close @close="handleCloseDialog">
            <handle-category ref="handleCategoryRef" v-model:form-model="currentFormModel" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialog = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
@include b(category) {
    padding: 0 15px 0 0;
    @include e(button) {
        margin: 50px 0 20px 0;
    }
}
@include b(treeText) {
    display: inline-block;
    flex: 1;
    // width: 135px;
    // padding-right: 5px;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
}
::v-deep .el-tree-node {
    width: 200px !important;
}
</style>
