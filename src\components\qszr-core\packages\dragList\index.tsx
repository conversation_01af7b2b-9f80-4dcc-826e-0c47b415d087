/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-22 14:47:27
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 17:01:49
 */
import { defineComponent, PropType } from 'vue'
import { VueDraggableNext } from 'vue-draggable-next'
import '../styles/m-dragList.scss'
export default defineComponent({
    props: {
        data: {
            type: Array,
            default() {
                return []
            },
        },
        secondKey: {
            type: String,
            default: '',
        },
        thirdKey: {
            type: String,
            default: '',
        },
    },
    emits: ['dragMove'],
    setup(props, { slots, emit }) {
        const emitHandle = (e) => {
            emit('dragMove', e)
        }
        return () => (
            <div class="drag__list">
                <VueDraggableNext vModel={props.data} move={(e) => emitHandle({ e, data: props.data })}>
                    {props.data.map((itemData, i) => {
                        return (
                            <el-collapse>
                                <el-collapse-item name={'level_1' + i} disabled={itemData.children && !itemData.children.length}>
                                    {{
                                        title: () => <div class="drag__list--item">{slots.item && slots.item({ childData: itemData, fLevelData: itemData, sLevelData: {}, index: i })}</div>,
                                        default: () => (
                                            <VueDraggableNext vModel={itemData.children} move={(e) => emitHandle({ e, data: itemData.children })}>
                                                {itemData.children &&
                                                    itemData.children.length &&
                                                    itemData.children.map((item, j) => {
                                                        return (
                                                            <el-collapse-item name={'level_2' + j} disabled={item.children && !item.children.length}>
                                                                {{
                                                                    title: () => (
                                                                        <div class="drag__list--item">
                                                                            {slots.item &&
                                                                                slots.item({
                                                                                    childData: item,
                                                                                    fLevelData: itemData,
                                                                                    sLevelData: item,
                                                                                    index: j,
                                                                                })}
                                                                        </div>
                                                                    ),
                                                                    default: () => (
                                                                        <VueDraggableNext vModel={item.children} move={() => emitHandle(item.children)}>
                                                                            {item.children &&
                                                                                item.children.length &&
                                                                                item.children.map((ite, k) => {
                                                                                    return (
                                                                                        <div>
                                                                                            {slots.child &&
                                                                                                slots.child({
                                                                                                    childData: ite,
                                                                                                    fLevelData: itemData,
                                                                                                    sLevelData: item,
                                                                                                    index: k,
                                                                                                })}
                                                                                        </div>
                                                                                    )
                                                                                })}
                                                                        </VueDraggableNext>
                                                                    ),
                                                                }}
                                                            </el-collapse-item>
                                                        )
                                                    })}
                                            </VueDraggableNext>
                                        ),
                                    }}
                                </el-collapse-item>
                            </el-collapse>
                        )
                    })}
                </VueDraggableNext>
            </div>
        )
    },
})
