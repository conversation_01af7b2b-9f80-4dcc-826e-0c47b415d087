/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-22 13:13:59
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-27 16:33:01
 */
import { get, post, put } from '../http'
import { CaptchaRequest } from '@components/slide-captcha/Captcha'
import { R } from '@apis/http.type'

export const getLoginPermShops = (username: string) => {
    return get({
        showLoading: false,
        url: `/gruul-mall-uaa/uaa/auth/shop/${username}`,
    })
}

/**
 * 获取滑块验证码
 */
export const doGetCaptchaSlider = () => {
    return get({ url: `gruul-mall-uaa/uaa/auth/captcha/slider` })
}

/**
 * @LastEditors: lexy
 * @description: 获取验证码
 * @param {} data
 */
export const doPostSmsCode = (data: CaptchaRequest<string>) => {
    return post({ url: `gruul-mall-uaa/uaa/auth/captcha/sms`, data })
}

export const signByUser = (data: any) => {
    return post({
        showLoading: true,
        url: '/gruul-mall-uaa/uaa/auth/oauth/token',
        data,
        // headers: {
        //     'Shop-id': '1640998223874670592',
        // },
    })
}

export const myData = () => {
    return get({
        url: 'gruul-mall-uaa/uaa/shop/admin/mine',
    })
}
/**
 * @LastEditors: lexy
 * @description: 商家中心 查询账户信息
 */
export const doGetUserDataAccount = () => {
    return get({
        url: `gruul-mall-uaa/uaa/user/data/account`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 商家中心 发送重置密码短信验证码
 */
export const doGetMyResetPasswordSms = () => {
    return get({
        url: `gruul-mall-uaa/uaa/auth/reset/my/password/sms`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 商家中心 重置密码
 * @param {string} code
 * @param {string} password
 * @param {string} confirmPassword
 * @returns {*}
 */
export const doPutMyResetPassword = (code: string, password: string, confirmPassword: string) => {
    return put({
        url: `gruul-mall-uaa/uaa/auth/reset/my/password`,
        data: {
            code,
            password,
            confirmPassword,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 重置密码 发送重置密码短信验证码
 */
export const doGetResetPasswordSms = (mobile: string) => {
    return get({
        url: `gruul-mall-uaa/uaa/auth/reset/${mobile}/password/sms`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 重置密码
 * @param {string} code
 * @param {string} password
 * @param {string} confirmPassword
 * @param {string} mobile
 * @returns {*}
 */
export const doPutResetPassword = (code: string, password: string, confirmPassword: string, mobile: string) => {
    return put({
        url: `gruul-mall-uaa/uaa/auth/reset/password`,
        data: {
            code,
            password,
            confirmPassword,
            mobile,
        },
    })
}

/**
 * 查询用户菜单导航
 */
export const doGetUserMenu = (): Promise<R<any>> => {
    return new Promise((resolve, reject) => {
        get({
            url: 'gruul-mall-uaa/uaa/menu/navigate',
        })
            .then((res: any) => {
                if (res.code !== 200) {
                    resolve(res)
                    return
                }
                const data = res.data
                const menuMap = new Map<string, Set<string>>()
                const menuConf = data.menuConfig
                for (const key in menuConf) {
                    menuMap.set(key, new Set(menuConf[key]))
                }
                data.menuConfig = menuMap
                resolve(res)
            })
            .catch((err: any) => reject(err))
    })
}

/**
 * 保存用户菜单配置
 *
 * @param data 菜单配置
 */
export const doPostSetUserMenuConfig = (data: Map<string, Set<string>>) => {
    const object = {}
    for (const [key, set] of data) {
        object[key] = [...set]
    }
    return post({
        url: 'gruul-mall-uaa/uaa/menu/navigate',
        data: object,
        showLoading: false,
    })
}
