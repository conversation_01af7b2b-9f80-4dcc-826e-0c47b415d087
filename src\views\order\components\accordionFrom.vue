<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-09 18:42:45
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-17 14:53:57
-->
<script setup lang="ts">
import { ref, reactive, defineEmits, watch, PropType, onMounted } from 'vue'
import MCard from '@/components/MCard.vue'
// /*
//  *variable
//  */
const $route = useRoute()
const $emit = defineEmits(['search-data', 'searchChange', 'exportData'])
const ShowMCard = ref(false)
// 下拉选择状态初始数据
const SearchFromData = reactive({
    no: $route.query.orderNo, // 订单号
    buyerNickname: '', // 买家昵称
    clinchTime: '', // 时间段
    productName: '', // 商品名称
    receiverName: '', // 收货人姓名
    platform: '', //所属渠道
})
/*
 *lifeCircle
 */
/*
 *function
 */

const props = defineProps({
    show: {
        type: <PERSON>olean,
        default: true,
    },
})
onMounted(() => {
    if ($route.query.orderNo) {
        showMCardChange()
    }
})
watch(
    () => ShowMCard.value,
    (val) => {
        $emit('searchChange', val)
    },
)
function showMCardChange() {
    setTimeout(() => {
        ShowMCard.value = true
    }, 500)
}
const HandleSearch = () => {
    const { no, buyerNickname, productName, receiverName, platform } = SearchFromData
    const params = {
        no,
        buyerNickname,
        productName,
        receiverName,
        startTime: '',
        endTime: '',
        platform,
    }
    if (Array.isArray(SearchFromData.clinchTime)) {
        params.startTime = SearchFromData.clinchTime[0]
        params.endTime = SearchFromData.clinchTime[1]
    }
    $emit('search-data', params)
}
const handleReset = () => {
    Object.keys(SearchFromData).forEach((key) => (SearchFromData[key] = ''))
    HandleSearch()
}

const platformList = [
    {
        label: '全部',
        value: '',
    },
    {
        label: '小程序',
        value: 'WECHAT_MINI_APP',
    },
    // {
    //     label: '公众号',
    //     value: 'WECHAT_MP',
    // },
    {
        label: 'H5商城',
        value: 'H5',
    },
    {
        label: 'IOS端',
        value: 'IOS',
    },
    {
        label: '安卓端',
        value: 'ANDROID',
    },
    {
        label: '鸿蒙端',
        value: 'HARMONY',
    },
    {
        label: 'PC商城',
        value: 'PC',
    },
]
const exportData = () => {
    $emit('exportData', SearchFromData)
}
</script>

<template>
    <!-- 搜索部分s -->
    <div class="form">
        <MCard v-model="ShowMCard">
            <el-form :model="SearchFromData">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称" label-width="90px">
                            <el-input v-model="SearchFromData.productName" placeholder="请输入商品名称" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="买家昵称" label-width="90px">
                            <el-input v-model="SearchFromData.buyerNickname" placeholder="请输入买家昵称" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="订单号" label-width="90px">
                            <el-input v-model="SearchFromData.no" placeholder="请输入订单号" maxlength="25" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="收货人姓名" label-width="90px">
                            <el-input v-model="SearchFromData.receiverName" placeholder="请输入收货人姓名" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="成交时间" label-width="90px">
                            <el-date-picker
                                v-model="SearchFromData.clinchTime"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                            /> </el-form-item
                    ></el-col>
                    <el-col v-if="!props.show" :span="8">
                        <el-form-item label="所属渠道" label-width="90px">
                            <el-select v-model="SearchFromData.platform" style="width: 100%">
                                <el-option v-for="(item, index) in platformList" :key="index" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label-width="90px">
                    <el-button class="from_btn" type="primary" round @click="HandleSearch">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                    <el-button type="primary" round @click="exportData">导出</el-button>
                </el-form-item>
            </el-form>
        </MCard>
    </div>
    <!-- 搜索部分e -->
</template>

<style lang="scss" scoped>
@include b(form) {
    background: #f9f9f9;
}
</style>
