/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-27 10:13:10
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-09 17:46:02
 */
import { dayjs, ElMessage } from 'element-plus'
import { get, put, post } from '../http'
import downloadRequest from '@/apis/download-request'
import { FullScreenLoadingHelper } from '@/libs/Loading'
/**
 * @LastEditors: lexy
 * @description: 物流轨迹
 * @returns {*}
 */
export const doGetLogisticsDetails = () => {
    return get({
        url: 'gruul-mall-freight/logistics/node?companyCode=shengfeng&waybillNo=SF1357038560440',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取订单列表 usePackage 是否根据
 * @param usePackage true 按包裹查询
 * @param packageId 为空查询未发货 否则查询已发货
 * @returns {*}
 */
export const doGetOrderList = (params, orderNo?: string) => {
    if (orderNo) {
        return get({ url: `gruul-mall-order/order/${orderNo}`, params })
    }
    return get({ url: `gruul-mall-order/order`, params })
}
/**
 * @LastEditors: lexy
 * @description: 通过orderNo获取订单详情
 * @param {*} orderId
 */
export const doGetOrderDetails = (orderNo: string, params: any) => {
    return get({ url: `/gruul-mall-order/order/${orderNo}`, params })
}
/**
 * @LastEditors: lexy
 * @description: 通过orderNo获取未发货订单
 * @param {string} orderNo
 * @param {string} shopOrderNo
 * @returns {*}
 */
export const dogetOrderNotDetailsByOrderNo = (orderNo: string, shopOrderNo: string) => {
    return get({ url: `/gruul-mall-order/order/${orderNo}/shopOrder/${shopOrderNo}/undelivered` })
}
/**
 * @LastEditors: lexy
 * @description: 查询所有已发货的商品
 * @param {string} orderNo
 * @param {string} shopOrderNo
 * @returns {*}
 */
export const doGetDeliveryPageAll = (orderNo: string, shopOrderNo: string) => {
    return get({ url: `gruul-mall-order/order/${orderNo}/shopOrder/${shopOrderNo}/delivered` })
}
/**
 * @LastEditors: lexy
 * @description: 商品发货
 * @param {string} orderNo
 * @returns {*}
 */
export const doPutOrderDetailsByOrderNo = (orderNo: string, data) => {
    return put({ url: `/gruul-mall-order/order/${orderNo}/deliver`, data })
}
/**
 * @LastEditors: lexy
 * @description: 商品批量发货
 * @param {string} orderNo
 * @returns {*}
 */
export const doPostOrderBatchDeliver = (data) => {
    return put({ url: `gruul-mall-order/order/batch/deliver`, data })
}
/**
 * @LastEditors: lexy
 * @description: 关闭店铺订单
 * @param {string} orderNo
 * @param {string} shopOrderNo
 * @returns {*}
 */
export const doPutCloseShopOrder = (orderNo: string, shopOrderNo: string) => {
    return put({ url: `gruul-mall-order/order/${orderNo}/shopOrder/${shopOrderNo}/close` })
}
/**
 * @description 获取平台发货配置信息
 * @returns
 */
export const doGetShopDeliveryConfig = () => {
    return get({ url: '/gruul-mall-shop/shop/deliver' })
}
/**
 * @LastEditors: lexy
 * @description: 商品下载电子面单
 * @returns {*}
 */
export const doPostOrderBatchDownloadPrinter = () => {
    return post({ url: `/gruul-mall-order/order/exportDelivery` })
}
