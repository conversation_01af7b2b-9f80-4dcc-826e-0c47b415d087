const usePreviewExamineDetails = () => {
    const previewVisible = ref(false)
    const commodityInfo = reactive({
        id: '',
        shopId: '',
        explain: '',
        auditStatus: '',
    })
    const handlePreviewExamineDetails = (row: any) => {
        commodityInfo.id = row?.id
        commodityInfo.shopId = row?.shopId
        commodityInfo.explain = row?.explain
        commodityInfo.auditStatus = row?.auditStatus
        previewVisible.value = true
    }
    return {
        previewVisible,
        commodityInfo,
        handlePreviewExamineDetails,
    }
}

export default usePreviewExamineDetails
