<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-02-07 13:42:06
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-22 10:56:58
-->
<template>
    <el-dialog v-model="isShow" class="sign-dialog" :width="dialogWidth * props.scale" :show-close="false">
        <div id="captcha-box"></div>
    </el-dialog>
</template>

<script setup lang="ts">
import { PropType, watch } from 'vue'
import { CaptchaRequest } from './Captcha'
import { useVModel } from '@vueuse/core'
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    scale: {
        type: Number,
        default: 2,
    },
    smsType: {
        type: String,
        default: null,
    },
    getForm: {
        type: Function as PropType<() => any>,
        required: true,
    },
    doSubmit: {
        type: Function as PropType<(request: CaptchaRequest<any>) => Promise<any>>,
        required: true,
    },
})
const emits = defineEmits(['update:modelValue', 'success'])
const isShow = useVModel(props, 'modelValue', emits)
// 弹窗宽度
const dialogWidth = ref<number>(0)

const refresh = async () => {
    const config = {
        // 获取接口
        requestCaptchaDataUrl: import.meta.env.VITE_BASE_URL + 'gruul-mall-uaa/uaa/auth/captcha/slider',
        // 验证接口
        validCaptchaUrl: import.meta.env.VITE_BASE_URL + 'gruul-mall-uaa/uaa/auth/captcha/check',
        // 验证码绑定的div块 (必选项,必须配置)
        bindEl: '#captcha-box',
        // 验证成功回调函数(必选项,必须配置)
        validSuccess: ({ data }, c, tac) => {
            console.log('验证成功，后端返回的数据为', data)
            // 调用具体的login方法
            if (data?.id) {
                props.doSubmit(HavesmsType(data.id)).then((response) => {
                    // 销毁验证码服务
                    tac.destroyWindow()
                    isShow.value = false

                    const { code, msg } = response
                    if (code === 200) {
                        emits('success', response)
                        return
                    }
                    msg && ElMessage.error(msg)
                    refresh()
                    return
                })
            }
        },
        // 验证失败的回调函数(可忽略，如果不自定义 validFail 方法时，会使用默认的)
        validFail: (res, c, tac) => {
            console.log('验证码验证失败回调...')
            // 验证失败后重新拉取验证码
            tac.reloadCaptcha()
        },
        // 刷新按钮回调事件
        btnRefreshFun: (el, tac) => {
            console.log('刷新按钮触发事件...')
            tac.reloadCaptcha()
        },
        // 关闭按钮回调事件
        btnCloseFun: (el, tac) => {
            console.log('关闭按钮触发事件...')
            isShow.value = false
            tac.destroyWindow()
        },
    }
    const style = {
        // logo地址
        logoUrl: import.meta.env.VITE_RESUME_LOGO,
    }

    window
        .initTAC('./tac', config, style)
        .then((tac) => {
            tac.init() // 调用init则显示验证码
            const tacTemp = Object.assign({}, tac)
            if (tacTemp.config.domBindEl.dom) {
                const { width } = tacTemp.config.domBindEl.dom.firstElementChild.getBoundingClientRect()

                if (width) {
                    dialogWidth.value = width
                }
            }
        })
        .catch((e) => {
            console.log('初始化tac失败', e)
        })
}
watch(
    () => isShow.value,
    (value) => {
        if (value) {
            nextTick(() => refresh())
        }
    },
)

const HavesmsType = (id: string) => {
    if (props.smsType) {
        return {
            mobile: props.getForm(),
            id,
            smsType: props.smsType,
        } as CaptchaRequest<any>
    }
    return {
        mobile: props.getForm(),
        id,
    } as CaptchaRequest<any>
}
</script>

<style scoped lang="scss"></style>
<style>
.sign-dialog {
    padding: 0 !important;
}
.sign-dialog .el-dialog__header {
    padding: 0 !important;
    margin: 0 !important;
}
.sign-dialog .el-dialog__body {
    padding: 0 !important;
}
</style>
