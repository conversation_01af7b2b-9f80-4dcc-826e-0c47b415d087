/**
 * 物流通知管理相关类型定义
 */

// API 响应根对象
export interface RootObject {
    code: number
    msg: string
    data: Data
}

// 分页数据结构
export interface Data {
    pages: number
    records: Records[]
    total: number
    size: number
    current: number
}

// 物流通知记录
export interface Records {
    id: number
    createTime: string
    updateTime: string
    version: number
    deleted: boolean
    supplierId: number
    itemId: number
    orderNo: string
    orderTime: string
    productName: string
    productSpecs?: string[]  // 商品规格
    productNum?: number      // 商品数量
    expressCompanyName: string
    expressCompanyCode: string
    expressNo: number
    receiverMobile: string
    deliveryTime: string
    afsStatus: string
    noticeType: string
    noticeStatus: string
    noticeTime: string
    dealTime: string
    dealName: string
    dealStatus: string
    supplierName: string
}

// 搜索参数
export interface SearchParams {
    orderNo?: string
    supplierName?: string
    productName?: string
    dealName?: string
    handler?: string
    noticeType?: string
    afsStatus?: string
    orderTimeStart?: string
    orderTimeEnd?: string
    transactionTimeStart?: string
    transactionTimeEnd?: string
    deliveryTimeStart?: string
    deliveryTimeEnd?: string
    shipmentTimeStart?: string
    shipmentTimeEnd?: string
    noticeTimeStart?: string
    noticeTimeEnd?: string
    dealTimeStart?: string
    dealTimeEnd?: string
}

// 分页参数
export interface PageParams {
    current: number
    size: number
}

// 完整的查询参数（搜索参数 + 分页参数）
export interface QueryParams extends SearchParams, PageParams { }

// 通知状态枚举
export enum NoticeStatus {
    AWAITING_NOTICE = 'AWAITING_NOTICE',
    NOTICE_SUCCESS = 'NOTICE_SUCCESS',
    NOTICE_FAILED = 'NOTICE_FAILED',
}

// 处理状态枚举
export enum DealStatus {
    AWAITING_PROCESSING = 'AWAITING_PROCESSING',
    PROCESSED = 'PROCESSED',
    PROCESSING_FAILED = 'PROCESSING_FAILED',
}

// 通知类型枚举
export enum NoticeType {
    PENDING_SHIPMENT = 'PENDING_SHIPMENT',
    PENDING_PICKUP = 'PENDING_PICKUP',
}

// AFS状态枚举
export enum AfsStatus {
    NONE = 'NONE',
}

// 状态标签类型映射
export interface StatusTypeMap {
    [key: string]: 'success' | 'warning' | 'danger' | 'info'
}

// 状态文本映射
export interface StatusTextMap {
    [key: string]: string
}

// 处理请求参数
export interface ProcessParams {
    dealName: string
    dealRemark?: string
}

// 批量处理参数
export interface BatchProcessParams {
    ids: number[]
}
