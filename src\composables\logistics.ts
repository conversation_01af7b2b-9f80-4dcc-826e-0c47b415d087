/*
 * @description: 包裹订单状态
 * @Author: lexy
 * @Date: 2023-01-09 15:48:58
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-09 18:06:27
 */
import type { ApiOrder } from '@/views/order/types/order'
import type { DeliverType } from '@/views/order/orderShipment/types'
/**
 * @LastEditors: lexy
 * @description:
 * @param {ApiOrder} order
 * @returns {*}
 */
export function orderStatusParcel(order: ApiOrder) {
    if (order.status === 'PAID') {
        // 已支付
        return orderPaidStatus(order)
    } else if (order.status === 'UNPAID') {
        // 待支付
        return 'UNPAID'
    } else {
        // 卖家关闭 || 商家关闭 || 超时系统关闭
        return order.status
    }
}
/**
 * @LastEditors: lexy
 * @description: 已支付订单状态
 * @returns {*}
 */
function orderPaidStatus(order: ApiOrder) {
    const ordersIsOk = order.shopOrders.every((item) => item.status === 'OK')
    if (ordersIsOk) {
        // 订单状态正常 查看包裹状态 拆分包裹 包裹信息应一致
        return order.shopOrders[0].shopOrderItems[0].packageStatus
    } else {
        // 订单异常
        // 返回当前的订单状态
        // 商家端 shopOrders 只有一项 所以直接返回索引 0 即可
        return order.shopOrders.map((item) => item.status)[0]
    }
}
/**
 * @LastEditors: lexy
 * @description: 是无需物流发货
 * @param {keyof} type
 * @returns {*}
 */
export function isWithout(type: keyof typeof DeliverType) {
    return type === 'WITHOUT'
}
