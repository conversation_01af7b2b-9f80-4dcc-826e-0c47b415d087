<template>
  <div class="q-btn-image" @mouseenter="showOverlay = true" @mouseleave="showOverlay = false">
    <el-image v-bind="$attrs" :src="src" :style="imageStyle" ref="imageRef">
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </el-image>
    <div v-if="hasOverlaySlots && showOverlay" class="image-overlay">
      <div class="overlay-content" :class="{ 'two-slots': hasTwoSlots }">
        <slot name="preview" :openViewer="openViewer"></slot>
        <template v-if="!disabled">
          <slot name="first"></slot>
          <slot name="second"></slot>
        </template>
      </div>
    </div>
    <el-image-viewer v-if="showViewer" :url-list="[src]" @close="closeViewer" :teleported="true" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, useSlots } from 'vue'
import type { ImageProps } from 'element-plus'
import { ElImageViewer } from 'element-plus'

interface QBtnImageProps extends ImageProps {
  width?: string
  height?: string
  fit?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<QBtnImageProps>(), {
  fit: 'cover',
  hideOnClickModal: false,
  lazy: true,
  previewTeleported: true,
  initialIndex: 0,
  maxScale: 7,
  minScale: 0.2,
  infinite: true,
  closeOnPressEscape: true,
  zoomRate: 1.2,
  zIndex: 2000,
})

const emit = defineEmits(['click'])

const slots = useSlots()
const imageRef = ref(null)
const showViewer = ref(false)

const hasOverlaySlots = computed(() => Object.keys(slots).length > 0)
const hasTwoSlots = computed(() => Object.keys(slots).length === 2)
const src = computed(() => props.src)
const showOverlay = ref(false)

const imageStyle = computed(() => ({
  width: props.width || '100%',
  height: props.height || 'auto',
  objectFit: props.fit || 'cover'
}))

const openViewer = () => {
  showViewer.value = true
}

const closeViewer = () => {
  showViewer.value = false
}

</script>

<style lang="scss" scoped>
.q-btn-image {
  position: relative;
  display: inline-block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease;
}

.overlay-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  &.two-slots {
    justify-content: space-around;
  }
}
</style>