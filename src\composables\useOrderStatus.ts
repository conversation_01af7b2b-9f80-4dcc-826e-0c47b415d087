/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-30 15:30:31
 * @LastEditors: lexy
 * @LastEditTime: 2023-04-24 15:52:15
 */
import { QUERYORDERSTATUS, ORDERSTATUS, SHOPORDERSTATUS, PACKAGESTATUS, SHOPITEMSTATUS, ApiOrder, ShopOrderItem, DISTRIBUTION } from '@/views/order/types/order'
const queryStatus: Record<keyof typeof QUERYORDERSTATUS, string> = {
    UNPAID: '待支付',
    UN_DELIVERY: '待发货',
    UN_RECEIVE: '待收货',
    COMPLETED: '已完成',
    CLOSED: '已关闭',
}
const orderStatus: Record<keyof typeof ORDERSTATUS, string> = {
    UNPAID: '未支付',
    PAID: '已支付',
    BUYER_CLOSED: '买家关闭订单',
    SYSTEM_CLOSED: '超时未支付 系统关闭',
    SELLER_CLOSED: '卖家关闭订单',
    TEAMING: '拼团中',
    TEAM_FAIL: '拼团失败',
}
const shopOrderStatus: Record<keyof typeof SHOPORDERSTATUS, string> = {
    OK: '正常状态',
    SYSTEM_CLOSED: '系统关闭',
    BUYER_CLOSED: '买家关闭订单',
    SELLER_CLOSED: '卖家关闭订单',
}
const distributionModeStatus: Record<keyof typeof DISTRIBUTION, string> = {
    MERCHANT: '快递配送',
    EXPRESS: '快递配送',
    INTRA_CITY_DISTRIBUTION: '同城配送',
    SHOP_STORE: '门店自提',
    VIRTUAL: '无需物流',
}
const shopItemsStatus: Record<keyof typeof SHOPITEMSTATUS, string> = {
    OK: '确认',
    CLOSED: '取消',
}
const packageStatus: Record<keyof typeof PACKAGESTATUS, string> = {
    WAITING_FOR_DELIVER: '未发货',
    WAITING_FOR_RECEIVE: '待收货',
    BUYER_WAITING_FOR_COMMENT: '待评价',
    SYSTEM_WAITING_FOR_COMMENT: '待评价',
    BUYER_COMMENTED_COMPLETED: '已完成',
    SYSTEM_COMMENTED_COMPLETED: '已完成',
}
/**
 * @LastEditors: lexy
 * @description: 状态转中文
 */
/**
 * @LastEditors: lexy
 * @description: 订单状态
 * @param {keyof} str
 * @returns {*}
 */
const getOrdercn = (order: ApiOrder) => {
    if (order.status !== 'PAID') {
        if (order.status === 'UNPAID' && order.shopOrders[0].status !== 'OK') {
            // 未支付并且店铺订单异常
            return orderStatus[order.status] + `(已关闭)`
        }
        return orderStatus[order.status]
    }
    if (order.shopOrders[0].status !== 'OK') return shopOrderStatus[order.shopOrders[0].status]
    const shopOrderItems = order.shopOrders[0].shopOrderItems
    const closedNum = shopOrderItems.filter((order) => order.status !== 'OK').length
    const delivery = shopOrderItems.filter((order) => order.packageStatus !== 'WAITING_FOR_DELIVER').length
    if (closedNum === shopOrderItems.length) return '交易失败'
    if (closedNum + delivery === shopOrderItems.length) return '已发货'
    return delivery === 0 ? '待发货' : '部分发货'
}

/**
 * 获取已拆分的包裹状态信息
 */
const getSplitOrderStatus = ({ status, shopOrders }: ApiOrder, shopOrderItems: ShopOrderItem[]) => {
    const check = {
        desc: '', //描述
        canDelivery: false, //是否可发货
    }
    const { status: shopStatus } = shopOrders[0]
    if (status !== 'PAID') {
        check.desc = orderStatus[status] + (status === 'UNPAID' && shopStatus !== 'OK' ? '（已关闭）' : '')
        return check
    }
    if (shopStatus !== 'OK') {
        check.desc = shopOrderStatus[shopStatus]
        return check
    }
    const shopOrderItem = shopOrderItems.find((order) => order.status === 'OK')
    if (!shopOrderItem) {
        check.desc = '交易失败'
        return check
    }
    const itemPackageStatus = shopOrderItem.packageStatus
    check.desc = packageStatus[itemPackageStatus]
    check.canDelivery = itemPackageStatus === 'WAITING_FOR_DELIVER'
    return check
}

/**
 * @LastEditors: lexy
 * @description: 判断当条订单数据是否为已拆分数据
 */
export function isSplitOrder(data: ApiOrder) {
    const shopOrderItems = data.shopOrders[0].shopOrderItems
    const packageId = shopOrderItems[0].packageId
    // 只要有发货的商品就拆分 且 商品不止一个
    const breakUp = shopOrderItems.some((order) => order.packageStatus !== 'WAITING_FOR_DELIVER') && shopOrderItems.length > 1
    // 都有packageId 且id一致 说明同一个包裹
    const same = shopOrderItems.every((item) => packageId && item.packageId === packageId)
    //有发货的商品 且  商品不止一个 且 不是同一个包裹 就拆分
    return breakUp && !same
}
/**
 * @LastEditors: lexy
 * @description: 商品状态
 * @param {keyof} str
 * @returns {*}
 */
const getShopcn = (str: keyof typeof SHOPORDERSTATUS) => {
    return shopOrderStatus[str]
}
/**
 * @LastEditors: lexy
 * @description: tab状态
 * @param {keyof} str
 * @returns {*}
 */
const getDeliverycn = (str: keyof typeof queryStatus) => {
    return queryStatus[str]
}
/**
 * @LastEditors: lexy
 * @description: 是否发货状态
 * @returns {*}
 */
const isSendGoods = (str: keyof typeof ORDERSTATUS) => {
    return str !== 'PAID'
}
/**
 * @LastEditors: lexy
 * @description: TabsName
 * @returns {*}
 */
const TabsName = ['近一个月订单', '近三个月订单', '全部订单']

export { queryStatus, orderStatus, shopOrderStatus, packageStatus, getSplitOrderStatus, getShopcn, getDeliverycn, isSendGoods, TabsName, distributionModeStatus }
