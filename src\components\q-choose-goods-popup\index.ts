/*
 * @description:
 * @Author: lexy
 * @Date: 2023-03-14 13:50:35
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-29 10:16:53
 */
import type { GoodsListItem } from '@/components/q-choose-goods-popup/types'

const { divTenThousand } = useConvert()
/**
 * @LastEditors: lexy
 * @description: 商品价格格式化
 * @param {GoodsListItem} goods
 * @returns {*}
 */
export function formatGoodsPrice(goods: GoodsListItem) {
    // 获取商品最高价格
    const highestPrice = divTenThousand(goods.highestPrice)
    // 获取商品最低价格
    const lowestPrice = divTenThousand(goods.lowestPrice)
    // 返回商品价格
    return `${lowestPrice}~${highestPrice}`
}
