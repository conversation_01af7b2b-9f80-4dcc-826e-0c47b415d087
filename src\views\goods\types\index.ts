/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-04 15:33:07
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-30 13:23:27
 */
import { WeightTypeEnum } from '../releaseCommodity/types'
enum Services {
    NO_FREIGHT,
    SEVEN_END_BACK,
    TWO_DAY_SEND,
    FAKE_COMPENSATE,
    ALL_ENSURE,
}
export enum Limit {
    UNLIMITED,
    PRODUCT_LIMITED,
    SKU_LIMITED,
}
export enum Stock {
    UNLIMITED,
    LIMITED,
}
/**
 * @LastEditors: lexy
 * @description: 商品基本信息部分类型
 * @param platformCategoryId 平台分类id
 * @param categoryId 店铺分类id
 * @param providerId 供应商id
 */
export interface CommodityBasicType {
    name: string
    saleDescribe: string
    platformCategoryId: string
    categoryId: string
    providerId: string
    attributeId: string
    videoUrl: string
    albumPics: string
    freightTemplateId: number
    pic: string
    shopId: string
}
/**
 * @LastEditors: lexy
 * @description: 商品规格组
 * @param {string} inputValue 规格值
 * @param {string} name 规格名称
 */
export interface CommoditySpecGroup {
    inputValue: string
    inputVisble: boolean
    name: string
    children: CommoditySpecGroup[]
}
/**
 * @LastEditors: lexy
 * @description: 商品规格信息
 */
interface CommoditySpecInfo {
    image: string
    price: string | number
    commission: string | number
    salePrice: string | number
    initSalesVolume: number
    limitNum: number
    weight: number
    weightType: keyof typeof WeightTypeEnum
    limitType: keyof typeof Limit
    specs?: string[]
    stockType: keyof typeof Stock
}
/**
 * @LastEditors: lexy
 * @description: 商品销售信息部分类型
 * @param price 划线价
 * @param salePrice 销售价
 * @param initSalesVolume 初始销量
 * @param serviceIds 服务保障
 */
export interface CommoditySaleType {
    serviceIds: (keyof typeof Services)[]
    specGroups: CommoditySpecGroup[]
    skus: CommoditySpecTable[]
}
export interface CommoditySpecTable extends CommoditySpecInfo {
    id: string
    stockType: keyof typeof Stock
    stock: string
    num: number
    salesVolume: string
    providerId: string
    productId: string
}
interface CommodityInfoType {
    detail: string
}
/**
 * @LastEditors: lexy
 * @description: 新增商品信息类型总和
 */
export interface SubmitCommodityType extends CommodityBasicType, CommoditySpecInfo, CommoditySaleType, CommodityInfoType {
    id: string
}

/**
 * @LastEditors: lexy
 * @description: 供应商列表类型
 */
export interface ApiSupplierType {
    address: string
    area: string
    city: string
    country: string
    id: string
    mobile: string
    name: string
    productInfo: string
    province: string
    status: string
}
/**
 * @LastEditors: lexy
 * @description: 商品属性
 * @param {string} content 属性值
 * @param {string} name 属性值名称
 * @param {string} parentId 父级iD
 */
export type CommodityAttribute = Record<'content' | 'id' | 'name' | 'parentId', string>
export interface ApiCommodityType extends CommodityBasicType, CommoditySpecInfo, CommoditySaleType {
    [key: string]: any
    id: string
    status: string
    shopId: string
    providerName: string
    storageSkus: CommoditySpecTable[]
}

export enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}

export enum ExamineGoodsEnum {
    ALREADY_PASSED = '已通过',
    UNDER_REVIEW = '审核中',
    REFUSE = '已拒绝',
}
